base-path: /workspace

debug: true

logging:
  level:
    root: warn
    com.dao42.paas: debug
    com.github.dockerjava: info
    jdbc:
      sqlonly: info
      resultsettable: off

# SQL日志
log4jdbc:
  dump:
    sql:
      insert: true
      delete: true
      update: true
      select: false
      addsemicolon: true
      maxlinelength: 500

# 性能诊断
management:
  endpoints:
    web:
      exposure:
        include: [ metrics, prometheus ]
  metrics:
    tags:
      application: ${spring.application.name}
    web:
      server:
        request:
          autotime:
            enabled: true

server:
  port: 8080
  tomcat:
    threads:
      max: 200
      min-spare: 100
    accept-count: 500
    max-connections: 10000
    connection-timeout: 10s
    keep-alive-timeout: 10s
    max-keep-alive-requests: 100
    mbeanregistry:
      enabled: true

spring:
  jpa:
    hibernate:
      ddl-auto: update
  datasource:
    url: ********************************************************************************************************************************************
    username: root
    password: rd123456
    hikari:
      connection-timeout: 10000
      idle-timeout: 600000
      max-lifetime: 1800000
      minimum-idle: 10
      maximum-pool-size: 10
      connection-test-query: 'SELECT 1'
      register-mbeans: true
  redis:
    host: redis
    port: 6379
    password:
    timeout: 5000
    database: 1
  rabbitmq:
    host: rabbitmq
    port: 5672
    username: agent
    password: d42agent
    virtual-host: dev
    listener:
      simple:
        acknowledge-mode: auto
        concurrency: 1
        max-concurrency: 2

kong:
  domain:
    admin: http://kong:8001
    app: http://localhost:5080
    lsp: http://localhost:54420

system:
  test-env: true
  local-debug: true
  token-validate: false
  btrfs:
    host: localhost
    port: 22
    userName: root
    keyPath: '/root/.ssh/id_ed25519'
    create-cmd: cp -RP {source} {target}
    remove-cmd: rm -rf {target}
  docker:
    sock-connect: false
    prefix-name: paas-devcontainer
    agent:
      host-path: ${base-path}/d42paas_agent
      docker-path: /agent
    storage:
      nfs-path: ${base-path}/dockerContainer
      file-server-path: ${base-path}/dockerContainer
    gpt:
      gpt-id: claky_encryptKey
      gpt-key: encrypted key
      proxy-url: encrypted proxy-url
      auth-token: encrypted auth-token
    paas-gateway-url: https://paas-gateway.develop.clackypaas.com

  redis-rate-limter:
    clone-rate: 5

  playground:
    ticket-secret: 1
    destroy-days: 7
    inactive-seconds:
      code-zone: 194
      code-zone-copy: 194
      code-zone-snapshot: 194
  code-zone:
    meta: ${base-path}/codeZone/data/@meta
    env-path: ${base-path}/environment
    path: ${base-path}/codeZone
    path-in-btrfs: /data/@data/codeZone
    snapshot-path: ${base-path}/codeZoneSnapshot
    snapshot-path-in-btrfs: /data/@data/codeZoneSnapshot
    blocked-files: .1024feature;.1024feature-file;.breakpoint
    default-expire-days: 30
    max-delete-rate-per-second: 10

xxl:
  job:
    enable: true
    admin:
      addresses: https://xxl-job-admin.develop.clackypaas.com/xxl-job-admin
    accessToken: default_token
    executor:
      appname: clacky-paas-executor
      address:
      ip:
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30 