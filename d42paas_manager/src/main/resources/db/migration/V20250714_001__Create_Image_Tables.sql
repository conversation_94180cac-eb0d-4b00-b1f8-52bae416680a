-- Thread镜像管理表
CREATE TABLE thread_image (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_id BIGINT NOT NULL COMMENT '项目ID',
    issue_id BIGINT COMMENT 'Issue ID (可为空，Root Thread没有Issue ID)',
    container_type VARCHAR(20) NOT NULL COMMENT '容器类型: ROOT_THREAD, ISSUE_THREAD',
    image_tag VARCHAR(255) NOT NULL COMMENT '镜像标签',
    image_digest VARCHAR(255) COMMENT '镜像摘要',
    image_size BIGINT COMMENT '镜像大小 (字节)',
    parent_image_tag VARCHAR(255) COMMENT '父镜像标签',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    status VARCHAR(20) NOT NULL COMMENT '状态: BUILDING, READY, PUSHING, AVAILABLE, FAILED',
    build_log LONGTEXT COMMENT '构建日志',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    last_used_at DATETIME COMMENT '最后使用时间',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间',
    deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    
    INDEX idx_project_issue (project_id, issue_id),
    INDEX idx_project_type (project_id, container_type),
    INDEX idx_user_id (user_id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_last_used_at (last_used_at)
) COMMENT 'Thread镜像管理表';

-- 镜像缓存表
CREATE TABLE image_cache (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    docker_server_id BIGINT NOT NULL COMMENT 'Docker服务器ID',
    image_tag VARCHAR(255) NOT NULL COMMENT '镜像标签',
    image_digest VARCHAR(255) COMMENT '镜像摘要',
    cache_size BIGINT COMMENT '缓存大小 (字节)',
    status VARCHAR(20) NOT NULL COMMENT '缓存状态: PULLING, AVAILABLE, FAILED',
    hit_count INT DEFAULT 0 COMMENT '命中次数',
    last_hit_at DATETIME COMMENT '最后命中时间',
    cached_at DATETIME NOT NULL COMMENT '缓存时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间',
    deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    
    UNIQUE KEY uk_server_image (docker_server_id, image_tag),
    INDEX idx_docker_server_id (docker_server_id),
    INDEX idx_image_tag (image_tag),
    INDEX idx_status (status),
    INDEX idx_hit_count (hit_count),
    INDEX idx_last_hit_at (last_hit_at),
    INDEX idx_cached_at (cached_at)
) COMMENT '镜像缓存表';

-- @meta环境版本表
CREATE TABLE meta_environment (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '环境名称',
    version VARCHAR(50) NOT NULL COMMENT '环境版本',
    s3_path VARCHAR(500) NOT NULL COMMENT 'S3存储路径',
    local_cache_path VARCHAR(500) COMMENT '本地缓存路径',
    size_bytes BIGINT COMMENT '环境大小 (字节)',
    file_count INT COMMENT '文件数量',
    status VARCHAR(20) NOT NULL COMMENT '状态: SYNCING, AVAILABLE, FAILED, EXPIRED',
    description VARCHAR(1000) COMMENT '描述',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认环境',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间',
    last_synced_at DATETIME COMMENT '最后同步时间',
    deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    
    UNIQUE KEY uk_name_version (name, version),
    INDEX idx_name (name),
    INDEX idx_status (status),
    INDEX idx_is_default (is_default),
    INDEX idx_last_synced_at (last_synced_at)
) COMMENT '@meta环境版本表';

-- 用户镜像配额表
CREATE TABLE user_image_quota (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    user_tier VARCHAR(20) NOT NULL COMMENT '用户级别: FREE, BASIC, PRO, ENTERPRISE',
    max_images INT NOT NULL COMMENT '最大镜像数量',
    max_storage_gb INT NOT NULL COMMENT '最大存储空间 (GB)',
    current_images INT DEFAULT 0 COMMENT '当前镜像数量',
    current_storage_gb DECIMAL(10,2) DEFAULT 0 COMMENT '当前存储空间 (GB)',
    quota_reset_at DATETIME COMMENT '配额重置时间',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    updated_at DATETIME NOT NULL COMMENT '更新时间',
    deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_user_tier (user_tier),
    INDEX idx_quota_reset_at (quota_reset_at)
) COMMENT '用户镜像配额表';

-- 镜像构建历史表
CREATE TABLE image_build_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    thread_image_id BIGINT NOT NULL COMMENT 'Thread镜像ID',
    container_id VARCHAR(100) NOT NULL COMMENT '源容器ID',
    build_status VARCHAR(20) NOT NULL COMMENT '构建状态: BUILDING, SUCCESS, FAILED',
    build_start_time DATETIME NOT NULL COMMENT '构建开始时间',
    build_end_time DATETIME COMMENT '构建结束时间',
    build_duration_ms BIGINT COMMENT '构建耗时 (毫秒)',
    build_log LONGTEXT COMMENT '构建日志',
    error_message TEXT COMMENT '错误消息',
    image_size_bytes BIGINT COMMENT '镜像大小 (字节)',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    
    INDEX idx_thread_image_id (thread_image_id),
    INDEX idx_container_id (container_id),
    INDEX idx_build_status (build_status),
    INDEX idx_build_start_time (build_start_time),
    FOREIGN KEY (thread_image_id) REFERENCES thread_image(id)
) COMMENT '镜像构建历史表';

-- 容器启动性能统计表
CREATE TABLE container_startup_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    container_id VARCHAR(100) NOT NULL COMMENT '容器ID',
    docker_server_id BIGINT NOT NULL COMMENT 'Docker服务器ID',
    image_tag VARCHAR(255) NOT NULL COMMENT '使用的镜像标签',
    used_cached_image BOOLEAN DEFAULT FALSE COMMENT '是否使用了缓存镜像',
    meta_environment_injected BOOLEAN DEFAULT FALSE COMMENT '是否注入了@meta环境',
    startup_time_ms BIGINT NOT NULL COMMENT '总启动时间 (毫秒)',
    server_selection_ms BIGINT COMMENT '服务器选择耗时 (毫秒)',
    image_selection_ms BIGINT COMMENT '镜像选择耗时 (毫秒)',
    image_pull_ms BIGINT COMMENT '镜像拉取耗时 (毫秒)',
    meta_injection_ms BIGINT COMMENT '@meta环境注入耗时 (毫秒)',
    container_creation_ms BIGINT COMMENT '容器创建耗时 (毫秒)',
    container_start_ms BIGINT COMMENT '容器启动耗时 (毫秒)',
    startup_success BOOLEAN NOT NULL COMMENT '启动是否成功',
    error_message TEXT COMMENT '错误消息',
    created_at DATETIME NOT NULL COMMENT '创建时间',
    deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    
    INDEX idx_container_id (container_id),
    INDEX idx_docker_server_id (docker_server_id),
    INDEX idx_image_tag (image_tag),
    INDEX idx_used_cached_image (used_cached_image),
    INDEX idx_startup_time_ms (startup_time_ms),
    INDEX idx_startup_success (startup_success),
    INDEX idx_created_at (created_at)
) COMMENT '容器启动性能统计表';
