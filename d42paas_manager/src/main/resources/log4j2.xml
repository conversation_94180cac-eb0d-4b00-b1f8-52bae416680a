<?xml version="1.0" encoding="UTF-8"?>
<Configuration>

    <Properties>
        <Property name="LOG_INFO_LEVEL" source="logging.level.root" defaultValue="INFO"/>
        <!--输出日志的格式：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度， %msg：日志消息，%n是换行符 -->
        <Property name="LOG_PATTERN" value="%d [%t] [%-5level] [%logger{0}] [trace_id]=%mdc{trace_id} span_id=%mdc{span_id} trace_flags=%mdc{trace_flags} [PlaygroundId]=%X{PLAYGROUND_ID} [DockerId]=%X{DOCKER_ID} - %msg%n"/>
    </Properties>

    <Appenders>
        <!-- 控制台输出 -->
        <Console name="STDOUT" target="SYSTEM_OUT">
            <!--输出日志的格式-->
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>
    </Appenders>

    <Loggers>
        <Root level="${LOG_INFO_LEVEL}"> <AppenderRef ref="STDOUT"/> </Root>
        <Logger name="com.dao42.paas" level="debug" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <!-- HikariCP 连接池日志 -->
        <Logger name="com.zaxxer.hikari" level="debug" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>
    </Loggers>

</Configuration>