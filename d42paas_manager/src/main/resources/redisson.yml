singleServerConfig:
  address: "redis://${SPRING_REDIS_HOST:127.0.0.1}:${SPRING_REDIS_PORT:6379}"
  password: "${SPRING_REDIS_PASSWORD:}"
  database: ${SPRING_REDIS_DATABASE:1}
  connectionMinimumIdleSize: 10
  connectionPoolSize: 64
  dnsMonitoringInterval: 5000
  subscriptionConnectionMinimumIdleSize: 1
  subscriptionConnectionPoolSize: 50
  subscriptionTimeout: 7500
  timeout: 3000
  retryAttempts: 3
  retryInterval: 1500
  keepAlive: true
  tcpNoDelay: true
threads: 16
nettyThreads: 32
codec: !<org.redisson.codec.JsonJacksonCodec> {}
transportMode: "NIO"
