# 镜像化相关配置
system:
  image:
    # 镜像仓库配置
    registry:
      url: "992382636473.dkr.ecr.us-east-1.amazonaws.com"
      username: "AWS"
      password: "${ECR_PASSWORD:}"
      namespace: "clacky"
    
    # 镜像构建配置
    build:
      timeout-seconds: 300
      concurrent-builds: 5
      auto-cleanup: true
    
    # 缓存配置
    cache:
      hot-image-threshold: 10
      preload-enabled: true
      cleanup-enabled: true
      inactive-days: 30
    
    # @meta环境配置
    meta:
      s3-bucket: "clacky-meta-environment"
      update-interval-days: 30
      local-cache-path: "/cache/meta"
    
    # 用户配额配置
    quota:
      free-tier:
        max-images: 5
        max-storage-gb: 10
      basic-tier:
        max-images: 20
        max-storage-gb: 50
      pro-tier:
        max-images: 100
        max-storage-gb: 200
      enterprise-tier:
        max-images: -1  # 无限制
        max-storage-gb: -1  # 无限制
    
    # 是否自动清理本地镜像
    auto-cleanup: true

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "health,info,metrics,prometheus,image-metrics"
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: "paas-manager"
      service: "image-service"

# 日志配置
logging:
  level:
    com.dao42.paas.service.image: DEBUG
    com.dao42.paas.service.meta: DEBUG
    com.dao42.paas.service.container: DEBUG
    com.dao42.paas.service.scheduler: DEBUG
