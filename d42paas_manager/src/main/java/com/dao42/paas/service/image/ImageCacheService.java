package com.dao42.paas.service.image;

/**
 * 镜像缓存服务接口
 *
 * <AUTHOR>
 */
public interface ImageCacheService {

    /**
     * 检查服务器是否有指定镜像
     *
     * @param serverId 服务器ID
     * @param imageTag 镜像标签
     * @return 是否存在
     */
    boolean hasImage(String serverId, String imageTag);

    /**
     * 获取镜像评分 (用于选择最优服务器)
     *
     * @param serverId 服务器ID
     * @param imageTag 镜像标签
     * @return 评分
     */
    double getImageScore(String serverId, String imageTag);

    /**
     * 预拉取镜像到指定服务器
     *
     * @param serverId 服务器ID
     * @param imageTag 镜像标签
     */
    void preloadImage(String serverId, String imageTag);

    /**
     * 更新镜像缓存信息
     *
     * @param serverId 服务器ID
     * @param imageTag 镜像标签
     * @param size     镜像大小
     */
    void updateImageCache(String serverId, String imageTag, Long size);

    /**
     * 记录镜像命中
     *
     * @param serverId 服务器ID
     * @param imageTag 镜像标签
     */
    void recordImageHit(String serverId, String imageTag);

    /**
     * 删除镜像缓存记录
     *
     * @param serverId 服务器ID
     * @param imageTag 镜像标签
     */
    void removeImageCache(String serverId, String imageTag);
}
