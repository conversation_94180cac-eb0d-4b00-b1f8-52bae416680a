package com.dao42.paas.service.monitoring;

import com.dao42.paas.enums.ImageCacheStatus;
import com.dao42.paas.enums.ThreadImageStatus;
import com.dao42.paas.repository.image.ImageCacheRepository;
import com.dao42.paas.repository.image.ThreadImageRepository;
import com.dao42.paas.repository.image.UserImageQuotaRepository;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 镜像化监控指标服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageMetricsService {

    private final MeterRegistry meterRegistry;
    private final ThreadImageRepository threadImageRepository;
    private final ImageCacheRepository imageCacheRepository;
    private final UserImageQuotaRepository userImageQuotaRepository;

    // 计数器
    private Counter threadImageBuildCounter;
    private Counter threadImageBuildSuccessCounter;
    private Counter threadImageBuildFailureCounter;
    private Counter imageCacheHitCounter;
    private Counter imageCacheMissCounter;
    private Counter containerStartupCounter;
    private Counter containerStartupWithCacheCounter;

    // 计时器
    private Timer threadImageBuildTimer;
    private Timer containerStartupTimer;
    private Timer imagePullTimer;
    private Timer metaInjectionTimer;

    // 仪表盘
    private final AtomicLong totalThreadImages = new AtomicLong(0);
    private final AtomicLong availableThreadImages = new AtomicLong(0);
    private final AtomicLong totalImageCaches = new AtomicLong(0);
    private final AtomicLong availableImageCaches = new AtomicLong(0);
    private final AtomicLong totalUsers = new AtomicLong(0);

    @PostConstruct
    public void initMetrics() {
        // 初始化计数器
        threadImageBuildCounter = Counter.builder("thread_image_build_total")
            .description("Total number of thread image builds")
            .register(meterRegistry);

        threadImageBuildSuccessCounter = Counter.builder("thread_image_build_success_total")
            .description("Total number of successful thread image builds")
            .register(meterRegistry);

        threadImageBuildFailureCounter = Counter.builder("thread_image_build_failure_total")
            .description("Total number of failed thread image builds")
            .register(meterRegistry);

        imageCacheHitCounter = Counter.builder("image_cache_hit_total")
            .description("Total number of image cache hits")
            .register(meterRegistry);

        imageCacheMissCounter = Counter.builder("image_cache_miss_total")
            .description("Total number of image cache misses")
            .register(meterRegistry);

        containerStartupCounter = Counter.builder("container_startup_total")
            .description("Total number of container startups")
            .register(meterRegistry);

        containerStartupWithCacheCounter = Counter.builder("container_startup_with_cache_total")
            .description("Total number of container startups using cached images")
            .register(meterRegistry);

        // 初始化计时器
        threadImageBuildTimer = Timer.builder("thread_image_build_duration")
            .description("Thread image build duration")
            .register(meterRegistry);

        containerStartupTimer = Timer.builder("container_startup_duration")
            .description("Container startup duration")
            .register(meterRegistry);

        imagePullTimer = Timer.builder("image_pull_duration")
            .description("Image pull duration")
            .register(meterRegistry);

        metaInjectionTimer = Timer.builder("meta_injection_duration")
            .description("Meta environment injection duration")
            .register(meterRegistry);

        // 初始化仪表盘
        Gauge.builder("thread_images_total")
            .description("Total number of thread images")
            .register(meterRegistry, this, ImageMetricsService::getTotalThreadImages);

        Gauge.builder("thread_images_available")
            .description("Number of available thread images")
            .register(meterRegistry, this, ImageMetricsService::getAvailableThreadImages);

        Gauge.builder("image_caches_total")
            .description("Total number of image caches")
            .register(meterRegistry, this, ImageMetricsService::getTotalImageCaches);

        Gauge.builder("image_caches_available")
            .description("Number of available image caches")
            .register(meterRegistry, this, ImageMetricsService::getAvailableImageCaches);

        Gauge.builder("users_with_quota_total")
            .description("Total number of users with image quota")
            .register(meterRegistry, this, ImageMetricsService::getTotalUsers);

        Gauge.builder("image_cache_hit_rate")
            .description("Image cache hit rate")
            .register(meterRegistry, this, ImageMetricsService::getCacheHitRate);

        log.info("镜像化监控指标初始化完成");
    }

    /**
     * 记录Thread镜像构建
     */
    public void recordThreadImageBuild() {
        threadImageBuildCounter.increment();
    }

    /**
     * 记录Thread镜像构建成功
     */
    public void recordThreadImageBuildSuccess() {
        threadImageBuildSuccessCounter.increment();
    }

    /**
     * 记录Thread镜像构建失败
     */
    public void recordThreadImageBuildFailure() {
        threadImageBuildFailureCounter.increment();
    }

    /**
     * 记录镜像缓存命中
     */
    public void recordImageCacheHit() {
        imageCacheHitCounter.increment();
    }

    /**
     * 记录镜像缓存未命中
     */
    public void recordImageCacheMiss() {
        imageCacheMissCounter.increment();
    }

    /**
     * 记录容器启动
     */
    public void recordContainerStartup() {
        containerStartupCounter.increment();
    }

    /**
     * 记录使用缓存镜像的容器启动
     */
    public void recordContainerStartupWithCache() {
        containerStartupWithCacheCounter.increment();
    }

    /**
     * 记录Thread镜像构建时间
     */
    public Timer.Sample startThreadImageBuildTimer() {
        return Timer.start(meterRegistry);
    }

    /**
     * 停止Thread镜像构建计时
     */
    public void stopThreadImageBuildTimer(Timer.Sample sample) {
        sample.stop(threadImageBuildTimer);
    }

    /**
     * 记录容器启动时间
     */
    public Timer.Sample startContainerStartupTimer() {
        return Timer.start(meterRegistry);
    }

    /**
     * 停止容器启动计时
     */
    public void stopContainerStartupTimer(Timer.Sample sample) {
        sample.stop(containerStartupTimer);
    }

    /**
     * 记录镜像拉取时间
     */
    public Timer.Sample startImagePullTimer() {
        return Timer.start(meterRegistry);
    }

    /**
     * 停止镜像拉取计时
     */
    public void stopImagePullTimer(Timer.Sample sample) {
        sample.stop(imagePullTimer);
    }

    /**
     * 记录@meta环境注入时间
     */
    public Timer.Sample startMetaInjectionTimer() {
        return Timer.start(meterRegistry);
    }

    /**
     * 停止@meta环境注入计时
     */
    public void stopMetaInjectionTimer(Timer.Sample sample) {
        sample.stop(metaInjectionTimer);
    }

    /**
     * 定期更新指标 (每分钟)
     */
    @Scheduled(fixedRate = 60000)
    public void updateMetrics() {
        try {
            // 更新Thread镜像统计
            long totalImages = threadImageRepository.count();
            long availableImages = threadImageRepository.countByStatus(ThreadImageStatus.AVAILABLE);
            totalThreadImages.set(totalImages);
            availableThreadImages.set(availableImages);

            // 更新镜像缓存统计
            long totalCaches = imageCacheRepository.count();
            long availableCaches = imageCacheRepository.countByStatus(ImageCacheStatus.AVAILABLE);
            totalImageCaches.set(totalCaches);
            availableImageCaches.set(availableCaches);

            // 更新用户统计
            long totalUsersCount = userImageQuotaRepository.count();
            totalUsers.set(totalUsersCount);

        } catch (Exception e) {
            log.error("更新监控指标失败", e);
        }
    }

    // Getter方法供Gauge使用
    public double getTotalThreadImages() {
        return totalThreadImages.get();
    }

    public double getAvailableThreadImages() {
        return availableThreadImages.get();
    }

    public double getTotalImageCaches() {
        return totalImageCaches.get();
    }

    public double getAvailableImageCaches() {
        return availableImageCaches.get();
    }

    public double getTotalUsers() {
        return totalUsers.get();
    }

    public double getCacheHitRate() {
        double hits = imageCacheHitCounter.count();
        double misses = imageCacheMissCounter.count();
        double total = hits + misses;
        return total > 0 ? hits / total : 0.0;
    }
}
