package com.dao42.paas.service.docker;

import com.dao42.paas.dto.image.CommitOptions;
import com.dao42.paas.dto.image.ImageInfo;

import java.util.List;

/**
 * Docker镜像服务接口
 *
 * <AUTHOR>
 */
public interface DockerImageService {

    /**
     * 提交容器为镜像
     *
     * @param containerId 容器ID
     * @param imageTag    镜像标签
     * @param options     提交选项
     * @return 镜像ID
     */
    String commit(String containerId, String imageTag, CommitOptions options);

    /**
     * 拉取镜像
     *
     * @param serverId  服务器ID
     * @param imageTag  镜像标签
     */
    void pullImage(String serverId, String imageTag);

    /**
     * 删除本地镜像
     *
     * @param imageId 镜像ID
     */
    void removeLocal(String imageId);

    /**
     * 删除服务器上的镜像
     *
     * @param serverId 服务器ID
     * @param imageTag 镜像标签
     */
    void removeImage(String serverId, String imageTag);

    /**
     * 列出服务器上的镜像
     *
     * @param serverId 服务器ID
     * @return 镜像列表
     */
    List<ImageInfo> listImages(String serverId);

    /**
     * 检查镜像是否存在
     *
     * @param serverId 服务器ID
     * @param imageTag 镜像标签
     * @return 是否存在
     */
    boolean imageExists(String serverId, String imageTag);

    /**
     * 获取镜像信息
     *
     * @param serverId 服务器ID
     * @param imageTag 镜像标签
     * @return 镜像信息
     */
    ImageInfo getImageInfo(String serverId, String imageTag);
}
