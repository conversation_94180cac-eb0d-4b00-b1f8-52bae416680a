package com.dao42.paas.service.playground;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.dao42.paas.constants.MdcConstant;
import com.dao42.paas.service.storage.StorageProvider;
import com.dao42.paas.service.storage.StorageService;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.MDC;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionManager;
import org.springframework.transaction.annotation.Transactional;

import com.dao42.paas.bean.FileDownloadBean;
import com.dao42.paas.common.ResourcesLimit;
import com.dao42.paas.common.bean.AgentConfigBean;
import com.dao42.paas.common.constants.MsgType;
import com.dao42.paas.common.enums.PlaygroundActiveResult;
import com.dao42.paas.common.enums.PlaygroundStatus;
import com.dao42.paas.common.message.BaseMQMsg;
import com.dao42.paas.common.message.PlaygroundHeartBeatResultMQMsg;
import com.dao42.paas.common.message.PlaygroundInfoMQMsg;
import com.dao42.paas.common.message.PlaygroundUrlResourceMQMsg;
import com.dao42.paas.config.AsyncConfig;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.constants.Constant;
import com.dao42.paas.enums.DockerStatus;
import com.dao42.paas.enums.PlaygroundBindType;
import com.dao42.paas.exception.docker.DockerCreateException;
import com.dao42.paas.exception.docker.DockerStartException;
import com.dao42.paas.exception.ideServer.NoIDEServerException;
import com.dao42.paas.exception.playground.PlaygroundActiveException;
import com.dao42.paas.exception.resource.NoDockerServerException;
import com.dao42.paas.exception.resource.ResourceNotEnoughException;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneFileDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneFileListDTO;
import com.dao42.paas.external.sdk.dto.playground.CmdDTO;
import com.dao42.paas.external.sdk.dto.playground.CmdRunIdDTO;
import com.dao42.paas.external.sdk.dto.playground.PlaygroundImportJsonDTO;
import com.dao42.paas.external.sdk.dto.resource.URLDTO;
import com.dao42.paas.framework.alert.ExceptionMsgBot;
import com.dao42.paas.framework.alert.ExceptionMsgBotBean;
import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.model.IDEServer;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.PlaygroundBindLog;
import com.dao42.paas.model.RunCmdLog;
import com.dao42.paas.model.codezone.CodeZone;
import com.dao42.paas.model.codezone.CodeZoneSnapshot;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.unittest.UnitTestRunLog;
import com.dao42.paas.rabbit.MQMessageSender;
import com.dao42.paas.rabbit.ideserver.PlaygroundInfoMsgHelper;
import com.dao42.paas.repository.IDEServerRepository;
import com.dao42.paas.repository.PlaygroundBindLogRepository;
import com.dao42.paas.repository.PlaygroundRepository;
import com.dao42.paas.repository.RunCmdLogRepository;
import com.dao42.paas.repository.unitTest.UnitTestRunLogRepository;
import com.dao42.paas.service.storage.BtrfsService;
import com.dao42.paas.service.IDEServerService;
import com.dao42.paas.service.RedisExternalService;
import com.dao42.paas.service.TenantService;
import com.dao42.paas.service.codezone.CodeZoneService;
import com.dao42.paas.service.codezone.CodeZoneSnapshotService;
import com.dao42.paas.service.docker.DockerAgentService;
import com.dao42.paas.service.docker.DockerContainerService;
import com.dao42.paas.service.docker.DockerExternalService;
import com.dao42.paas.service.docker.DockerResourceService;
import com.dao42.paas.service.docker.DockerService;
import com.dao42.paas.service.file.FileService;
import com.dao42.paas.service.file.FileUtilService;
import com.dao42.paas.service.ideserver.LoadIDEServerSelector;
import com.dao42.paas.service.permission.PermissionService;
import com.dao42.paas.service.resource.URLResourceService;
import com.dao42.paas.utils.DirUtil;
import com.dao42.paas.utils.FileUtil;
import com.github.dockerjava.api.exception.ConflictException;
import com.github.dockerjava.api.exception.NotFoundException;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 新PlaygroundService
 *
 * <AUTHOR>
@Slf4j
@Service
public class PlaygroundService {

    private final SystemProperties systemProperties;
    private final PlaygroundRepository playgroundRepository;
    private final PlaygroundBindLogRepository bindLogRepository;
    private final UnitTestRunLogRepository unitTestRunLogRepository;
    private final RunCmdLogRepository runCmdLogRepository;
    private final IDEServerRepository ideServerRepository;
    private final LoadIDEServerSelector ideServerSelector;
    private final PermissionService permissionService;
    private final BtrfsService btrfsService;
    private final CodeZoneService codeZoneService;
    private final CodeZoneSnapshotService codeZoneSnapshotService;
    private final DockerService dockerService;
    private final DockerAgentService dockerAgentService;
    private final DockerExternalService dockerExternalService;
    private final DockerContainerService dockerContainerService;
    private final RedisExternalService redisExternalService;
    private final FileService fileService;
    private final URLResourceService urlResourceService;
    private final FileUtilService fileUtilService;
    private final MQMessageSender mqMessageSender;
    private final ExceptionMsgBot exceptionMsgBot;
    private final IDEServerService ideServerService;
    private final PlaygroundService self;
    private final TenantService tenantService;
    private final DockerResourceService resourceService;

    private final StorageProvider storageProvider;

    private final Long RETRY_LIMIT = 3L;

    public PlaygroundService(
        SystemProperties systemProperties,
        PlaygroundRepository playgroundRepository,
        DockerResourceService resourceService,
        PlaygroundBindLogRepository bindLogRepository,
        IDEServerRepository ideServerRepository,
        LoadIDEServerSelector ideServerSelector,
        UnitTestRunLogRepository unitTestRunLogRepository,
        PermissionService permissionService,
        BtrfsService btrfsService,
        CodeZoneService codeZoneService,
        CodeZoneSnapshotService codeZoneSnapshotService,
        DockerContainerService containerService,
        DockerService dockerService,
        DockerExternalService dockerExternalService,
        DockerAgentService agentService,
        URLResourceService urlResourceService,
        RunCmdLogRepository runCmdLogRepository,
        RedisExternalService redisExternalService,
        FileService fileService,
        FileUtilService fileUtilService,
        MQMessageSender mqMessageSender,
        ExceptionMsgBot exceptionMsgBot,
        @Lazy IDEServerService ideServerService,
        @Lazy PlaygroundService self,
        TenantService tenantService,
        TransactionManager transactionManager,
        EntityManager entityManager,
        StorageProvider storageProvider) {
        this.systemProperties = systemProperties;
        this.resourceService = resourceService;
        this.playgroundRepository = playgroundRepository;
        this.bindLogRepository = bindLogRepository;
        this.ideServerRepository = ideServerRepository;
        this.unitTestRunLogRepository = unitTestRunLogRepository;
        this.ideServerSelector = ideServerSelector;
        this.permissionService = permissionService;
        this.btrfsService = btrfsService;
        this.codeZoneService = codeZoneService;
        this.codeZoneSnapshotService = codeZoneSnapshotService;
        this.dockerContainerService = containerService;
        this.dockerService = dockerService;
        this.dockerExternalService = dockerExternalService;
        this.dockerAgentService = agentService;
        this.redisExternalService = redisExternalService;
        this.fileService = fileService;
        this.urlResourceService = urlResourceService;
        this.fileUtilService = fileUtilService;
        this.mqMessageSender = mqMessageSender;
        this.exceptionMsgBot = exceptionMsgBot;
        this.ideServerService = ideServerService;
        this.runCmdLogRepository = runCmdLogRepository;
        this.self = self;
        this.tenantService = tenantService;
        this.storageProvider=storageProvider;
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * 查找
     *
     * @param playgroundId ID
     * @return Playground
     */
    public Playground get(Long playgroundId) {
        Playground playground = this.getOptional(playgroundId)
            .orElseThrow(
                () -> new CustomRuntimeException(
                    "playground.not.exist",
                    String.format("playground: %s 不存在", playgroundId)));
        if (playground.getStatus() == PlaygroundStatus.END) {
            throw new CustomRuntimeException(
                "playground.has.ended", String.format("playground: %s 已关闭", playgroundId));
        }
        permissionService.checkPermission(playground.getTenantId());
        return playground;
    }

    /**
     * 查找
     *
     * @param playgroundId ID
     * @return Optional<Playground>
     */
    public Optional<Playground> getOptional(Long playgroundId) {
        return playgroundRepository.findById(playgroundId);
    }

    /**
     * 查询多个Playground
     *
     * @param playgroundIds ID集合
     * @return Playground集合
     */
    public List<Playground> get(Collection<Long> playgroundIds) {
        return (List<Playground>) playgroundRepository.findAllById(playgroundIds);
    }

    /**
     * 创建
     *
     * @return
     */
    public Playground create(Long tenantId, PlaygroundBindType bindType, Map<String, String> env) {
        if (env == null) {
            env = new HashMap<>();
        }
        Playground playground = new Playground();
        playground.setTenantId(tenantId);
        playground.setStatus(PlaygroundStatus.EMPTY);
        playground.setBindType(bindType);
        playground.setEnv(env);
        return playgroundRepository.save(playground);
    }

    /**
     * 绑定CodeZone
     *
     * @param playground playground
     * @param codeZone   codeZone
     */
    @Transactional
    public void bindCodeZone(Playground playground, CodeZone codeZone) {
        playground.setBindType(PlaygroundBindType.CODE_ZONE);
        playground.setBindObject(codeZone);
        // 创建新容器
        DockerContainer docker = dockerService.generateNewForEdit(codeZone, playground);
        playground.setDockerContainer(docker);
        this.bind(PlaygroundBindType.CODE_ZONE, playground, codeZone, docker);
    }

    /**
     * 绑定CodeZone副本
     *
     * @param playground playground
     * @param codeZone   codeZone
     */
    @Transactional
    public void bindDuplicate(Playground playground, CodeZone codeZone) {
        playground.setBindType(PlaygroundBindType.CODE_ZONE_DUPLICATE);
        playground.setBindObject(codeZone);
        // 创建新容器
        DockerContainer docker = dockerService.generateForDuplicate(codeZone, playground);
        this.bind(PlaygroundBindType.CODE_ZONE_DUPLICATE, playground, codeZone, docker);
    }

    /**
     * 绑定CodeZone快照
     *
     * @param playgroundId       Playground ID
     * @param codeZoneSnapshotId snapshot ID
     * @param defaultOpenFile    默认打开文件路径
     */
    public Playground switchToSnapshot(
        Long playgroundId, Long codeZoneSnapshotId, String defaultOpenFile) {
        Playground playground = this.get(playgroundId);
        CodeZoneSnapshot snapshot = codeZoneSnapshotService.get(codeZoneSnapshotId);
        // 生成新容器
        DockerContainer docker = dockerService.generateForSnapshot(snapshot, playground, defaultOpenFile);
        this.bind(playground, snapshot, docker, defaultOpenFile);

        dockerService.bindResourcesToDocker(snapshot.getCodeZone(), docker);
        tenantService.coverGitignore(
            docker.getRootPath() + Constant.CODE_ZONE_SOURCE_PATH,
            snapshot.getCodeZone().getEnvironmentVer().getEnvVerKey(),
            playground.getTenantId());
        URLDTO urldto = this.getUrl(docker, playground.isLspSupported());
        if (playground.getIdeServer() != null) {
            this.notifyChangePlaygroundInfo(
                playground, urldto, snapshot.getPath(), defaultOpenFile);
        }
        return playground;
    }

    /**
     * 绑定Docker快照
     *
     * @param playground      playground
     * @param dockerContainer docker
     * @param snapshot        snapshot
     * @param defaultOpenFile 默认打开文件
     */
    @Transactional
    public URLDTO switchToDocker(
        Playground playground,
        DockerContainer dockerContainer,
        CodeZoneSnapshot snapshot,
        String defaultOpenFile) {
        this.bind(playground, snapshot, dockerContainer, defaultOpenFile);
        return getUrl(dockerContainer, playground.isLspSupported());
    }

    public URLDTO getUrl(DockerContainer dockerContainer, boolean lspSupported) {
        String serviceUrl = urlResourceService.getServiceUrlResource(dockerContainer).getUrl();
        String lspUrl = null;
        if (lspSupported) {
            lspUrl = urlResourceService.getLspUrlResource(dockerContainer).getUrl();
        }
        URLDTO urldto = new URLDTO();
        urldto.setServiceUrl(serviceUrl);
        urldto.setLspUrl(lspUrl);
        return urldto;
    }

    /**
     * 激活playground
     *
     * @param playground playground
     * @return 激活结果
     */
    public void activate(Playground playground)
        throws PlaygroundActiveException, NoDockerServerException, NoIDEServerException,
        DockerCreateException, DockerStartException {
        // 设置ide server
        if (playground.getIdeServer() == null) {
            IDEServer ideServer = ideServerSelector.select();
            playground.setIdeServer(ideServer);
            playgroundRepository.save(playground);
        }

        DockerContainer docker = playground.getDockerContainer();
        MDC.put(MdcConstant.DOCKER_ID, String.valueOf(docker.getId()));
        // 检查docker绑定
        if (docker == null) {
            throw new PlaygroundActiveException(PlaygroundActiveResult.DOCKER_NOT_BIND);
        }
    	//批量创建的第一次激活修改为非批量创建，用于标记第一次使用，后期走正常逻辑
        if (playground.getDockerContainer().isPreCreate()){
            playground.getDockerContainer().setPreCreate(false);
            playground=playgroundRepository.save(playground);
        }
        
        // 生成容器启动所需的环境变量
        AgentConfigBean config = dockerAgentService.getConfig(playground);
        log.info("DockerScheduling-Active, active start, playgroundId: {},dockerId: {},docker status: {}",
            playground.getId(), docker.getId(), docker.getStatus());

        long startTime = 0;
        long endTime = 0;
        long diff = 0;
        switch (docker.getStatus()) {
            case NOT_INIT:
                // 未初始化，先创建
                startTime = System.currentTimeMillis();
                // 创建容器配置到数据库
                docker = dockerService.createContainer(docker, config);
                dockerService.startContainer(docker);
                endTime = System.currentTimeMillis();
                diff = endTime - startTime;
                log.info("DockerScheduling-Active, active end(NOT_INIT),  playgroundId: {}, dockerId: {}, cost_time: {} ms",
                    playground.getId(), docker.getId(), diff);
                break;
            case START_SUCCESS:
                // 已启动，不做操作
                // 容器为激活状态，修正
//                if (playground.getStatus() != PlaygroundStatus.ACTIVE) {
//                    playgroundRepository.updateStatusByDockerId(PlaygroundStatus.ACTIVE, docker.getId());
//                }
//
//                dockerService.sendActiveSuccessResult(playground);
                // docker状态为START_SUCCESS不代表容器可以，
                // 给docker发送一个心跳包探测docker是否真的存活
//                redisExternalService.setPlaygroundHeartbeatWait(playground.getId());
                this.mqMessageSender.sendToDocker(MsgType.PLAYGROUND_HEART_BEAT, playground, new BaseMQMsg());
                break;
            case STOP_SUCCESS: {
                startTime = System.currentTimeMillis();
                // 检查原server资源，如果资源不足，需要重新选择server并创建容器
                if (!dockerService.checkOriginServerResource(docker)) {
                    docker = dockerService.createContainer(docker, config);
                }
                try {
                    dockerService.startContainer(docker);
                } catch (NotFoundException notFoundException) {
                    docker = dockerContainerService.getById(docker.getId());
                    docker.setStatus(DockerStatus.NOT_INIT);
                    docker = dockerService.createContainer(docker, config);
                    dockerService.startContainer(docker);
                    log.debug("DockerScheduling-Active, STOP_SUCCESS active end,  playgroundId: {}, dockerId: {}, notFoundException: {}",
                        playground.getId(), docker.getId(), notFoundException);
                } catch (ConflictException conflictException) {
                    docker = dockerContainerService.getById(docker.getId());
                    docker.setStatus(DockerStatus.NOT_INIT);
                    docker = dockerService.createContainer(docker, config);
                    dockerService.startContainer(docker);
                    log.debug("DockerScheduling-Active, STOP_SUCCESS active end,  playgroundId: {}, dockerId: {}, conflictException: {}",
                        playground.getId(), docker.getId(), conflictException);
                } finally {
                    endTime = System.currentTimeMillis();
                    diff = endTime - startTime;
                    log.info("DockerScheduling-Active, STOP_SUCCESS active end,  playgroundId: {}, dockerId: {}, cost_time: {} ms",
                        playground.getId(), docker.getId(), diff);
                }

                break;
            }
            case START_FAIL:
            case STOP_FAIL:
            case DELETE_SUCCESS:
            case DELETE_FAIL: {
                try {
                    log.debug("DockerScheduling-Active active buildNewDocker,  playgroundId: {}, dockerId: {}, docker_statu: {}",
                        playground.getId(), docker.getId(), docker.getStatus());
                    // 重建新容器
                    startTime = System.currentTimeMillis();

                    // 这个流程一般是机器资源缩容时会把主容器的状态改为DELETE_SUCCESS，
                    // 激活时就会走到这个流程，这里增加重试是为了保障容器激活成功
                    int retries = 3;
                    while (retries > 0) {
                        try {
                            docker = this.buildNewDocker(playground);
                            break;
                        } catch (Exception e) {
                            retries--;
                            log.info("DockerScheduling-Active, buildNewDocker,  playgroundId: {}, retries: {}, e: {}",
                                playground.getId(), retries, e);
                            if (retries == 0) {
                                throw e;
                            }
                        }
                    }

                    // 未初始化，先创建
                    try {
                        docker = dockerService.createContainer(docker, config);
                        dockerService.startContainer(docker);
                        endTime = System.currentTimeMillis();
                        diff = endTime - startTime;
                        log.debug("DockerScheduling-Active active buildNewDocker end,  playgroundId: {}, "
                                + "dockerId: {}, docker_status: {}, cost_time: {} ms",
                            playground.getId(), docker.getId(), docker.getStatus(), diff);
                    } catch (Exception e) {
                        log.info("DockerScheduling-Active, buildNewDocker-startContainer, playgroundId: {}, e: {}",
                            playground.getId(), e);
                        throw e;
                    }
                } catch (Exception e) {
                    log.info("DockerScheduling-Active, buildNewDocker active end,  playgroundId: {}, e: {}",
                        playground.getId(), e);
                    throw e;
                }

                break;
            }
            default:
                log.warn("DockerScheduling-Active, 容器(id:{}), 未知状态{}", docker.getId(), docker.getStatus());
                break;
        }
    }

    /**
     * 重启 playground
     *
     * @param playground playground
     * @return 激活结果
     */
    public void restart(Playground playground)
        throws PlaygroundActiveException, NoDockerServerException, DockerCreateException,
        DockerStartException {
        log.debug("DockerScheduling-Active, Docker restart start");

        DockerContainer docker = playground.getDockerContainer();
        AgentConfigBean config = dockerAgentService.getConfig(playground);
        // 检查docker绑定
        if (docker == null) {
            throw new PlaygroundActiveException(PlaygroundActiveResult.DOCKER_NOT_BIND);
        }
        try {
            dockerService.stop(docker);
		} catch (Exception e) {
			log.warn("DockerScheduling-Active, 容器关闭失败：{}",docker.getId());
		}
        try {
            resourceService.chooseAllServer(docker);
            dockerService.startContainer(docker);
        } catch (NotFoundException notFoundException) {
            docker = dockerContainerService.getById(docker.getId());
            docker.setStatus(DockerStatus.NOT_INIT);
            docker = dockerService.createContainer(docker, config);
            dockerService.startContainer(docker);
        } catch (ConflictException e) {
            log.info("DockerScheduling-Active, ConflictException", e);
        }
    }

    /**
     * 暂停
     *
     * @param playground playground
     */
    public void pause(Playground playground, String from) {
    	log.info("docker-pause-begin:{}, from: {}", playground.getId(), from);
        if (PlaygroundStatus.ACTIVE != playground.getStatus()) {
            log.warn(
                String.format(
                    "Playground:%d with status:%s can't pause.",
                    playground.getId(), playground.getStatus()));
            return;
        }
        List<PlaygroundBindLog> playgroundBindLogs = this.bindLogRepository.findAllByPlayground(playground);
        playgroundBindLogs = playgroundBindLogs.stream()
            .filter(distinctByKey(PlaygroundBindLog::getDockerContainerId))
            .collect(Collectors.toList());
        playgroundBindLogs.forEach(
            item -> {
                DockerContainer docker = this.dockerContainerService.getById(item.getDockerContainerId());
                if (docker == null) {
                    log.warn("Pause playground:{} without docker", playground.getId());
                    return;
                }
                log.debug("docker-pause-beginning1:{}, dockerId: {}, from: {}", playground.getId(), docker.getId(), from);
                if (DockerStatus.START_SUCCESS != docker.getStatus()) {
                    return;
                }
                // 停止docker
                dockerService.stop(docker);
                // 更新状态
                // 副本模式的playground是临时的，停止即删除
                if (PlaygroundBindType.CODE_ZONE_DUPLICATE == playground.getBindType()) {
                    docker = dockerContainerService.getById(docker.getId());
                    dockerService.remove(docker);
                    log.debug(
                        "Remove docker,because codeZone.overlay timeout.ID:{}",
                        docker.getId());
                }

                log.debug("docker-pause-beginning2:{}, dockerId: {}", playground.getId(), docker.getId());
            });

        if (PlaygroundBindType.CODE_ZONE_DUPLICATE == playground.getBindType()) {
            playground.setStatus(PlaygroundStatus.END);
        } else {
            playground.setStatus(PlaygroundStatus.INACTIVE);
        }

        log.info("docker-pause-end:{}, from: {}", playground.getId(), from);

        playgroundRepository.save(playground);
        // 发送通知
        this.notifyPlaygroundInfo(playground);
    }

    /**
     * 移除（释放资源）
     *
     * @param playground playground
     */
    @Transactional
    public void release(Playground playground) {
        if (playground.getStatus() != PlaygroundStatus.ACTIVE) {
            return;
        }
        if (playground.getDockerContainer() != null) {
            this.stopOldDocker(playground);
        }
        // 结束Playground
        playground.setStatus(PlaygroundStatus.INACTIVE);
        playgroundRepository.save(playground);
    }

    /**
     * 重建容器
     *
     * @param playground playground
     * @return 新容器
     */
    private DockerContainer buildNewDocker(Playground playground) {
        log.info("DockerScheduling-buildNewDocker begin,  playgroundId: {}, BindType: {}",
            playground.getId(), playground.getBindType());
        dockerService.remove(playground.getDockerContainer());
        playground.setDockerContainer(
            dockerContainerService.getById(playground.getDockerContainer().getId()));
        switch (playground.getBindType()) {
            // 删除原有容器
            case CODE_ZONE -> {
                log.info("DockerScheduling-buildNewDocker-codezone,  playgroundId: {}", playground.getId());
                CodeZone codeZone = playground.getCodeZone();
                DockerContainer docker = dockerService.update(playground);
                // 绑定新容器，绑定对象没变化，不需要插入绑定记录
                this.bind(PlaygroundBindType.CODE_ZONE, playground, codeZone, docker);
                return docker;
            }
            case CODE_ZONE_SNAPSHOT -> {
                CodeZoneSnapshot snapshot = playground.getCodeZoneSnapshot();
                DockerContainer docker = dockerService.update(playground);
                this.bind(playground, snapshot, docker, null);
                return docker;
            }
            case CODE_ZONE_DUPLICATE -> {
                CodeZone codeZone = playground.getCodeZone();
                DockerContainer docker = dockerService.update(playground);
                this.bind(PlaygroundBindType.CODE_ZONE_DUPLICATE, playground, codeZone, docker);
                // 生成新容器
                return docker;
            }
            default -> {
                log.warn(
                    "重建Docker失败，不支持的类型 playground id:{}, type:{}, ",
                    playground.getId(),
                    playground.getBindType());
                throw new CustomRuntimeException(
                    "当前playground类型{},不支持重新激活", playground.getBindType());
            }
        }
    }

    /**
     * 移除已绑定的docker容器
     *
     * @param playground playground
     */
    @Transactional
    public void stopOldDocker(Playground playground) {
        // 判断是否有正在运行的测试用例 如果有延时 30s ,运行超过一个小时容器走正常逻辑停止
        if (checkUnittest(playground.getDockerContainer())) {
            log.warn(
                "playground({}) stop  old docker({}) not work, because unittest not finished!",
                playground.getId(),
                playground.getDockerContainer().getId());
            return;
        }
        // 停止监视
        redisExternalService.stopPlaygroundMonitor(playground.getId());
        // 停止容器并回收资源
        dockerService.stopAsync(playground.getDockerContainer());
    }

    public boolean checkUnittest(DockerContainer dockerContainer) {
        boolean result = false;
        // 判断是否有正在运行的测试用例 如果有延时 30s ,运行超过三分钟容器走正常逻辑停止
        final List<UnitTestRunLog> unitTestRunLogList = this.unitTestRunLogRepository
            .findListByDockerContainerAndEndOfRunFalse(
                dockerContainer);
        for (UnitTestRunLog unitTestRunLog : unitTestRunLogList) {
            if (unitTestRunLog != null
                && (unitTestRunLog.getCreatedDate().getTime()
                + Constant.UNITTEST_RUN_TIMEOUT_MILLISECOND) > System.currentTimeMillis()) {
                if (!unitTestRunLog.isEndOfRun()) {
                    redisExternalService.startDockerMonitor(dockerContainer);
                    result = true;
                    break;
                }
            }
        }
        // 判断是否有正在运行的命令 如果有延时 30s ,运行超过三分钟容器走正常逻辑停止
        final List<RunCmdLog> runCmdLogs = this.runCmdLogRepository
            .findListByDockerContainerAndEndOfRunFalse(dockerContainer);
        for (RunCmdLog runCmdLog : runCmdLogs) {
            if (runCmdLog != null
                && (runCmdLog.getCreatedDate().getTime()
                + Constant.UNITTEST_RUN_TIMEOUT_MILLISECOND) > System.currentTimeMillis()) {
                if (!runCmdLog.isEndOfRun()) {
                    redisExternalService.startDockerMonitor(dockerContainer);
                    result = true;
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 移除 docker 容器
     *
     * @param dockerContainer
     */
    public void removeDocker(DockerContainer dockerContainer) {
        if (checkUnittest(dockerContainer)) {
            return;
        }
        log.info("移除docker容器:{}",dockerContainer.getId());
        // 停止监视
        redisExternalService.stopDockerMonitor(dockerContainer.getId());
        // 停止容器并回收资源
        dockerService.stop(dockerContainer);
        Playground playground = this.playgroundRepository.findByDockerContainerId(dockerContainer.getId());
        if (playground != null && playground.getStatus() == PlaygroundStatus.ACTIVE) {
            // 状态不一致重启
            try {
                log.info("restart start docker");
                this.restart(playground);
            } catch (Exception e) {
                log.error("restart fail", e);
                playground.setStatus(PlaygroundStatus.INACTIVE);
                playgroundRepository.save(playground);
                this.notifyPlaygroundInfo(playground);
            }
        }
    }

    private void bind(
        PlaygroundBindType bindType,
        Playground playground,
        CodeZone codeZone,
        DockerContainer newDocker) {
        playground = get(playground.getId());
        // 有旧容器的话停止监视，并且移除旧容器
        if (playground.getDockerContainer() != null
            && !newDocker.equals(playground.getDockerContainer())
            && playground.getDockerContainer().getStatus() == DockerStatus.START_SUCCESS) {
            this.stopOldDocker(playground);
        }

        // 绑定容器到playground
        playground.setDockerContainer(newDocker);
        // 绑定对象
        playground.setBindType(bindType);
        playground.setBindObject(codeZone);
        playground.setStatus(
            newDocker.getStatus() == DockerStatus.START_SUCCESS
                ? PlaygroundStatus.ACTIVE
                : PlaygroundStatus.INACTIVE);
        // 增加绑定记录
        PlaygroundBindLog log = new PlaygroundBindLog(bindType, playground, codeZone.getId(), newDocker.getId());
        playground.getBindLogs().add(log);
        playgroundRepository.save(playground);
    }

    public void bind(
        Playground playground,
        CodeZoneSnapshot snapshot,
        DockerContainer newDocker,
        String defaultOpenFile) {
        // 有旧容器的话停止监视，并且移除旧容器
        if (playground.getDockerContainer() != null
            && !newDocker.equals(playground.getDockerContainer())
            && playground.getDockerContainer().getStatus() == DockerStatus.START_SUCCESS) {
            this.stopOldDocker(playground);
        }
        // 绑定对象
        playground.setBindType(PlaygroundBindType.CODE_ZONE_SNAPSHOT);
        playground.setBindObject(snapshot);
        playground.setDockerContainer(newDocker);
        playground.setStatus(
            newDocker.getStatus() == DockerStatus.START_SUCCESS
                ? PlaygroundStatus.ACTIVE
                : PlaygroundStatus.INACTIVE);
        // 增加绑定记录
        PlaygroundBindLog log = new PlaygroundBindLog(
            PlaygroundBindType.CODE_ZONE_SNAPSHOT,
            playground,
            snapshot.getId(),
            newDocker.getId());
        playground.getBindLogs().add(log);
        playgroundRepository.save(playground);
    }

    /**
     * 发送最新Playground信息
     *
     * @param playground playground
     */
    public void notifyPlaygroundInfo(Playground playground) {
        if (playground.getIdeServer() == null) {
            log.warn(
                "Message not send, because playground:{}'s ide server is null",
                playground.getId());
            return;
        }

        DockerContainer container = playground.getDockerContainer();

        String lspUrl = null;
        if (playground.isLspSupported()) {
            lspUrl = urlResourceService.getLspUrlResource(container).getUrl();
        }
        String dockerInfoStr = redisExternalService.getDockerInfo(container.getId());

        String agentServerUrl = urlResourceService.getAgentServerUrlResource(container).getUrl();

        PlaygroundInfoMQMsg message = PlaygroundInfoMsgHelper.get(
            "",
            playground,
            getInitialPathPath(playground),
            systemProperties.getDocker().getStorage().getAppPath(),
            "",
            lspUrl,
            dockerInfoStr,
            container.getDefaultOpenFile(), agentServerUrl);
        mqMessageSender.sendToIdeServer(MsgType.PLAYGROUND_INFO, playground, message);
    }

    /**
     * 发送更新后的Playground信息
     *
     * @param playground      playground
     * @param urldto          urldto
     * @param initialPath     初始化代码路径
     * @param defaultOpenFile
     */
    public void notifyChangePlaygroundInfo(
        Playground playground, URLDTO urldto, String initialPath, String defaultOpenFile) {
        if (playground.getIdeServer() == null) {
            log.warn(
                "Message not send, because playground:{}'s ide server is null",
                playground.getId());
            return;
        }

        DockerContainer container = playground.getDockerContainer();

        String dockerInfoStr = redisExternalService.getDockerInfo(container.getId());

        String agentServerUrl = urlResourceService.getAgentServerUrlResource(container).getUrl();

        PlaygroundInfoMQMsg message = PlaygroundInfoMsgHelper.get(
            "",
            playground,
            initialPath,
            systemProperties.getDocker().getStorage().getAppPath(),
            "",
            urldto.getLspUrl(),
            dockerInfoStr,
            defaultOpenFile, agentServerUrl);

        mqMessageSender.sendToIdeServer(MsgType.CHANGE_PLAYGROUND_INFO, playground, message);
    }

    /**
     * 发送更新中的Playground信息
     *
     * @param playground playground
     */
    public void notifyChangingPlayground(
        Playground playground) {
        if (playground.getIdeServer() == null) {
            log.warn(
                "Message not send, because playground:{}'s ide server is null",
                playground.getId());
            return;
        }

        mqMessageSender.sendToIdeServer(MsgType.CHANGING_PLAYGROUND, playground, new BaseMQMsg());
    }

    private String getInitialPathPath(Playground playground) {
        switch (playground.getBindType()) {
            case CODE_ZONE_SNAPSHOT -> {
                CodeZoneSnapshot codeZoneSnapshot = playground.getCodeZoneSnapshot();
                return codeZoneSnapshot.getPath();
            }
        }
        return null;
    }

    /**
     * 销毁不活跃playground的docker容器
     */
    public void destroyDeadPlaygrounds() {
        // fixme: 查询数据量太大
        List<Playground> playgroundList = playgroundRepository
            .findAllByStatusAndLastModifiedDateLessThanAndDockerContainerDeletedFalse(
                PlaygroundStatus.INACTIVE,
                DateUtils.addDays(
                    new Date(),
                    -1 * systemProperties.getPlayground().getDestroyDays()));
        for (Playground playground : playgroundList) {
            dockerService.remove(playground.getDockerContainer());
        }
    }

    public void download(
        Playground playground, HttpServletRequest request, HttpServletResponse response) {
        String filePath = null;
        List<String> ignoreList = new ArrayList<>();
        String[] strArray = null;
        switch (playground.getBindType()) {
            case CODE_ZONE_SNAPSHOT -> filePath = playground.getDockerContainer().getRootPath();
            case CODE_ZONE -> filePath = playground.getCodeZone().getRootPath();
        }
        if (playground.getEnvironment().getFileTreeIgnore() != null) {
            strArray = playground.getEnvironment().getFileTreeIgnore().split(";");
        }
        if (strArray != null) {
            ignoreList = Arrays.asList(strArray);
        }
        List<FileDownloadBean> fileDownloadBeans = new ArrayList<>();
        fileDownloadBeans.add(
            new FileDownloadBean(
                playground.getEnvironment().getName(),
                DirUtil.join(filePath, Constant.CODE_ZONE_SOURCE_PATH),
                ignoreList));
        this.fileService.download(
            fileDownloadBeans, systemProperties.getCodeZone().getDownload(), request, response);
    }

    /**
     * 销毁不活跃playground的docker容器
     */
    @Transactional
    public void destroyUnitTestPlaygrounds() {
        List<UnitTestRunLog> unitTestRunLogList = this.unitTestRunLogRepository.findListByEndOfRunFalse();
        for (UnitTestRunLog unitTestRunLog : unitTestRunLogList) {
            if (unitTestRunLog.getDockerContainer() == null) {
                unitTestRunLog.setEndOfRun(true);
                this.unitTestRunLogRepository.save(unitTestRunLog);
                continue;
            }
            final Playground playground = this.playgroundRepository.findByDockerContainerId(
                unitTestRunLog.getDockerContainer().getId());
            if ((unitTestRunLog.getCreatedDate().getTime() + Constant.HOUR_TO_MILLISECOND) < System
                .currentTimeMillis()) {
                if (playground == null) {
                    this.stopOldDocker(playground);
                }
                unitTestRunLog.setEndOfRun(true);
                this.unitTestRunLogRepository.save(unitTestRunLog);
            }
        }
    }

    /**
     * 以JSON格式批量获取文件内容
     *
     * @param playground          playground
     * @param codeZoneFileListDTO 文件名（路径）集合
     * @return 文件内容集合
     */
    public List<CodeZoneFileDTO> downloadFilesForJson(
        Playground playground, CodeZoneFileListDTO codeZoneFileListDTO) {
        if (playground.getStatus() == PlaygroundStatus.EMPTY) {
            throw new CustomRuntimeException("playground.is.empty", "请先绑定资源");
        }
        String filePath = switch (playground.getBindType()) {
            case CODE_ZONE_SNAPSHOT, CODE_ZONE_DUPLICATE -> playground
                .getDockerContainer()
                .getRootPath();
            case CODE_ZONE -> playground.getCodeZone().getRootPath();
            default -> null;
        };
        codeZoneFileListDTO
            .getFileList()
            .forEach(
                item -> item.setFileContent(
                    FileUtil.getContent(
                        DirUtil.join(
                            filePath,
                            Constant.CODE_ZONE_SOURCE_PATH,
                            item.getFilePath()))));
        return codeZoneFileListDTO.getFileList();
    }

    /**
     * 运行命令返回状态和结果
     *
     * @param playground
     * @return
     */
    public CmdRunIdDTO runCmd(Playground playground, CmdDTO cmdDTO) {
        return this.dockerService.runCmd(playground, cmdDTO);
    }

    public void stopRunCmd(Playground playground, String runId) {
        this.dockerService.stopRunCmd(playground, runId);
    }

    public void updateStatus(Playground playground, PlaygroundStatus status) {
        this.getOptional(playground.getId())
            .ifPresent(
                p -> {
                    p.setStatus(status);
                    playgroundRepository.save(p);
                });
    }

    /**
     * 修改ideServer
     *
     * @param playground    playground
     * @param ideServerCode IDE Server CODE
     * @return
     */
    public Playground updateIdeServer(Playground playground, String ideServerCode) {
        IDEServer ideServer = this.ideServerRepository.findByCode(ideServerCode);
        return this.updateIdeServer(playground, ideServer);
    }

    /**
     * 修改ideServer
     *
     * @param playground playground
     * @param ideServer  ideServer
     * @return
     */
    public Playground updateIdeServer(Playground playground, IDEServer ideServer) {
        playgroundRepository.updateIdeServer(
            ideServer.getId(), Collections.singletonList(playground.getId()));
        playground.setIdeServer(ideServer);
        ideServerService.changeIdeServerToDocker(playground);
        return playground;
    }

    public void importJson(Playground playground, PlaygroundImportJsonDTO dto) {
        String path = switch (playground.getBindType()) {
            case CODE_ZONE -> playground.getCodeZone().getRootPath();
            case CODE_ZONE_SNAPSHOT, CODE_ZONE_DUPLICATE -> playground
                .getDockerContainer()
                .getRootPath();
        };
        dto.getCodeZoneImportFileListDTOList()
            .forEach(
                item -> this.fileUtilService.write(
                    path
                        + Constant.CODE_ZONE_SOURCE_PATH
                        + "/"
                        + item.getFileName(),
                    item.getFileContent()));
    }

    /**
     * 预创建多个Playground
     *
     * @param tenantId           租户ID
     * @param codeZoneSnapshotId 代码空间快照ID
     * @param expectedValue      预创建的Playground数量
     * @return 创建成功的Playground ID集合
     * @see PlaygroundService#activateInBackground(List, HttpServletRequest) 创建完成后的异步激活
     */
    @Transactional(rollbackFor = {})
    public List<Playground> preCreate(
        Long tenantId, Long codeZoneSnapshotId, Integer expectedValue,String defaultOpenFile) {
        List<Playground> playgrounds = new ArrayList<>();
        for (int i = 0; i < expectedValue; i++) {
            Playground playground = this.create(tenantId, PlaygroundBindType.CODE_ZONE_SNAPSHOT, new HashMap<>());
            this.switchToSnapshot(playground.getId(), codeZoneSnapshotId, defaultOpenFile);
            playgrounds.add(playground);
        }
        return playgrounds;
    }

    /**
     * 异步激活多个Playground
     *
     * @param playgrounds Playground集合
     * @param request     http请求
     */
    @Async
    public void activateInBackground(List<Playground> playgrounds, HttpServletRequest request) {
        for (Playground p : playgrounds) {
            try {
                self.activateInBackground(p);
            } catch (PlaygroundActiveException
                     | ResourceNotEnoughException
                     | NoDockerServerException
                     | DockerCreateException
                     | DockerStartException
                     | NoIDEServerException e) {
                log.error("预创建容器激活失败", e);
                exceptionMsgBot.send(new ExceptionMsgBotBean(e, "预创建容器,激活失败", request));
            }
        }
    }

    /**
     * 异步激活每个Playground
     *
     * @param playground
     * @throws PlaygroundActiveException
     * @throws DockerCreateException
     * @throws ResourceNotEnoughException
     * @throws NoDockerServerException
     * @throws DockerStartException
     */
    @Async(value = AsyncConfig.ACTIVATE_THREAD_POOL)
    public void activateInBackground(Playground playground)
        throws PlaygroundActiveException, ResourceNotEnoughException, NoDockerServerException,
        DockerCreateException, DockerStartException, NoIDEServerException {
        IDEServer ideServer = ideServerSelector.select();
        if (ideServer == null) {
            throw new PlaygroundActiveException(PlaygroundActiveResult.BUSY, "找不到可用的IDE Server");
        }
        playground.setIdeServer(ideServer);
        playgroundRepository.save(playground);
        log.debug("playground.save:" + playground.getId());

        // 生成容器启动所需的环境变量
        AgentConfigBean config = dockerAgentService.getConfig(playground);
        // 标记为是预创建的容器， 在分配DockerServer的逻辑有特殊处理，激活成功后也不设置超时时间。
        DockerContainer docker = playground.getDockerContainer();
        docker.setPreCreate(true);
        docker = dockerService.createContainer(docker, config);
        dockerService.startContainer(docker);
    }

    public Playground getPlaygroundByDockerId(Long dockerId) {
        PlaygroundBindLog playgroundBindLog = bindLogRepository.findFirstByDockerContainerIdOrderByIdDesc(dockerId);
        return playgroundBindLog.getPlayground();
    }

    public void delete(Playground playground, List<Long> dockerId) {
        List<PlaygroundBindLog> allByPlayground = bindLogRepository.findAllByPlayground(playground);
        if (allByPlayground == null) {
            throw new CustomRuntimeException("playground non-existent!");
        }
        List<Long> rmDockerId;
        if (CollectionUtils.isNotEmpty(dockerId)) {
            rmDockerId = allByPlayground.stream().filter(item -> {
                return !dockerId.contains(item.getDockerContainerId());
            }).map(item -> item.getDockerContainerId()).collect(Collectors.toList());
        } else {
            rmDockerId = allByPlayground.stream().map(item -> item.getDockerContainerId()).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(rmDockerId)) {
            deleteDocker(rmDockerId);
        }

    }

    public void deleteDocker(List<Long> dockerId) {
        List<DockerContainer> byIds = dockerContainerService.getByIds(dockerId);
        if (CollectionUtils.isNotEmpty(byIds)) {
            for (DockerContainer dockerContainer : byIds) {
                dockerService.delete(dockerContainer);
            }
            return;
        }
        throw new CustomRuntimeException("DockerContainer non-existent!");
    }

    /**
     * 删除playground
     *
     * @param playground
     */
    public void delete(Playground playground) {
        if (playground.getStatus() == PlaygroundStatus.EMPTY) {
            return;
        }
        List<PlaygroundBindLog> playgroundBindLogs = bindLogRepository.findAllByPlayground(playground);
        playgroundBindLogs.forEach(
            playgroundBindLog -> {
                if (playground.getBindType() == PlaygroundBindType.CODE_ZONE) {
                    CodeZone codeZone = codeZoneService.getCodeZone(playgroundBindLog.getBindObjectId());
                    // 删除codeZone
                    codeZoneService.delete(codeZone);
                }
                DockerContainer dockerContainer = dockerContainerService.getById(
                    playgroundBindLog.getDockerContainerId());
                // 删除docker 和中间件
                try {
                    // 删除docker 和中间件
                    dockerService.stop(dockerContainer);
                    dockerService.remove(dockerContainer);
                } catch (Exception e) {
                    log.error("docker rm fail", e);
                }
                StorageService storageService = storageProvider.getStorageService(dockerContainer.getNfsNodeId());
                // 删除 docker 所挂载的文件
                String cmd = systemProperties
                    .getBtrfs()
                    .getRemoveCmd()
                    .replace(
                        "{target}",
                        dockerContainer
                            .getRootPath()
                            .replace(
                                storageService.getPath()+Constant.CODE_ZONE_PATH,
                                systemProperties
                                    .getCodeZone()
                                    .getPathInBtrfs())
                            .replace(
                                systemProperties
                                    .getDocker()
                                    .getStorage()
                                    .getNfsPath(),
                                systemProperties
                                    .getDocker()
                                    .getStorage()
                                    .getFileServerPath()));

                storageService.execCommandBtrfs(cmd);
            });

        // 结束Playground
        playground.setStatus(PlaygroundStatus.END);
        playgroundRepository.save(playground);
    }

    public void updateResourcesLimit(Playground playground, ResourcesLimit resourcesLimit) {
        if (playground.getDockerContainer().getResourcesLimit().getRAM() == resourcesLimit.getRAM()) {
            return;
        }
        switch (playground.getBindType()) {
            case CODE_ZONE, CODE_ZONE_DUPLICATE -> codeZoneService.updateResources(
                playground.getCodeZone(), resourcesLimit);
            case CODE_ZONE_SNAPSHOT -> codeZoneSnapshotService.updateResources(
                playground.getCodeZoneSnapshot(), resourcesLimit);
        }
        if (PlaygroundStatus.ACTIVE != playground.getStatus()) {
            return;
        }
        if (playground.getDockerContainer() != null) {
            Long mem = resourcesLimit.getRAM()
                - playground.getDockerContainer().getResourcesLimit().getRAM();
            boolean requestResource = dockerService.updateResource(
                playground.getDockerContainer(), resourcesLimit, mem);
            try {
                if (requestResource) {
                    // 服务器资源充足直接修改 docker 信息
                    dockerExternalService.update(playground.getDockerContainer());
                } else {
                    pause(playground, "updateResourcesLimit");
                    redisExternalService.removePlaygroundActive(playground.getId());
                }
            } catch (NoDockerServerException e) {
                log.warn("未更新");
            } catch (Exception e) {
                exceptionMsgBot.send(new ExceptionMsgBotBean(e, "更新资源失败！"));
                // 更新失败回收资源
                dockerService.rollbackResource(playground.getDockerContainer(), mem);
            }
        }
    }

    public CmdRunIdDTO backgroundRunCmd(Playground playground, CmdDTO cmdDTO) {
        return this.dockerService.backgroundRunCmd(playground, cmdDTO);
    }

    public void heartBeatFail(Long playgroundId) {
        Playground playground = get(playgroundId);
        log.info("heartBeatFail-心跳失败;playgroundId:{}, playground_status: {}", playgroundId, playground.getStatus());
        if (playground.getStatus() == PlaygroundStatus.INACTIVE) {
            log.debug("heartBeatFail-心跳失败1;playgroundId:{}, playground_status: {}", playgroundId, playground.getStatus());
            return;
        }

        PlaygroundHeartBeatResultMQMsg result = new PlaygroundHeartBeatResultMQMsg();
        result.setDockerStatus(com.dao42.paas.common.enums.DockerStatus.FAIL);
        // 如果playground出现连续三次心跳失败，则停止容器 playground 设为 inactive 客户端重新激活
        Long playgroundHeartbeatRetry =
            redisExternalService.getPlaygroundHeartbeatRetry(playgroundId);
        if (playgroundHeartbeatRetry == null || playgroundHeartbeatRetry.compareTo(RETRY_LIMIT) < 0) {
            Long retryCount = redisExternalService.incrementPlaygroundHeartBeatRetry(playgroundId);
            log.info("playground心跳失败重试：playground:{},retry : {} ",playgroundId, retryCount);
            redisExternalService.setPlaygroundHeartbeatWait(playgroundId);
            this.mqMessageSender.sendToDocker(MsgType.PLAYGROUND_HEART_BEAT, playground, new BaseMQMsg());
        }else {
            log.info("heartBeatFail-心跳失败;playgroundId:{}, pause playground", playgroundId);
            ExceptionMsgBotBean exceptionMsgBotBean = new ExceptionMsgBotBean();
            exceptionMsgBotBean.setText("docker.heart.fail:" + playground.getDockerContainer().getId());
            exceptionMsgBot.send(exceptionMsgBotBean);

            redisExternalService.removePlaygroundHeartbeatRetry(playgroundId);
            this.pause(playground, "heartBeatFail");
            mqMessageSender.sendToIdeServer(MsgType.PLAYGROUND_HEART_BEAT, playground, result);
        }
    }

    public void notifyPlaygroundUrlResource(Playground playground) {
        if (playground.getIdeServer() == null) {
            log.warn(
                "DockerScheduling-Active, Message not send, because playground:{}'s ide server is null",
                playground.getId());
            return;
        }

        String lspUrl = null;
        if (playground.isLspSupported()) {
            lspUrl = urlResourceService.getLspUrlResource(playground.getDockerContainer()).getUrl();
        }
        PlaygroundUrlResourceMQMsg msg = new PlaygroundUrlResourceMQMsg();
        msg.setApp("");
        msg.setLsp(lspUrl);
        msg.setAgentServer(urlResourceService.getAgentServerUrlResource(playground.getDockerContainer()).getUrl());
        msg.setDockerId(String.valueOf(playground.getDockerContainer().getId()));
        mqMessageSender.sendToIdeServer(MsgType.PLAYGROUND_URL_RESOURCE, playground, msg);
    }

    public void notifyPlaygroundInfoByCodeZone(CodeZone codeZone){
        Playground playground = playgroundRepository.findByBindTypeAndBindObject(
            PlaygroundBindType.CODE_ZONE, codeZone.getId());
        if (playground != null ){
            notifyPlaygroundInfo(playground);
        }
    }
}
