package com.dao42.paas.service.scheduler;

import com.dao42.paas.common.enums.DockerServerStatus;
import com.dao42.paas.exception.resource.NoDockerServerException;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.docker.DockerServer;
import com.dao42.paas.redis.RedisRedissonUtil;
import com.dao42.paas.repository.image.ThreadImageRepository;
import com.dao42.paas.service.docker.DockerServerService;
import com.dao42.paas.service.image.ImageCacheService;
import com.dao42.paas.service.impl.MemoryDockerServerSelector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 基于镜像缓存的智能Docker服务器选择器
 * 优先选择已缓存目标镜像的服务器，提升容器启动速度
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ImageAwareDockerServerSelector extends MemoryDockerServerSelector {

    private final DockerServerService dockerServerService;
    private final ImageCacheService imageCacheService;
    private final ThreadImageRepository threadImageRepository;

    public ImageAwareDockerServerSelector(StringRedisTemplate stringRedisTemplate,
                                        DockerServerService dockerServerService,
                                        @Lazy ImageAwareDockerServerSelector serverSelector,
                                        RedisRedissonUtil redisRedissonUtil,
                                        ImageCacheService imageCacheService,
                                        ThreadImageRepository threadImageRepository) {
        super(stringRedisTemplate, dockerServerService, serverSelector, redisRedissonUtil);
        this.dockerServerService = dockerServerService;
        this.imageCacheService = imageCacheService;
        this.threadImageRepository = threadImageRepository;
    }

    /**
     * 智能选择Docker服务器
     * 优先级：
     * 1. 有目标镜像缓存的服务器
     * 2. 资源充足的服务器
     * 3. 负载均衡
     */
    @Override
    public void select(DockerContainer dockerContainer) throws NoDockerServerException {
        try {
            // 确定目标镜像
            String targetImage = determineTargetImage(dockerContainer);
            if (targetImage == null) {
                // 没有目标镜像，使用默认选择策略
                super.select(dockerContainer);
                return;
            }

            log.debug("为容器选择服务器，目标镜像: {}", targetImage);

            // 获取所有活跃的服务器
            List<DockerServer> activeServers = dockerServerService.findAll(DockerServerStatus.ACTIVE);
            if (activeServers.isEmpty()) {
                throw new NoDockerServerException("没有可用的Docker服务器");
            }

            // 查找有目标镜像缓存的服务器
            DockerServer bestServer = selectServerWithImageCache(activeServers, targetImage, dockerContainer);
            if (bestServer != null) {
                log.info("选择有镜像缓存的服务器: serverId={}, image={}",
                        bestServer.getId(), targetImage);

                // 设置服务器到容器
                dockerContainer.setDockerServer(bestServer);

                // 记录镜像命中
                imageCacheService.recordImageHit(bestServer.getId().toString(), targetImage);
                return;
            }

            // 没有找到有缓存的服务器，使用默认策略
            super.select(dockerContainer);

            // 异步预拉取镜像到选中的服务器
            if (dockerContainer.getDockerServer() != null) {
                imageCacheService.preloadImage(dockerContainer.getDockerServer().getId().toString(), targetImage);
            }

        } catch (Exception e) {
            log.error("智能服务器选择失败，回退到默认策略", e);
            super.select(dockerContainer);
        }
    }

    /**
     * 确定目标镜像
     */
    private String determineTargetImage(DockerContainer dockerContainer) {
        try {
            // 根据容器类型和项目信息查找最新的Thread镜像
            Long projectId = extractProjectId(dockerContainer);
            Long issueId = extractIssueId(dockerContainer);

            if (projectId != null) {
                if (issueId != null) {
                    // Issue Thread - 查找最新的Issue Thread镜像
                    Optional<String> issueImage = threadImageRepository
                        .findLatestIssueThreadImage(projectId, issueId);
                    if (issueImage.isPresent()) {
                        return issueImage.get();
                    }
                }

                // Root Thread - 查找最新的Root Thread镜像
                Optional<String> rootImage = threadImageRepository
                    .findLatestRootThreadImage(projectId);
                if (rootImage.isPresent()) {
                    return rootImage.get();
                }
            }

            // 没有找到Thread镜像，返回null使用默认策略
            return null;

        } catch (Exception e) {
            log.error("确定目标镜像失败", e);
            return null;
        }
    }

    /**
     * 选择有镜像缓存的最优服务器
     */
    private DockerServer selectServerWithImageCache(List<DockerServer> servers, 
                                                   String targetImage, 
                                                   DockerContainer dockerContainer) {
        DockerServer bestServer = null;
        double bestScore = -1;

        for (DockerServer server : servers) {
            try {
                // 检查服务器是否有目标镜像
                if (!imageCacheService.hasImage(server.getId().toString(), targetImage)) {
                    continue;
                }

                // 检查服务器资源是否充足
                if (!hasEnoughResources(server, dockerContainer)) {
                    continue;
                }

                // 计算服务器评分
                double score = calculateServerScore(server, targetImage, dockerContainer);
                
                if (score > bestScore) {
                    bestScore = score;
                    bestServer = server;
                }

            } catch (Exception e) {
                log.warn("评估服务器失败: serverId={}", server.getId(), e);
            }
        }

        return bestServer;
    }

    /**
     * 计算服务器评分
     */
    private double calculateServerScore(DockerServer server, String targetImage, DockerContainer dockerContainer) {
        double score = 0;

        // 镜像缓存评分 (40%)
        double imageScore = imageCacheService.getImageScore(server.getId().toString(), targetImage);
        score += imageScore * 0.4;

        // 资源评分 (40%)
        double resourceScore = calculateResourceScore(server, dockerContainer);
        score += resourceScore * 0.4;

        // 负载评分 (20%)
        double loadScore = calculateLoadScore(server);
        score += loadScore * 0.2;

        return score;
    }

    /**
     * 计算资源评分
     */
    private double calculateResourceScore(DockerServer server, DockerContainer dockerContainer) {
        try {
            // 简化版本的资源评分
            // 基于服务器的配置信息给出基础评分
            long totalMemory = server.getMemoryMB();
            long totalCpu = server.getCpuCount();

            // 基础评分：根据服务器配置
            double memoryScore = Math.min(100, totalMemory / 1024.0); // 每GB内存得1分，最高100分
            double cpuScore = Math.min(100, totalCpu * 10); // 每核CPU得10分，最高100分

            return (memoryScore + cpuScore) / 2;

        } catch (Exception e) {
            log.warn("计算资源评分失败: serverId={}", server.getId(), e);
            return 50; // 默认评分
        }
    }

    /**
     * 计算负载评分
     */
    private double calculateLoadScore(DockerServer server) {
        try {
            // 简化版本的负载评分
            // 基于服务器状态给出评分
            if (server.getStatus() == DockerServerStatus.ACTIVE) {
                return 80; // 活跃服务器得80分
            } else {
                return 20; // 非活跃服务器得20分
            }

        } catch (Exception e) {
            log.warn("计算负载评分失败: serverId={}", server.getId(), e);
            return 50; // 默认评分
        }
    }

    /**
     * 检查服务器资源是否充足
     */
    private boolean hasEnoughResources(DockerServer server, DockerContainer dockerContainer) {
        try {
            // 简化版本的资源检查
            // 只检查服务器是否活跃
            return server.getStatus() == DockerServerStatus.ACTIVE;
        } catch (Exception e) {
            log.warn("检查服务器资源失败: serverId={}", server.getId(), e);
            return false;
        }
    }

    /**
     * 从容器路径中提取项目ID
     */
    private Long extractProjectId(DockerContainer container) {
        // 实现逻辑与ThreadImageLifecycleManager中的getProjectId方法相同
        String rootPath = container.getRootPath();
        if (rootPath != null) {
            String[] parts = rootPath.split("/");
            if (parts.length > 3 && "projects".equals(parts[2])) {
                try {
                    return Long.parseLong(parts[3]);
                } catch (NumberFormatException e) {
                    log.warn("无法从路径中解析项目ID: {}", rootPath);
                }
            }
        }
        return null;
    }

    /**
     * 从容器路径中提取Issue ID
     */
    private Long extractIssueId(DockerContainer container) {
        // 实现逻辑与ThreadImageLifecycleManager中的getIssueId方法相同
        String rootPath = container.getRootPath();
        if (rootPath != null && rootPath.contains("/issues/")) {
            String[] parts = rootPath.split("/");
            for (int i = 0; i < parts.length - 1; i++) {
                if ("issues".equals(parts[i]) && i + 1 < parts.length) {
                    try {
                        return Long.parseLong(parts[i + 1]);
                    } catch (NumberFormatException e) {
                        log.warn("无法从路径中解析Issue ID: {}", rootPath);
                    }
                }
            }
        }
        return null;
    }
}
