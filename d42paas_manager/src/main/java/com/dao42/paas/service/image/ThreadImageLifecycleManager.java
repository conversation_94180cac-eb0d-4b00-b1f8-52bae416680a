package com.dao42.paas.service.image;

import com.dao42.paas.dto.image.CommitOptions;
import com.dao42.paas.enums.ContainerType;
import com.dao42.paas.enums.DockerStatus;
import com.dao42.paas.enums.ThreadImageStatus;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.image.ThreadImage;
import com.dao42.paas.repository.image.ThreadImageRepository;
import com.dao42.paas.service.docker.DockerImageService;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.event.ContainerStoppedEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * Thread镜像生命周期管理器
 * 负责容器停止时自动创建镜像、推送到镜像仓库、更新数据库记录等功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThreadImageLifecycleManager {

    private final DockerImageService dockerImageService;
    private final ImageRegistryService imageRegistryService;
    private final ThreadImageRepository threadImageRepository;
    private final SystemProperties systemProperties;

    /**
     * 容器停止时自动创建镜像 (用户无感)
     */
    @EventListener
    @Async
    public void onContainerStopped(ContainerStoppedEvent event) {
        DockerContainer container = event.getContainer();

        // 只对 STOP_SUCCESS 状态的容器创建镜像
        if (container.getStatus() == DockerStatus.STOP_SUCCESS) {
            createThreadImage(container);
        }
    }

    /**
     * 创建Thread镜像
     */
    private void createThreadImage(DockerContainer container) {
        try {
            // 1. 生成镜像标签
            String imageTag = generateImageTag(container);

            // 2. 创建数据库记录
            ThreadImage threadImage = createThreadImageRecord(container, imageTag);

            // 3. 执行 docker commit
            String imageId = dockerImageService.commit(
                container.getContainerId(),
                imageTag,
                CommitOptions.builder()
                    .author("Clacky PaaS System")
                    .message("Auto-generated thread image")
                    .pause(true) // 暂停容器以确保一致性
                    .build()
            );

            // 4. 更新镜像ID
            threadImage.setImageDigest(imageId);
            threadImage.setStatus(ThreadImageStatus.READY);
            threadImageRepository.save(threadImage);

            // 5. 推送到镜像仓库
            pushImageAsync(imageTag, container, threadImage);

            // 6. 清理本地镜像 (可选)
            if (systemProperties.getImage().isAutoCleanup()) {
                dockerImageService.removeLocal(imageId);
            }

            log.info("Thread 镜像创建完成: container={}, image={}",
                    container.getId(), imageTag);

        } catch (Exception e) {
            log.error("Thread 镜像创建失败: container={}", container.getId(), e);
        }
    }

    /**
     * 生成镜像标签
     */
    private String generateImageTag(DockerContainer container) {
        String registry = systemProperties.getImage().getRegistry().getUrl();

        ContainerType containerType = determineContainerType(container);

        if (containerType == ContainerType.ROOT_THREAD) {
            return String.format("%s/clacky/docker:root-thread-%s-%s",
                registry,
                getProjectId(container),
                System.currentTimeMillis());
        } else {
            return String.format("%s/clacky/docker:issue-thread-%s-%s-%s",
                registry,
                getProjectId(container),
                getIssueId(container),
                System.currentTimeMillis());
        }
    }

    /**
     * 创建Thread镜像数据库记录
     */
    private ThreadImage createThreadImageRecord(DockerContainer container, String imageTag) {
        ThreadImage threadImage = new ThreadImage();
        threadImage.setProjectId(getProjectId(container));
        threadImage.setIssueId(getIssueId(container));
        threadImage.setContainerType(determineContainerType(container));
        threadImage.setImageTag(imageTag);
        threadImage.setUserId(getUserId(container));
        threadImage.setTenantId(container.getTenantId());
        threadImage.setStatus(ThreadImageStatus.BUILDING);
        threadImage.setCreatedAt(LocalDateTime.now());
        
        return threadImageRepository.save(threadImage);
    }

    /**
     * 异步推送镜像
     */
    @Async
    private void pushImageAsync(String imageTag, DockerContainer container, ThreadImage threadImage) {
        try {
            threadImage.setStatus(ThreadImageStatus.PUSHING);
            threadImageRepository.save(threadImage);
            
            imageRegistryService.push(imageTag);
            
            threadImage.setStatus(ThreadImageStatus.AVAILABLE);
            threadImageRepository.save(threadImage);
            
            log.info("镜像推送完成: {}", imageTag);
        } catch (Exception e) {
            threadImage.setStatus(ThreadImageStatus.FAILED);
            threadImage.setBuildLog("Push failed: " + e.getMessage());
            threadImageRepository.save(threadImage);
            
            log.error("镜像推送失败: {}", imageTag, e);
        }
    }

    /**
     * 确定容器类型
     */
    private ContainerType determineContainerType(DockerContainer container) {
        // 根据容器的路径或其他属性判断是Root Thread还是Issue Thread
        // 这里需要根据实际的业务逻辑来实现
        String rootPath = container.getRootPath();
        if (rootPath != null && rootPath.contains("/issue/")) {
            return ContainerType.ISSUE_THREAD;
        }
        return ContainerType.ROOT_THREAD;
    }

    /**
     * 获取项目ID
     */
    private Long getProjectId(DockerContainer container) {
        // 从容器路径或其他属性中提取项目ID
        // 这里需要根据实际的业务逻辑来实现
        String rootPath = container.getRootPath();
        // 假设路径格式为: /data/projects/{projectId}/...
        if (rootPath != null) {
            String[] parts = rootPath.split("/");
            if (parts.length > 3 && "projects".equals(parts[2])) {
                try {
                    return Long.parseLong(parts[3]);
                } catch (NumberFormatException e) {
                    log.warn("无法从路径中解析项目ID: {}", rootPath);
                }
            }
        }
        return null;
    }

    /**
     * 获取Issue ID
     */
    private Long getIssueId(DockerContainer container) {
        // 从容器路径或其他属性中提取Issue ID
        String rootPath = container.getRootPath();
        // 假设路径格式为: /data/projects/{projectId}/issues/{issueId}/...
        if (rootPath != null && rootPath.contains("/issues/")) {
            String[] parts = rootPath.split("/");
            for (int i = 0; i < parts.length - 1; i++) {
                if ("issues".equals(parts[i]) && i + 1 < parts.length) {
                    try {
                        return Long.parseLong(parts[i + 1]);
                    } catch (NumberFormatException e) {
                        log.warn("无法从路径中解析Issue ID: {}", rootPath);
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取用户ID
     */
    private Long getUserId(DockerContainer container) {
        // 这里需要根据实际的业务逻辑来获取用户ID
        // 可能需要从容器的其他属性或关联的实体中获取
        return container.getTenantId(); // 临时使用tenantId
    }
}
