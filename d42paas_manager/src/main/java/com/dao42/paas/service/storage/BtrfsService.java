package com.dao42.paas.service.storage;

import com.dao42.paas.common.constants.RedisPrefix;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.framework.alert.ExceptionMsgBot;
import com.dao42.paas.framework.alert.ExceptionMsgBotBean;
import com.dao42.paas.redis.RedisRedissonUtil;
import com.dao42.paas.utils.FileUtil;
import com.github.woostju.ssh.SshClientConfig;
import com.github.woostju.ssh.SshResponse;
import com.github.woostju.ssh.pool.SshClientWrapper;
import com.github.woostju.ssh.pool.SshClientsPool;
import io.opentelemetry.instrumentation.annotations.SpanAttribute;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RRateLimiter;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import java.util.concurrent.TimeUnit;


/**
 * Btrfs业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BtrfsService implements StorageService{

    private final SystemProperties systemProperties;
    private final SshClientsPool pool;
    private final ExceptionMsgBot bot;
    private final RedisRedissonUtil redisRedissonUtil;

    @Override
    public String getPath() {
        return "/app/data";
    }

    @Override
    public Long getNfsNodeId() {
        return 0L;
    }

    @WithSpan
    @Override
//    @Limit(key = "btrfs", permitsPerSecond = 10.0)
    public boolean execCommandBtrfs(@SpanAttribute("btrfs.cmd") String cmd) {
        //获取令牌桶
        RRateLimiter rateLimiter = redisRedissonUtil.getRateLimiter(systemProperties.getBtrfs().getSnapshotRateLimit(),1L,RedisPrefix.REDISSON_REDIS_BTRFS);
        if (!rateLimiter.tryAcquire(1000 * systemProperties.getBtrfs().getSnapshotWaitLimit(), TimeUnit.MILLISECONDS)) {
            return false;
        }
        log.debug("BtrfsService获取令牌成功", RedisPrefix.REDISSON_REDIS_BTRFS);
        log.info("cmd: {}", cmd);
        if (systemProperties.isLocalDebug()) {
            return execCommandLocal(cmd);
        }
        if (StringUtils.hasText(systemProperties.getBtrfs().getCheckDiskSnapshotLockFile()) && FileUtil.exist(
            systemProperties.getBtrfs().getCheckDiskSnapshotLockFile())) {
            log.info("文件正在备份");
            return false;
        }
        SshClientConfig clientConfig = new SshClientConfig(systemProperties.getBtrfs().getHost(),
            systemProperties.getBtrfs().getPort(), systemProperties.getBtrfs().getUserName(),
            null, systemProperties.getBtrfs().getKeyPath());
        SshClientWrapper client = pool.client(clientConfig);
        // 命令执行超时20s
        SshResponse sshResponse = client.executeCommand(cmd, 60);

        log.debug("SSH command execute output: " + String.join("; ", sshResponse.getStdout()));
        if (sshResponse.getCode() != 0) {
            Exception exception = sshResponse.getException();
            bot.send(new ExceptionMsgBotBean(exception, exception.getMessage(), null));
            log.error("SSH command execute failed, code:" + sshResponse.getCode(), exception);
            if (null != exception && null != exception.getMessage() && exception.getMessage().toLowerCase().contains("broken")){
                try {
                    pool.invalidateObject(clientConfig,client);
                    pool.addObject(clientConfig);
                } catch (Exception e) {
                    log.debug("update ssh pool connection obj failed :" , e );
                }
            }
            return false;
        }
        return true;
    }

    public boolean execCommandLocal(String cmd) {
        Process p = null;
        try {
            String[] cmdArray = cmd.split(" ");
            p = Runtime.getRuntime().exec(cmdArray);
            log.debug("cmd:{}", cmd);
            int result = p.waitFor();
            if (result != 0) {
                log.error("cmd:{},result:{}", cmd, result);
            }
            return result == 0;
        } catch (Exception e) {
            log.error("exec shell cmd fail", e);
            return false;
        } finally {
            if (p != null) {
                p.destroy();
            }
        }
    }

}