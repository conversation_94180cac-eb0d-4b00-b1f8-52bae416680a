package com.dao42.paas.service.docker;

import com.dao42.paas.common.bean.AgentConfigBean;
import com.dao42.paas.common.constants.MsgType;
import com.dao42.paas.common.constants.SystemConsts;
import com.dao42.paas.common.enums.PlaygroundActiveResult;
import com.dao42.paas.common.message.PlaygroundActiveResultMQMsg;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.constants.Constant;
import com.dao42.paas.enums.DockerRunCmdEnum;
import com.dao42.paas.exception.docker.DockerRemoveException;
import com.dao42.paas.exception.docker.DockerStartException;
import com.dao42.paas.exception.docker.DockerStopException;
import com.dao42.paas.exception.resource.NoDockerServerException;
import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.model.Environment;
import com.dao42.paas.model.EnvironmentVer;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.docker.DockerDiskResource;
import com.dao42.paas.model.docker.DockerServer;
import com.dao42.paas.model.resource.DiskResource;
import com.dao42.paas.rabbit.MQMessageSender;
import com.dao42.paas.repository.PlaygroundRepository;
import com.dao42.paas.repository.docker.DockerDiskResourceRepository;
import com.dao42.paas.repository.resource.DiskResourceRepository;
import com.dao42.paas.service.RedisExternalService;
import com.dao42.paas.service.RunCmdCallBackService;
import com.dao42.paas.service.docker.handler.BackgroundRunCmdHandler;
import com.dao42.paas.utils.FileUtil;
import com.dao42.paas.utils.JsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.command.CreateContainerCmd;
import com.github.dockerjava.api.command.CreateContainerResponse;
import com.github.dockerjava.api.command.ExecCreateCmdResponse;
import com.github.dockerjava.api.command.InspectContainerResponse;
import com.github.dockerjava.api.command.PullImageResultCallback;
import com.github.dockerjava.api.command.WaitContainerResultCallback;
import com.github.dockerjava.api.exception.ConflictException;
import com.github.dockerjava.api.exception.NotFoundException;
import com.github.dockerjava.api.exception.NotModifiedException;
import com.github.dockerjava.api.model.AuthConfig;
import com.github.dockerjava.api.model.Bind;
import com.github.dockerjava.api.model.BlkioRateDevice;
import com.github.dockerjava.api.model.ExposedPort;
import com.github.dockerjava.api.model.HostConfig;
import com.github.dockerjava.api.model.LogConfig;
import com.github.dockerjava.api.model.LogConfig.LoggingType;
import com.github.dockerjava.api.model.PortBinding;
import com.github.dockerjava.api.model.Ports.Binding;
import com.github.dockerjava.core.command.ExecStartResultCallback;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class DockerExternalService {

    private final SystemProperties systemProperties;

    private final DockerClientFactory dockerClientFactory;

    private final DockerDiskResourceRepository dockerDiskResourceRepository;

    private final DiskResourceRepository diskResourceRepository;

    private final ObjectMapper objectMapper;

    private final RedisExternalService redisExternalService;

    private final MQMessageSender mqMessageSender;

    private final PlaygroundRepository playgroundRepository;

    /**
     * 创建容器
     *
     * @param docker docker容器
     * @param config 配置参数
     * @return 容器的CONTAINER ID
     */
    public String create(DockerContainer docker, AgentConfigBean config) throws NoDockerServerException {
        DockerClient dockerClient = dockerClientFactory.get(docker.getDockerServer());
        Map<String, Object> configMap = objectMapper.convertValue(config, Map.class);

        log.debug("DockerScheduling-Active begin, 创建go agent主容器, docker_id:{}; docker_server_id:{}",
            docker.getId(), docker.getDockerServer().getId());

        /* 环境变量 */
        List<String> envList = configMap.entrySet().stream().filter(e -> e.getValue() != null)
            .map(e -> e.getKey() + "=" + e.getValue()).collect(Collectors.toList());

        /* 资源限制 */
        HostConfig hostConfig = new HostConfig();

        // cpu限制 https://docs.docker.com/config/containers/resource_constraints/#configure-the-default-cfs-scheduler
        hostConfig.withCpuShares(1024);
        hostConfig.withNanoCPUs(config.getDockerCpuCount() * 1000000000);

        // 内存限制 https://docs.docker.com/config/containers/resource_constraints/#limit-a-containers-access-to-memory
        // memory-swap == memory + swap; (swap == 0 ? 禁止swap:可用swap)
        hostConfig.withMemoryReservation(1024L * 1024 * docker.getResourcesLimit().getRAM())
            .withMemory(1024L * 1024 * docker.getResourcesLimit().getRAM())
            .withMemorySwap(1024L * 1024 * docker.getResourcesLimit().getRAM());
        hostConfig.withOomKillDisable(true);
        //  log限制
        hostConfig.withLogConfig(new LogConfig(LoggingType.DEFAULT, Collections.singletonMap("max-size", "10m")));

        // io 限制
        List<BlkioRateDevice> blkioDeviceIOps = new ArrayList<>();
        BlkioRateDevice blkioRateDevice = new BlkioRateDevice();
        blkioRateDevice.withPath("/dev/nvme0n1");
        blkioRateDevice.withRate(systemProperties.getDocker().getLimit().getIops());
        blkioDeviceIOps.add(blkioRateDevice);
        hostConfig.withBlkioDeviceReadIOps(blkioDeviceIOps);
        hostConfig.withBlkioDeviceWriteIOps(blkioDeviceIOps);
        // 吞吐
        List<BlkioRateDevice> blkioDeviceBps = new ArrayList<>();
        BlkioRateDevice blkioRateDeviceBps = new BlkioRateDevice();
        blkioRateDeviceBps.withPath("/dev/nvme0n1");
        blkioRateDeviceBps.withRate(systemProperties.getDocker().getLimit().getBandwidth() * 1024 * 1024L);
        blkioDeviceBps.add(blkioRateDeviceBps);
        hostConfig.withBlkioDeviceWriteBps(blkioDeviceBps);
        hostConfig.withBlkioDeviceReadBps(blkioDeviceBps);

        //磁盘限制
//        Map<String, String> map = new HashMap<>();
//        map.put("size", systemProperties.getDocker().getLimit().getStorageSize());
//        hostConfig.withStorageOpt(map);
        // TODO: 带宽限制

        // 文件存储配置

        // 端口绑定配置
        hostConfig.withPortBindings(this.generatePortBindList(docker));
        //hostConfig.withAutoRemove(true);
        // todo： 特权模式是为了在容器中mount nix overlay目录 @see docker/dockerImage/run.sh:7
        // 可以使用--mount替换 https://docs.docker.com/storage/volumes/#choose-the--v-or---mount-flag
        // hostConfig.withPrivileged(true);
        // 文件挂载配置
        hostConfig.withBinds(this.generateBindList(docker, config.getPaasLanguagePackage()));
        // 容器名称
        String containerName = systemProperties.getDocker().getPrefixName() + "-" + docker.getId();
        // 创建容器命令
        CreateContainerCmd createCmd =
            dockerClient
                .createContainerCmd(docker.getImage())
                .withHostConfig(hostConfig)
                .withUser(Constant.DOCKER_USER_NAME)
                .withTty(true)
                .withName(containerName)
                .withHostName(Constant.DOCKER_HOST_NAME)
                .withEnv(envList)
                .withEntrypoint("/agent/agent");
        File initFlagFile =new File(docker.getRootPath()+Constant.CODE_ZONE_DEPENDENCY_PATH + "/flags/init");
        if (initFlagFile.exists()){
            createCmd.withEntrypoint("/usr/bin/tini", "--", "/agent/agent");
        }



        /* 创建容器 */
        try {
            log.debug("DockerScheduling-Active, Create container, image: {}, hostConfig: {}, containerName: {}, envList: {}",
                docker.getImage(), hostConfig, containerName, envList);
            CreateContainerResponse response = createCmd.exec();
            return response.getId();
        } catch (ConflictException e) {
            log.debug("DockerScheduling-Active, Create container name conflict", e);
            // 创建容器名称冲突，移除旧容器后重试
            dockerClient.removeContainerCmd(containerName).withForce(true).exec();
            CreateContainerResponse response = createCmd.exec();
            return response.getId();
        } catch (NotFoundException e) {
            log.debug("DockerScheduling-Active, Create container image not found", e);
            try {
                Playground playground = playgroundRepository.findByDockerContainerId(docker.getId());
                PlaygroundActiveResultMQMsg resultMsg = new PlaygroundActiveResultMQMsg();
                resultMsg.setReason(PlaygroundActiveResult.DOCKER_IMAGE_NOT_READY);
                resultMsg.setError(e.getMessage());
                if (playground != null) {
                    mqMessageSender.sendToIdeServer(MsgType.ACTIVE, playground, resultMsg);
                }
            } catch (Exception ex) {
                log.error("DockerScheduling-Active, Create container container image pull, send active", ex);
            }

            try {
                pullImage(docker.getImage(), dockerClient);
                // 拉取镜像成功，创建容器
                CreateContainerResponse response = createCmd.exec();
                return response.getId();
            } catch (InterruptedException ex) {
                log.error("DockerScheduling-Active, Create container container image pull error", e);
                throw e;
            }
        } catch (Exception e) {
            log.error("DockerScheduling-Active, Create container error", e);
            throw e;
        }
    }

    /**
     * 拉取镜像
     *
     * @param image        镜像名
     * @param dockerClient Docker客户端
     * @throws InterruptedException
     */
    private void pullImage(String image, DockerClient dockerClient) throws InterruptedException {
        PullImageResultCallback callback = new PullImageResultCallback();
        dockerClient
            .pullImageCmd(image)
            .withAuthConfig(
                new AuthConfig()
                    .withRegistryAddress(systemProperties.getDockerImageRepository().getAddr())
                    .withUsername(systemProperties.getDockerImageRepository().getUser())
                    .withPassword(systemProperties.getDockerImageRepository().getPasswd()))
            .exec(callback);
        callback.awaitCompletion();
    }

    /**
     * 启动容器
     *
     * @param docker docker容器
     * @throws DockerStartException 启动失败异常
     */
    public void start(DockerContainer docker) throws NoDockerServerException {
        if (docker.getContainerId() == null) {
            return;
        }
        DockerClient dockerClient = dockerClientFactory.get(docker.getDockerServer());
        log.info("DockerScheduling-Active, 启动容器:{};{}",docker.getId(),docker.getDockerServer().getId());
        /* 启动容器 */
        try {
            dockerClient.startContainerCmd(docker.getContainerId()).exec();
            InspectContainerResponse response = dockerClient.inspectContainerCmd(docker.getContainerId()).exec();
            Map<ExposedPort, Binding[]> exposedPorts = response.getNetworkSettings().getPorts().getBindings();
            exposedPorts.forEach((key, value) -> {
                if ((value == null) || (value.length == 0)) {
                    return;
                }

                Binding binding = value[0];
                if (binding == null) {
                    return;
                }

                String hostPort = binding.getHostPortSpec();
                String hostIp = binding.getHostIp();

                if (hostPort == null || hostIp == null) {
                    return;
                }

                // LSP端口
                if (docker.isLspSupported() && key.getPort() == SystemConsts.LSP_PORT) {
                    docker.setLspPort(Integer.parseInt(hostPort));
                    docker.setDockerAddress(hostIp);
                }
                // 服务端口
                if (key.getPort() == docker.getEnvironmentVer().getEnvironment().getPort()) {
                    docker.setHostPort(Integer.parseInt(hostPort));
                    docker.setDockerAddress(hostIp);
                }
                // SSH 端口
                if (key.getPort() == 22 ) {
                    docker.setSshPort(Integer.parseInt(hostPort));
                    docker.setDockerAddress(hostIp);
                }
                // 容器内容项目运行代理端口号
                if (key.getPort() == SystemConsts.PROJECT_WEB_PORT) {
                    docker.setProjectWebPort(Integer.parseInt(hostPort));
                    docker.setDockerAddress(hostIp);
                }
                // 用户容器其唯一入口
                if (key.getPort() == SystemConsts.AGENT_SERVER_PORT) {
                    docker.setAgentServerPort(Integer.parseInt(hostPort));
                    docker.setDockerAddress(hostIp);
                }
            });
        } catch (NotModifiedException e) {
            // 容器已启动，不处理
            log.debug("DockerScheduling-Active, Start container already started", e);
        } catch (NotFoundException e) {
            // 容器不存在，可能被手动删除了，要重建
            log.debug("DockerScheduling-Active, Start container not found", e);
            throw e;
        }
    }

    /**
     * 停止(5秒后kill)容器
     *
     * @param docker 容器
     * @throws DockerStopException 停止异常
     */
    public void stop(DockerContainer docker) {
        if (docker.getContainerId() == null) {
            return;
        }
        log.info("停止docker:{}",docker.getId());
        try {
            DockerClient dockerClient = dockerClientFactory.get(docker.getDockerServer());
            dockerClient.stopContainerCmd(docker.getContainerId()).withTimeout(5).exec();
        } catch (NoDockerServerException | NotFoundException | NotModifiedException ignored) {
            // 容器已停止，不处理
            log.debug("停止docker异常:{} , status : {} ",docker.getId(), docker.getStatus());
            log.warn("Stop container error", ignored);
        }
    }

    /**
     * 停止容器
     *
     * @param containerId 容器
     * @throws DockerStopException 停止异常
     */
    public void stop(String containerId, DockerServer dockerServer) {

        try {
            log.debug("停止docker;containerId:{};dockerServerId:{}",containerId,dockerServer.getId());
            DockerClient dockerClient = dockerClientFactory.get(dockerServer);
            dockerClient.stopContainerCmd(containerId).withTimeout(5).exec();
        } catch (NoDockerServerException | NotFoundException | NotModifiedException ignored) {
            // 容器已停止，不处理
            log.debug("停止docker异常;containerId:{};dockerServerId:{}",containerId,dockerServer.getId());
            log.warn("Stop container error", ignored);
        }
    }

    /**
     * 异步kill(强制关闭)容器
     *
     * @param docker 容器
     */
    @Async
    public void killAsync(DockerContainer docker) {
        this.kill(docker);
    }

    /**
     * kill(强制关闭)容器
     *
     * @param docker 容器
     */
    public void kill(DockerContainer docker) {
        if (docker.getContainerId() == null) {
            return;
        }

        try {
            log.debug("强制关闭docker;containerId:{};dockerServerId:{}",docker.getId(),docker.getDockerServer().getId());
            DockerClient dockerClient = dockerClientFactory.get(docker.getDockerServer());
            dockerClient.killContainerCmd(docker.getContainerId()).exec();
        } catch (NoDockerServerException | NotFoundException ignored) {
            log.debug("强制关闭docker异常;dockerId:{};dockerServerId:{}",docker.getId(),docker.getDockerServer().getId());
            log.warn("Kill container error", ignored);
        }
    }

    /**
     * 删除容器
     *
     * @param docker
     * @throws DockerRemoveException
     */
    public void remove(DockerContainer docker) {
        if (docker.getContainerId() == null) {
            return;
        }
        try {
            log.debug("删除容器docker;containerId:{};dockerServerId:{}",docker.getId(),docker.getDockerServer().getId());
            DockerClient dockerClient = dockerClientFactory.get(docker.getDockerServer());
            dockerClient.removeContainerCmd(docker.getContainerId()).withForce(true).exec();
        } catch (NoDockerServerException | NotFoundException e) {
            log.debug("删除容器异常docker;containerId:{};dockerServerId:{}",docker.getId(),docker.getDockerServer().getId());
            log.warn("Remove container error", e);
        }
    }

    /**
     * 生成端口绑定规则
     *
     * @param docker
     * @return
     */
    private List<PortBinding> generatePortBindList(DockerContainer docker) {
        List<PortBinding> portBindingList = new ArrayList<>();
        Environment environment = docker.getEnvironmentVer().getEnvironment();
        if (docker.isLspSupported()) {
            portBindingList.add(PortBinding.parse(String.valueOf(SystemConsts.LSP_PORT)));
        }
        portBindingList.add(PortBinding.parse(String.valueOf(environment.getPort())));
        portBindingList.add(PortBinding.parse(String.valueOf(SystemConsts.PROJECT_WEB_PORT)));
        portBindingList.add(PortBinding.parse(String.valueOf(SystemConsts.AGENT_SERVER_PORT)));
        portBindingList.add(PortBinding.parse(String.valueOf(22)));
        return portBindingList;
    }

    /**
     * 生成文件绑定规则
     *
     * @param docker
     * @return
     */
    private List<Bind> generateBindList(DockerContainer docker, String languagePackage) {
        // 文件挂载配置
        List<Bind> bindList = new ArrayList<>();
        // 可执行Agent
        bindList.add(
            Bind.parse(
                systemProperties.getDocker().getAgent().getHostPath()
                    + ":"
                    + systemProperties.getDocker().getAgent().getDockerPath()
                    + ":ro"));
        // nix
//        bindList.add(Bind.parse(systemProperties.getDocker().getVolumeNix() + ":/cache/nix:ro"));

        // 各种依赖
        String dependencyPath = docker.getRootPath() + Constant.CODE_ZONE_DEPENDENCY_PATH + "/";

        String dependencyHomePath = dependencyPath + "home";
        File homeDir = new File(dependencyHomePath);
        if (homeDir.exists() && homeDir.isDirectory()) {
            // 先挂载/home/<USER>
            bindList.add(
                Bind.parse(dependencyHomePath
                    + ":"
                    + systemProperties.getDocker().getStorage().getHomePath()));

            String dependencyHomePathApp = dependencyHomePath + "/app";
            File homeAppDir = new File(dependencyHomePathApp);
            if (!(homeAppDir.exists() && homeAppDir.isDirectory())) {
                // 代码挂在到/home/<USER>/app目录
                bindList.add(
                    Bind.parse(
                        docker.getRootPath()
                            + Constant.CODE_ZONE_SOURCE_PATH
                            + ":"
                            + systemProperties.getDocker().getStorage().getAppPath()));

                if (FileUtil.exist(dependencyPath + ".ssh")) {
                    bindList.add(
                        Bind.parse(
                            dependencyPath
                                + ".ssh"
                                + ":"
                                + systemProperties.getDocker().getStorage().getHomePath()
                                + ".ssh"));
                }
            }

            File nvmDir = new File(dependencyHomePath + "/.nvm");
            if (nvmDir.exists() && nvmDir.isDirectory()) {
                switch (languagePackage) {
                    case Constant.CODE_ZONE_PYTHON_PACKAGE:
                        // python
                        bindList.add(
                            Bind.parse(
                                dependencyPath
                                    + ".pyenv"
                                    + ":"
                                    + systemProperties.getDocker().getStorage().getHomePath()
                                    + ".pyenv"));
                        break;
                    case Constant.CODE_ZONE_GO_PACKAGE:
                        // golang
                        bindList.add(
                            Bind.parse(
                                dependencyPath
                                    + ".gvm"
                                    + ":"
                                    + systemProperties.getDocker().getStorage().getHomePath()
                                    + ".gvm"));
                        break;
                    case Constant.CODE_ZONE_RUBY_PACKAGE:
                        // ruby
                        File rbenvDir = new File(dependencyPath + ".rbenv");
                        if (rbenvDir.exists() && rbenvDir.isDirectory()) {
                            bindList.add(
                                Bind.parse(
                                    dependencyPath
                                        + ".rbenv"
                                        + ":"
                                        + systemProperties.getDocker().getStorage().getHomePath()
                                        + ".rbenv"));
                        }

                    case Constant.CODE_ZONE_JAVA_PACKAGE:
                        // java
                        File sdkmanDir = new File(dependencyPath + ".sdkman");
                        if (sdkmanDir.exists() && sdkmanDir.isDirectory()) {
                            bindList.add(
                                Bind.parse(
                                    dependencyPath
                                        + ".sdkman"
                                        + ":"
                                        + systemProperties.getDocker().getStorage().getHomePath()
                                        + ".sdkman"));
                        }
                }
            }
        }
//        } else {
//            File paasDir = new File(dependencyPath + ".paas");
//            if (paasDir.exists() && paasDir.isDirectory()) {
//                bindList.add(
//                    Bind.parse(
//                        dependencyPath
//                            + ".paas"
//                            + ":"
//                            + systemProperties.getDocker().getStorage().getHomePath()
//                            + ".paas"));
//            }
//
//            File nvmDir = new File(dependencyPath + ".nvm");
//            // 兼容新老镜像目录不存在的问题
//            if (nvmDir.exists() && nvmDir.isDirectory()) {
//                switch (languagePackage) {
//                    case Constant.CODE_ZONE_PYTHON_PACKAGE:
//                        // python
//                        bindList.add(
//                            Bind.parse(
//                                dependencyPath
//                                    + ".pyenv"
//                                    + ":"
//                                    + systemProperties.getDocker().getStorage().getHomePath()
//                                    + ".pyenv"));
//                        break;
//                    case Constant.CODE_ZONE_GO_PACKAGE:
//                        // golang
//                        bindList.add(
//                            Bind.parse(
//                                dependencyPath
//                                    + ".gvm"
//                                    + ":"
//                                    + systemProperties.getDocker().getStorage().getHomePath()
//                                    + ".gvm"));
//                        break;
//                    case Constant.CODE_ZONE_RUBY_PACKAGE:
//                        // ruby
//                        File rbenvDir = new File(dependencyPath + ".rbenv");
//                        if (rbenvDir.exists() && rbenvDir.isDirectory()) {
//                            bindList.add(
//                                Bind.parse(
//                                    dependencyPath
//                                        + ".rbenv"
//                                        + ":"
//                                        + systemProperties.getDocker().getStorage().getHomePath()
//                                        + ".rbenv"));
//                        }
//                }
//
//                // node
//                bindList.add(
//                    Bind.parse(
//                        dependencyPath
//                            + ".nvm"
//                            + ":"
//                            + systemProperties.getDocker().getStorage().getHomePath()
//                            + ".nvm"));
//            }
//
//            bindList.add(
//                Bind.parse(
//                    dependencyPath
//                        + ".m2"
//                        + ":"
//                        + systemProperties.getDocker().getStorage().getHomePath()
//                        + ".m2"));
//            bindList.add(
//                Bind.parse(
//                    dependencyPath
//                        + ".npm"
//                        + ":"
//                        + systemProperties.getDocker().getStorage().getHomePath()
//                        + ".npm"));
//            bindList.add(
//                Bind.parse(
//                    dependencyPath
//                        + "ruby"
//                        + ":"
//                        + systemProperties.getDocker().getStorage().getHomePath()
//                        + ".local/share/gem/ruby"));
//            bindList.add(
//                Bind.parse(
//                    dependencyPath
//                        + "pip"
//                        + ":"
//                        + systemProperties.getDocker().getStorage().getHomePath()
//                        + ".cache/pip"));
//            bindList.add(
//                Bind.parse(
//                    dependencyPath
//                        + ".config"
//                        + ":"
//                        + systemProperties.getDocker().getStorage().getHomePath()
//                        + ".config"));
//            bindList.add(
//                Bind.parse(
//                    dependencyPath
//                        + "mysqlData"
//                        + ":"
//                        + systemProperties.getDocker().getStorage().getHomePath()
//                        + ".mysql"));
//            bindList.add(
//                Bind.parse(
//                    dependencyPath
//                        + "go"
//                        + ":"
//                        + systemProperties.getDocker().getStorage().getHomePath()
//                        + "go"));
//            bindList.add(
//                Bind.parse(
//                    dependencyPath
//                        + ".dotnet"
//                        + ":"
//                        + systemProperties.getDocker().getStorage().getHomePath()
//                        + ".dotnet"));
//            bindList.add(
//                Bind.parse(
//                    dependencyPath
//                        + ".rag"
//                        + ":"
//                        + systemProperties.getDocker().getStorage().getHomePath()
//                        + ".rag"));
//            bindList.add(
//                Bind.parse(
//                    dependencyPath
//                        + ".pub-cache"
//                        + ":"
//                        + systemProperties.getDocker().getStorage().getHomePath()
//                        + ".pub-cache"));
//            if (FileUtil.exist(dependencyPath+".ssh")){
//                bindList.add(
//                    Bind.parse(
//                        dependencyPath
//                            + ".ssh"
//                            + ":"
//                            + systemProperties.getDocker().getStorage().getHomePath()
//                            + ".ssh"));
//            }
//            // 代码
//            bindList.add(
//                Bind.parse(
//                    docker.getRootPath()
//                        + Constant.CODE_ZONE_SOURCE_PATH
//                        + ":"
//                        + systemProperties.getDocker().getStorage().getAppPath()));
//        }

        // 系统目录 持久化
        bindList.add(
            Bind.parse(
                dependencyPath
                    + "apt/etc"
                    + ":"
                    + "/etc"));
        bindList.add(
            Bind.parse(
                dependencyPath
                    + "apt/var"
                    + ":"
                    + "/var"));
        bindList.add(
            Bind.parse(
                dependencyPath
                    + "apt/usr"
                    + ":"
                    + "/usr"));

        // 如果容器被分配了磁盘资源，挂载磁盘资源
        if (dockerDiskResourceRepository.existsByDockerContainerId(docker.getId())) {
            List<DockerDiskResource> dockerDiskResourceList =
                dockerDiskResourceRepository.findAllByDockerContainerId(docker.getId());
            for (DockerDiskResource dockerDiskResource : dockerDiskResourceList) {
                Optional<DiskResource> optionalDiskResource =
                    diskResourceRepository.findById(dockerDiskResource.getResourceId());
                optionalDiskResource.ifPresent(
                    diskResource ->
                        bindList.add(
                            Bind.parse(diskResource.getPath() + ":" + dockerDiskResource.getMountPath())));
            }
        }

        return bindList;
    }

    public Integer run(DockerServer dockerServer, String[] runCmd, String image, String dockerName)
        throws NoDockerServerException {
        DockerClient dockerClient = dockerClientFactory.get(dockerServer);
        String containerName = Constant.NIX_CONTAINER_NAME + dockerName;
        List<Bind> bindList = new ArrayList<>();
        bindList.add(Bind.parse(systemProperties.getDocker().getVolumeNix() + ":/nix"));
        HostConfig hostConfig = HostConfig
            .newHostConfig()
            .withBinds(bindList)
            .withAutoRemove(true);
        CreateContainerCmd createCmd =
            dockerClient
                .createContainerCmd(image)
                .withTty(true)
                .withName(containerName)
                .withHostConfig(hostConfig)
                .withCmd(runCmd);
        CreateContainerResponse response = createCmd.exec();
        WaitContainerResultCallback callback = new WaitContainerResultCallback();
        dockerClient.startContainerCmd(response.getId()).exec();
        dockerClient.waitContainerCmd(response.getId()).exec(callback);
        return callback.awaitStatusCode();
    }

    public void exec(String cmd, DockerContainer dockerContainer) throws NoDockerServerException {
        DockerClient client = dockerClientFactory.get(dockerContainer.getDockerServer());
        ExecCreateCmdResponse exec = client.execCreateCmd(dockerContainer.getContainerId()).withUser("root")
            .withCmd("sh", "-c", cmd).withTty(false)
            .withAttachStdin(true).withAttachStdout(true).withAttachStderr(true).exec();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            ExecStartResultCallback resultCallback = client.execStartCmd(exec.getId()).withDetach(false)
                .withTty(true).exec(new ExecStartResultCallback(outputStream, System.err));
            resultCallback.awaitCompletion();
        } catch (InterruptedException e) {
            throw new CustomRuntimeException("run cmd fail", "容器运行失败");
        }
    }

    public void execRunCmd(String cmd, DockerContainer docker, String runId,
        DockerRunCmdEnum cmdEnum, RunCmdCallBackService runCmdCallBackService)
        throws NoDockerServerException {
        DockerClient dockerClient = dockerClientFactory.get(docker.getDockerServer());

        List<String> envList = new ArrayList<>();
        docker.getMiddlewares().forEach(item -> {
            Map<String, Object> userEnvMap = JsonUtil.jsonToMap(item.getUserEnv());
            if (userEnvMap != null) {
                userEnvMap.forEach((key, value) -> envList.add(String.format("%s=%s", key, value)));
            }
        });

        EnvironmentVer environmentVer = docker.getEnvironmentVer();
        Environment environment = environmentVer.getEnvironment();

        /* 资源限制 */
        HostConfig hostConfig = new HostConfig();

        // cpu限制 https://docs.docker.com/config/containers/resource_constraints/#configure-the-default-cfs-scheduler
        hostConfig.withCpuShares(1024);

        // 内存限制 https://docs.docker.com/config/containers/resource_constraints/#limit-a-containers-access-to-memory
        // memory-swap == memory + swap; (swap == 0 ? 禁止swap:可用swap)
        hostConfig.withMemoryReservation(1024L * 1024 * docker.getResourcesLimit().getRAM())
            .withMemory(1024L * 1024 * docker.getResourcesLimit().getRAM())
            .withMemorySwap(1024L * 1024 * docker.getResourcesLimit().getRAM());
        hostConfig.withOomKillDisable(true);
        //  log限制
        hostConfig.withLogConfig(new LogConfig(LoggingType.DEFAULT, Collections.singletonMap("max-size", "10m")));
        // todo： 特权模式是为了在容器中mount nix overlay目录 @see docker/dockerImage/run.sh:7
        // 可以使用--mount替换 https://docs.docker.com/storage/volumes/#choose-the--v-or---mount-flag
        hostConfig.withPrivileged(true);
        // 文件挂载配置
        hostConfig.withBinds(this.generateBindList(docker, environment.getLanguagePackage()));
        hostConfig.withAutoRemove(true);
        String containerName = Constant.TMP_CONTAINER_NAME + runId;
        CreateContainerCmd createCmd =
            dockerClient
                .createContainerCmd(docker.getImage())
                .withTty(true).withUser("root")
                .withName(containerName)
                .withHostConfig(hostConfig).withEnv(envList)
                .withCmd("sh", "-c", "sleep " + Constant.UNITTEST_RUN_TIMEOUT);
        CreateContainerResponse response = createCmd.exec();
        dockerClient.startContainerCmd(response.getId()).exec();

        String fileName = Constant.PAAS_UNIT_CONSTANT + runId + Constant.FILE_SUFFIX.SH;
        String toFileCmd = cmd + "\n" + "echo -e \"\\n$?\"";
        String joinCmd =
            "/mount.sh && su - " + Constant.DOCKER_USER_NAME + " -c \"cd " + systemProperties.getDocker().getStorage()
                .getAppPath() + " &&  nix-shell " + Constant.SHELL_NIX_RENAME + " --run './" + fileName + "'\""
                + " && rm " + systemProperties.getDocker().getStorage().getAppPath() + File.separator + fileName;
        FileUtil.write(
            docker.getRootPath() + Constant.CODE_ZONE_SOURCE_PATH + File.separator + fileName, toFileCmd, false);
        ExecCreateCmdResponse createCmdResponse = dockerClient
            .execCreateCmd(response.getId()).withAttachStdout(true).withAttachStderr(true).withTty(true)
            .withAttachStdin(true)
            .withCmd("sh", "-c", joinCmd)
            .exec();
        dockerClient.execStartCmd(createCmdResponse.getId()).withTty(true)
            .exec(
                new BackgroundRunCmdHandler(redisExternalService, runCmdCallBackService, runId, this, response.getId(),
                    docker.getDockerServer(), cmdEnum, docker.getRootPath()));
    }

    /**
     * Docker update
     *
     * @param docker docker
     * @throws NoDockerServerException DockerServer不可用
     * @see <a href="https://docs.docker.com/engine/reference/commandline/update/">Update configuration of one or more
     * containers</>
     */
    public void update(DockerContainer docker) throws NoDockerServerException {
        DockerClient client = dockerClientFactory.get(docker.getDockerServer());
        try {
            client.updateContainerCmd(docker.getContainerId())
                .withMemory(1024L * 1024 * docker.getResourcesLimit().getRAM())
                .withMemoryReservation(1024L * 1024 * docker.getResourcesLimit().getRAM())
                .withMemorySwap(1024L * 1024 * docker.getResourcesLimit().getRAM()).exec();
        } catch (NotFoundException e) {
            log.warn("Update container fail, not found {}", docker.getContainerId());
        }
    }

    /**
     * Docker info
     *
     * @param docker docker
     * @throws NoDockerServerException DockerServer不可用
     * @see <a href="https://docs.docker.com/engine/reference/commandline/update/">Update configuration of one or more
     * containers</>
     */
    public InspectContainerResponse info(DockerContainer docker) throws NoDockerServerException {
        DockerClient client = dockerClientFactory.get(docker.getDockerServer());
        InspectContainerResponse inspectContainerResponse = new InspectContainerResponse();
        try {
            inspectContainerResponse = client.inspectContainerCmd(docker.getContainerId()).exec();
        } catch (NotFoundException e) {
            log.warn("inspect container fail, not found {}", docker.getContainerId());
        }
        return inspectContainerResponse;
    }
}
