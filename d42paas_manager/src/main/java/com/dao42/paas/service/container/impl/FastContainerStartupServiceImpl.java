package com.dao42.paas.service.container.impl;

import com.dao42.paas.dto.container.ContainerStartupResult;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.docker.DockerServer;
import com.dao42.paas.repository.image.ThreadImageRepository;
import com.dao42.paas.service.container.FastContainerStartupService;
import com.dao42.paas.service.docker.DockerService;
import com.dao42.paas.service.image.ImageCacheService;
import com.dao42.paas.service.meta.MetaEnvironmentInjector;
import com.dao42.paas.service.scheduler.ImageAwareDockerServerSelector;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 快速容器启动服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FastContainerStartupServiceImpl implements FastContainerStartupService {

    private final DockerService dockerService;
    private final ImageAwareDockerServerSelector serverSelector;
    private final ImageCacheService imageCacheService;
    private final MetaEnvironmentInjector metaEnvironmentInjector;
    private final ThreadImageRepository threadImageRepository;
    
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    public ContainerStartupResult fastStartContainer(DockerContainer dockerContainer) {
        ContainerStartupResult result = new ContainerStartupResult();
        ContainerStartupResult.StepTimings timings = new ContainerStartupResult.StepTimings();
        result.setStepTimings(timings);
        result.setStartTime(LocalDateTime.now());
        
        long totalStartTime = System.currentTimeMillis();
        
        try {
            // 1. 智能服务器选择
            long serverSelectionStart = System.currentTimeMillis();
            DockerServer targetServer = serverSelector.select(dockerContainer);
            if (targetServer == null) {
                throw new RuntimeException("没有可用的Docker服务器");
            }
            timings.setServerSelectionMs(System.currentTimeMillis() - serverSelectionStart);
            result.setServerId(targetServer.getId().toString());

            // 2. 最优镜像选择
            long imageSelectionStart = System.currentTimeMillis();
            String optimalImage = selectOptimalImage(dockerContainer, targetServer);
            timings.setImageSelectionMs(System.currentTimeMillis() - imageSelectionStart);
            result.setImageTag(optimalImage);

            // 3. 并行准备资源
            parallelPrepareResources(dockerContainer, targetServer, optimalImage);

            // 4. 检查镜像是否可用
            long imagePullStart = System.currentTimeMillis();
            boolean imageAvailable = imageCacheService.hasImage(
                targetServer.getId().toString(), optimalImage);
            
            if (!imageAvailable) {
                boolean pullSuccess = fastPullImage(targetServer.getId().toString(), optimalImage);
                if (!pullSuccess) {
                    throw new RuntimeException("镜像拉取失败: " + optimalImage);
                }
            } else {
                result.setUsedCachedImage(true);
                // 记录镜像命中
                imageCacheService.recordImageHit(targetServer.getId().toString(), optimalImage);
            }
            timings.setImagePullMs(System.currentTimeMillis() - imagePullStart);

            // 5. 注入@meta环境
            long metaInjectionStart = System.currentTimeMillis();
            try {
                metaEnvironmentInjector.injectDefaultMetaEnvironment(dockerContainer);
                result.setMetaEnvironmentInjected(true);
            } catch (Exception e) {
                log.warn("@meta环境注入失败，继续启动容器: {}", e.getMessage());
                result.setMetaEnvironmentInjected(false);
            }
            timings.setMetaInjectionMs(System.currentTimeMillis() - metaInjectionStart);

            // 6. 创建和启动容器
            long containerCreationStart = System.currentTimeMillis();
            
            // 更新容器配置
            dockerContainer.setDockerServerId(targetServer.getId());
            dockerContainer.setImageName(optimalImage);
            
            // 创建容器
            String containerId = dockerService.createContainer(dockerContainer);
            result.setContainerId(containerId);
            timings.setContainerCreationMs(System.currentTimeMillis() - containerCreationStart);

            // 7. 启动容器
            long containerStartStart = System.currentTimeMillis();
            dockerService.startContainer(dockerContainer);
            timings.setContainerStartMs(System.currentTimeMillis() - containerStartStart);

            // 设置成功结果
            result.setSuccess(true);
            result.setEndTime(LocalDateTime.now());
            result.setStartupTimeMs(System.currentTimeMillis() - totalStartTime);

            log.info("快速容器启动成功: containerId={}, totalTime={}ms, usedCache={}", 
                    containerId, result.getStartupTimeMs(), result.isUsedCachedImage());

        } catch (Exception e) {
            log.error("快速容器启动失败", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now());
            result.setStartupTimeMs(System.currentTimeMillis() - totalStartTime);
        }

        return result;
    }

    @Override
    public void preheatStartupEnvironment(DockerContainer dockerContainer) {
        try {
            log.debug("预热容器启动环境: {}", dockerContainer.getId());
            
            // 异步预热操作
            CompletableFuture.runAsync(() -> {
                try {
                    // 1. 预选服务器
                    DockerServer targetServer = serverSelector.select(dockerContainer);
                    if (targetServer == null) {
                        return;
                    }

                    // 2. 预选镜像
                    String optimalImage = selectOptimalImage(dockerContainer, targetServer);

                    // 3. 预拉取镜像
                    if (!imageCacheService.hasImage(targetServer.getId().toString(), optimalImage)) {
                        imageCacheService.preloadImage(targetServer.getId().toString(), optimalImage);
                    }

                    // 4. 预热@meta环境
                    // 这里可以预先同步@meta环境到本地缓存

                } catch (Exception e) {
                    log.warn("预热容器启动环境失败", e);
                }
            }, executorService);

        } catch (Exception e) {
            log.error("预热容器启动环境失败", e);
        }
    }

    @Override
    public String selectOptimalImage(DockerContainer dockerContainer, DockerServer targetServer) {
        try {
            // 1. 尝试查找Thread镜像
            String threadImage = findThreadImage(dockerContainer);
            if (threadImage != null) {
                // 检查目标服务器是否有该镜像
                if (imageCacheService.hasImage(targetServer.getId().toString(), threadImage)) {
                    log.debug("选择Thread镜像 (已缓存): {}", threadImage);
                    return threadImage;
                }
                
                // 即使没有缓存，Thread镜像也是最优选择
                log.debug("选择Thread镜像 (需拉取): {}", threadImage);
                return threadImage;
            }

            // 2. 回退到默认镜像
            String defaultImage = dockerContainer.getImageName();
            if (defaultImage == null || defaultImage.isEmpty()) {
                // 使用系统默认镜像
                defaultImage = "992382636473.dkr.ecr.us-east-1.amazonaws.com/clacky/docker:latest";
            }

            log.debug("选择默认镜像: {}", defaultImage);
            return defaultImage;

        } catch (Exception e) {
            log.error("选择最优镜像失败", e);
            return dockerContainer.getImageName();
        }
    }

    @Override
    public void parallelPrepareResources(DockerContainer dockerContainer, DockerServer targetServer, String imageTag) {
        try {
            // 并行执行多个准备任务
            CompletableFuture<Void> imagePrepare = CompletableFuture.runAsync(() -> {
                // 预检查镜像
                if (!imageCacheService.hasImage(targetServer.getId().toString(), imageTag)) {
                    imageCacheService.preloadImage(targetServer.getId().toString(), imageTag);
                }
            }, executorService);

            CompletableFuture<Void> metaPrepare = CompletableFuture.runAsync(() -> {
                // 预热@meta环境
                try {
                    // 这里可以预先检查和同步@meta环境
                } catch (Exception e) {
                    log.warn("@meta环境预热失败", e);
                }
            }, executorService);

            CompletableFuture<Void> resourcePrepare = CompletableFuture.runAsync(() -> {
                // 预分配资源
                try {
                    // 这里可以预先分配网络、存储等资源
                } catch (Exception e) {
                    log.warn("资源预分配失败", e);
                }
            }, executorService);

            // 等待所有准备任务完成 (设置超时)
            CompletableFuture.allOf(imagePrepare, metaPrepare, resourcePrepare)
                .orTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .join();

        } catch (Exception e) {
            log.warn("并行准备资源失败", e);
        }
    }

    @Override
    public boolean fastPullImage(String serverId, String imageTag) {
        try {
            log.debug("快速拉取镜像: serverId={}, imageTag={}", serverId, imageTag);
            
            // 使用ImageCacheService进行拉取，它会处理并发和缓存
            imageCacheService.preloadImage(serverId, imageTag);
            
            // 等待拉取完成 (简化版本，实际应该有更好的等待机制)
            int maxWaitSeconds = 60;
            int waitedSeconds = 0;
            
            while (waitedSeconds < maxWaitSeconds) {
                if (imageCacheService.hasImage(serverId, imageTag)) {
                    log.debug("镜像拉取完成: serverId={}, imageTag={}", serverId, imageTag);
                    return true;
                }
                
                Thread.sleep(1000);
                waitedSeconds++;
            }
            
            log.warn("镜像拉取超时: serverId={}, imageTag={}", serverId, imageTag);
            return false;

        } catch (Exception e) {
            log.error("快速拉取镜像失败: serverId={}, imageTag={}", serverId, imageTag, e);
            return false;
        }
    }

    @Override
    public long estimateStartupTime(DockerContainer dockerContainer, DockerServer targetServer) {
        try {
            long estimatedTime = 0;
            
            // 基础启动时间
            estimatedTime += 2000; // 2秒基础时间
            
            // 镜像相关时间
            String optimalImage = selectOptimalImage(dockerContainer, targetServer);
            if (imageCacheService.hasImage(targetServer.getId().toString(), optimalImage)) {
                estimatedTime += 1000; // 有缓存，1秒
            } else {
                estimatedTime += 15000; // 需要拉取，15秒
            }
            
            // @meta环境注入时间
            if (!metaEnvironmentInjector.isMetaEnvironmentInjected(dockerContainer)) {
                estimatedTime += 3000; // 3秒注入时间
            }
            
            // 服务器负载影响
            double loadFactor = calculateServerLoadFactor(targetServer);
            estimatedTime = (long) (estimatedTime * loadFactor);
            
            return estimatedTime;

        } catch (Exception e) {
            log.error("估算容器启动时间失败", e);
            return 20000; // 默认20秒
        }
    }

    /**
     * 查找Thread镜像
     */
    private String findThreadImage(DockerContainer dockerContainer) {
        try {
            Long projectId = extractProjectId(dockerContainer);
            Long issueId = extractIssueId(dockerContainer);

            if (projectId != null) {
                if (issueId != null) {
                    // Issue Thread镜像
                    Optional<String> issueImage = threadImageRepository
                        .findLatestIssueThreadImage(projectId, issueId);
                    if (issueImage.isPresent()) {
                        return issueImage.get();
                    }
                }

                // Root Thread镜像
                Optional<String> rootImage = threadImageRepository
                    .findLatestRootThreadImage(projectId);
                if (rootImage.isPresent()) {
                    return rootImage.get();
                }
            }

            return null;

        } catch (Exception e) {
            log.error("查找Thread镜像失败", e);
            return null;
        }
    }

    /**
     * 计算服务器负载因子
     */
    private double calculateServerLoadFactor(DockerServer server) {
        try {
            // CPU负载影响
            double cpuUsage = (double) server.getUsedCpu() / server.getTotalCpu();
            
            // 内存负载影响
            double memoryUsage = (double) server.getUsedMemory() / server.getTotalMemory();
            
            // 容器数量影响
            double containerLoad = (double) server.getRunningContainerCount() / server.getMaxContainerCount();
            
            // 综合负载因子 (1.0 = 正常，> 1.0 = 较慢)
            double loadFactor = 1.0 + (cpuUsage + memoryUsage + containerLoad) / 3.0;
            
            return Math.max(1.0, Math.min(3.0, loadFactor)); // 限制在1.0-3.0之间

        } catch (Exception e) {
            log.warn("计算服务器负载因子失败", e);
            return 1.5; // 默认负载因子
        }
    }

    /**
     * 从容器路径中提取项目ID
     */
    private Long extractProjectId(DockerContainer container) {
        String rootPath = container.getRootPath();
        if (rootPath != null) {
            String[] parts = rootPath.split("/");
            if (parts.length > 3 && "projects".equals(parts[2])) {
                try {
                    return Long.parseLong(parts[3]);
                } catch (NumberFormatException e) {
                    log.warn("无法从路径中解析项目ID: {}", rootPath);
                }
            }
        }
        return null;
    }

    /**
     * 从容器路径中提取Issue ID
     */
    private Long extractIssueId(DockerContainer container) {
        String rootPath = container.getRootPath();
        if (rootPath != null && rootPath.contains("/issues/")) {
            String[] parts = rootPath.split("/");
            for (int i = 0; i < parts.length - 1; i++) {
                if ("issues".equals(parts[i]) && i + 1 < parts.length) {
                    try {
                        return Long.parseLong(parts[i + 1]);
                    } catch (NumberFormatException e) {
                        log.warn("无法从路径中解析Issue ID: {}", rootPath);
                    }
                }
            }
        }
        return null;
    }
}
