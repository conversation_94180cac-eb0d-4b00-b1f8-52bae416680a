package com.dao42.paas.service.docker.handler;

import cn.hutool.json.JSONUtil;
import com.dao42.paas.common.constants.RedisPrefix;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.enums.DockerStatus;
import com.dao42.paas.enums.MiddlewareStatusEnum;
import com.dao42.paas.exception.resource.NoDockerServerException;
import com.dao42.paas.framework.alert.ExceptionMsgBot;
import com.dao42.paas.framework.alert.ExceptionMsgBotBean;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.middleware.MiddlewareInstance;
import com.dao42.paas.redis.RedisRedissonUtil;
import com.dao42.paas.repository.docker.DockerRepository;
import com.dao42.paas.repository.middleware.MiddlewareInstanceRepository;
import com.dao42.paas.service.RedisExternalService;
import com.dao42.paas.service.impl.MemoryDockerServerSelector;
import com.dao42.paas.service.resource.URLResourceService;
import com.dao42.paas.event.ContainerStoppedEvent;
import com.github.dockerjava.api.async.ResultCallback.Adapter;
import com.github.dockerjava.api.model.Event;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * DockerEvents事件回调处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class EventsCallbackHandler extends Adapter<Event> {

    /**
     * 容器销毁事件
     */
    public static final String DOCKER_EVENT_DIE = "die";
    /**
     * 容器启动成功事件
     */
    public static final String DOCKER_EVENT_START = "start";

    /**
     * 容器健康检查通过事件
     */
    public static final String DOCKER_EVENT_HEALTHY = "health_status: healthy";

    /**
     * 容器健康检查不通过事件
     */
    public static final String DOCKER_EVENT_UNHEALTHY = "health_status: unhealthy";
    private final SystemProperties systemProperties;
    private final MemoryDockerServerSelector dockerServerSelector;
    private final DockerRepository dockerRepository;
    private final MiddlewareInstanceRepository middlewareRepository;
    private final URLResourceService urlResourceService;
    private final RedisExternalService redisExternalService;
    private final MiddlewareInstanceRepository middlewareInstanceRepository;
    private final ExceptionMsgBot bot;
    private final RedisRedissonUtil redisRedissonUtil;
    private final ApplicationEventPublisher eventPublisher;
    /**
     * 正则提取容器名称 example: dev-428319596139905024 MYSQL-428319596139905024
     */
    private final Pattern pattern = Pattern.compile("^(.+)-(\\d{18})$");

    /**
     * @param dockerEvent
     */
    @Override
    public void onNext(Event dockerEvent) {
        long startTime = System.currentTimeMillis();
        final RLock rLock = redisRedissonUtil
            .getRlockByName(RedisPrefix.REDISSON_REDIS_EVENTSCALLBACKHANDER_LOCK + ":" + dockerEvent.getId());
        boolean locked = false;
        try {
            //第一个时间是等锁时间，第二个时间是锁失效时间
            locked = rLock.tryLock(RedisRedissonUtil.REDISSON_TRY_LOCK_TIME, RedisRedissonUtil.REDISSON_RELEASE_LOCK_TIME, TimeUnit.MILLISECONDS);
            if (locked) {
                log.debug("DockerScheduling-docker-event, docker监听:{}", JSONUtil.toJsonStr(dockerEvent));
                if (dockerEvent.getAction() == null) {
                    return;
                }
                Optional<MiddlewareInstance> middlewareInstance;
                switch (dockerEvent.getAction()) {
//            case DOCKER_EVENT_START -> {
//                this.dealStartEvent(dockerEvent);
//            }
                    case DOCKER_EVENT_DIE -> {
                        try {
                            this.dealDieEvent(dockerEvent);
                        } catch (NoDockerServerException e) {
                            e.printStackTrace();
                        }
                    }
                    case DOCKER_EVENT_HEALTHY -> {
                        middlewareInstance = middlewareInstanceRepository.findByContainerId(dockerEvent.getId());
                        if (middlewareInstance.isPresent()) {
                            redisExternalService.setMiddlewareHealth(dockerEvent.getId(),
                                MiddlewareStatusEnum.HEALTH.name());
                        }
                    }
                    case DOCKER_EVENT_UNHEALTHY -> {
                        middlewareInstance = middlewareInstanceRepository.findByContainerId(
                            dockerEvent.getId());
                        if (middlewareInstance.isPresent()) {
                            redisExternalService.setMiddlewareHealth(dockerEvent.getId(),
                                MiddlewareStatusEnum.UN_HEALTH.name());
                        }
                    }
                    default -> log.warn("DockerScheduling-docker-event, unknown docker event type: {}", dockerEvent.getAction());
                }
            }
        } catch (Exception e) {
            log.error("DockerScheduling-docker-event, Redisson exception:RedisMessage:" + e.getMessage());
        } finally {
            // 持有者释放锁
            if (locked && rLock.isHeldByCurrentThread()) {
                // 释放锁
                rLock.unlock();
                log.debug("DockerScheduling-docker-event, Redisson get lock :RedisMessage:" + RedisPrefix.REDISSON_REDIS_EVENTSCALLBACKHANDER_LOCK + ":" + dockerEvent.getId());

            }else {
                log.debug("DockerScheduling-docker-event, Redisson get lock fail:RedisMessage:" + RedisPrefix.REDISSON_REDIS_EVENTSCALLBACKHANDER_LOCK + ":" + dockerEvent.getId());
            }

            long endTime = System.currentTimeMillis();
            log.debug("onNext execution time: " + (endTime - startTime) + "ms");
        }
    }

//    private void dealStartEvent(Event event) {
//        // 容器id（containerId）
//        String containerId = Objects.requireNonNull(event.getActor()).getId();
//        // 容器名（包含dockerId）
//        String containerName = event.getActor().getAttributes().get("name");
//        // 判断是否PAAS的容器或中间件
//        Matcher matcher = pattern.matcher(containerName);
//        if (matcher.matches()) {
//            String prefix = matcher.group(1);
//            Long dataId = Long.valueOf(matcher.group(2));
//            if (systemProperties.getDocker().getPrefixName().equals(prefix)) {
//                /* 先用CONTAINER_ID查询 */
//                Optional<DockerContainer> findByContainerId = dockerRepository.findByContainerId(containerId);
//                if (findByContainerId.isPresent()) {
//                    dockerService.setPort(findByContainerId.get());
//                } else {
//                    /*没查到，再用dockerId查询*/
//                    DockerContainer dockerContainer = dockerContainerService.getById(dataId);
//                    dockerService.setPort(dockerContainer);
//                }
//            }
//        }
//    }

    /**
     * 处理容器销毁事件
     *
     * @param event 事件
     */
    public void dealDieEvent(Event event) throws NoDockerServerException {
        // 容器id（containerId）
        String containerId = Objects.requireNonNull(event.getActor()).getId();
        // 容器名（包含dockerId）
        String containerName = event.getActor().getAttributes().get("name");
        // 判断是否PAAS的容器或中间件
        Matcher matcher = pattern.matcher(containerName);
        if (matcher.matches()) {
            String prefix = matcher.group(1);
            Long dataId = Long.valueOf(matcher.group(2));
            // 主容器
            if (systemProperties.getDocker().getPrefixName().equals(prefix)) {
                /* 先用CONTAINER_ID查询 */
                Optional<DockerContainer> findByContainerId = dockerRepository.findByContainerId(containerId);
                if (findByContainerId.isPresent()) {
                    DockerContainer container = findByContainerId.get();
                    dockerServerSelector.recycle(container);
                    urlResourceService.recycle(container);

                    // 发布容器停止事件，用于镜像化处理
                    eventPublisher.publishEvent(new ContainerStoppedEvent(this, container));

                    if (container.getStatus() != DockerStatus.STOP_SUCCESS) {
                        log.warn(
                            "DockerScheduling-docker-event, 容器停止，但状态不正常,id={},status={}",
                            container.getId(),
                            container.getStatus());
                    }
                } else {
                    /*没查到，再用dockerId查询*/
                    dockerRepository.findById(dataId)
                        .ifPresent(
                            findByDockerId -> {
                                dockerServerSelector.recycle(findByDockerId);
                                urlResourceService.recycle(findByDockerId);
                                log.warn(
                                    "DockerScheduling-docker-event, 容器ContainerId字段不正确，ContainerId:{}, ContainerName:{}",
                                    containerId,
                                    containerName);
                                if (findByDockerId.getStatus() != DockerStatus.STOP_SUCCESS) {
                                    log.warn(
                                        "DockerScheduling-docker-event, 容器停止，但状态不正常2, id={},status={}",
                                        findByDockerId.getId(),
                                        findByDockerId.getStatus());
                                }
                            });
                }
                // 中间件
            } else {
                Optional<MiddlewareInstance> byContainerId =
                    middlewareRepository.findByContainerId(containerId);
                if (byContainerId.isPresent()) {
                    redisExternalService.removeMiddlewareHealth(containerId);
                    dockerServerSelector.recycle(byContainerId.get());
                } else {
                    middlewareRepository.findById(dataId)
                        .ifPresent(
                            middleware -> {
                                dockerServerSelector.recycle(middleware);
                                log.warn(
                                    "DockerScheduling-docker-event, 中间件ContainerId字段不正确，ContainerId:{}, ContainerName:{}",
                                    containerId,
                                    containerName);
                                if (middleware.getStatus() != DockerStatus.STOP_SUCCESS) {
                                    log.warn(
                                        "DockerScheduling-docker-event, 中间件停止，但状态不正常,id={},status={}",
                                        middleware.getId(),
                                        middleware.getStatus());
                                }
                            });
                }
            }
        }
    }

    /**
     * @param throwable
     */
    @Override
    public void onError(Throwable throwable) {
        log.error("onError", throwable);
        bot.send(new ExceptionMsgBotBean((Exception) throwable, "Docker events发生异常", null));
        super.onError(throwable);
    }

    /**
     *
     */
    @Override
    public void onComplete() {
        log.debug("onComplete");
        bot.send(new ExceptionMsgBotBean(null, "Docker events已断开", null));
        super.onComplete();
    }
}
