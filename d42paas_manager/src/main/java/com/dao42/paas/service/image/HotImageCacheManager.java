package com.dao42.paas.service.image;

import com.dao42.paas.model.image.ImageCache;

import java.util.List;

/**
 * 热点镜像缓存管理器接口
 *
 * <AUTHOR>
 */
public interface HotImageCacheManager {

    /**
     * 识别热点镜像
     *
     * @return 热点镜像列表
     */
    List<ImageCache> identifyHotImages();

    /**
     * 预拉取热点镜像到所有服务器
     *
     * @param imageTag 镜像标签
     */
    void preloadHotImageToAllServers(String imageTag);

    /**
     * 预拉取热点镜像到指定服务器
     *
     * @param imageTag 镜像标签
     * @param serverIds 服务器ID列表
     */
    void preloadHotImageToServers(String imageTag, List<String> serverIds);

    /**
     * 清理过期的镜像缓存
     */
    void cleanupExpiredCaches();

    /**
     * 清理低使用率的镜像缓存
     */
    void cleanupLowUsageCaches();

    /**
     * 优化存储成本
     */
    void optimizeStorageCost();

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计
     */
    CacheStatistics getCacheStatistics();

    /**
     * 强制清理指定服务器的镜像缓存
     *
     * @param serverId 服务器ID
     * @param imageTag 镜像标签
     */
    void forceCleanupImageCache(String serverId, String imageTag);
}
