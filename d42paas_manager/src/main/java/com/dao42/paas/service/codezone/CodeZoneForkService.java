package com.dao42.paas.service.codezone;

import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.constants.Constant;
import com.dao42.paas.enums.SnapshotPublicationEnum;
import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.model.codezone.CodeZone;
import com.dao42.paas.service.storage.BtrfsService;
import com.dao42.paas.service.TenantService;
import com.dao42.paas.service.file.FileUtilService;
import com.dao42.paas.service.storage.StorageProvider;
import com.dao42.paas.service.storage.StorageService;
import com.dao42.paas.utils.DirUtil;
import java.io.File;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class CodeZoneForkService {

    private final SystemProperties systemProperties;
    private final GitExternalService gitExternalService;
    private final FileUtilService fileUtilService;
    private final TenantService tenantService;
    private final BtrfsService shellService;
    private final StorageProvider storageProvider;

    /**
     * 返回fork出codeZone在NFS共享盘上的绝对路径
     *
     * @param path                    fork来源目录
     * @param commitId                git commit ID
     * @param parentPath              nfs挂载的父目录
     * @param parentPathInBtrfs       btrfs上的父目录
     * @param snapshotPublicationEnum Snapshot发布类型
     * @return 新子卷在nfs上的路径
     */
    public String fork(String path, String commitId, String parentPath, String parentPathInBtrfs,
        SnapshotPublicationEnum snapshotPublicationEnum, CodeZone codeZone) {

        StorageService storageService = storageProvider.getStorageService(codeZone.getNfsNodeId());
        // 在NFS上创建父级路径
        File file = new File(parentPath);
        file.mkdirs();
        if (!file.exists()) {
            throw new CustomRuntimeException("mkdir.error", "创建子卷父路径失败");
        }
        log.info("gitStatus:" + System.currentTimeMillis());

        // 新CodeZone的文件夹名称
        String codeZonePathName = "@" + UUID.randomUUID();

        // 复制到新子卷
        String rootPathOnFileServer;
        switch (snapshotPublicationEnum) {
            case CODE_ZONE:
                rootPathOnFileServer = path.replace(
                    storageService.getPath()+Constant.CODE_ZONE_PATH,
                    systemProperties.getCodeZone().getPathInBtrfs());
                break;
            case CODE_ZONE_SNAPSHOT:
                rootPathOnFileServer = path.replace(
                    systemProperties.getCodeZone().getSnapshotPath(),
                    systemProperties.getCodeZone().getSnapshotPathInBtrfs());
                break;
            case DOCKER:
                rootPathOnFileServer = path.replace(
                    systemProperties.getDocker().getStorage().getNfsPath(),
                    systemProperties.getDocker().getStorage().getFileServerPath());
                break;
            default:
                throw new CustomRuntimeException("fork.is.fail", "fork 失败");
        }
        String cmd = systemProperties.getBtrfs().getCreateCmd()
            .replace("{source}", rootPathOnFileServer)
            .replace("{target}", DirUtil.join(parentPathInBtrfs, codeZonePathName));

        boolean success = storageService.execCommandBtrfs(cmd);
        if (!success) {
            throw new CustomRuntimeException("copy.fail", "复制子卷失败");
        }

        // reset到指定commitID
        String newCodeZoneSourcePath = parentPath + codeZonePathName + File.separator + Constant.CODE_ZONE_SOURCE_PATH;

        if (StringUtils.isNotBlank(commitId)) {
            this.gitExternalService.reset(newCodeZoneSourcePath, commitId);
            // 重置git
            this.fileUtilService.delete(new File(newCodeZoneSourcePath + "/.git"));
            this.gitExternalService.gitInit(newCodeZoneSourcePath);

        }

        // 清理不可继承的文件
        deleteBlockedFiles(newCodeZoneSourcePath);
        tenantService.coverGitignore(newCodeZoneSourcePath, codeZone.getEnvironmentVer().getEnvVerKey(),
            codeZone.getTenantId());
        return parentPath + codeZonePathName + "/";
    }

    public void deleteBlockedFiles(String sourcePath){
        String[] files = systemProperties.getCodeZone().getBlockedFiles().split(";");
        for (String file : files){
            this.fileUtilService.delete(new File(sourcePath+"/"+file));
        }
    }

}
