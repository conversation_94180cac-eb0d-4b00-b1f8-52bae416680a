package com.dao42.paas.service.image.impl;

import com.dao42.paas.dto.image.CacheStatistics;
import com.dao42.paas.dto.image.ThreadImageBuildRequest;
import com.dao42.paas.enums.ContainerType;
import com.dao42.paas.enums.ThreadImageStatus;
import com.dao42.paas.model.image.ThreadImage;
import com.dao42.paas.repository.image.ThreadImageRepository;
import com.dao42.paas.service.image.ImageRegistryService;
import com.dao42.paas.service.image.ThreadImageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * Thread镜像服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThreadImageServiceImpl implements ThreadImageService {

    private final ThreadImageRepository threadImageRepository;
    private final ImageRegistryService imageRegistryService;

    @Override
    public ThreadImage buildThreadImage(ThreadImageBuildRequest request) {
        try {
            // 创建Thread镜像记录
            ThreadImage threadImage = new ThreadImage();
            threadImage.setProjectId(request.getProjectId());
            threadImage.setIssueId(request.getIssueId());
            threadImage.setContainerType(ContainerType.valueOf(request.getContainerType()));
            threadImage.setImageTag(request.getImageTag() != null ? request.getImageTag() : generateImageTag(request));
            threadImage.setUserId(getCurrentUserId()); // 需要从上下文获取
            threadImage.setTenantId(getCurrentTenantId()); // 需要从上下文获取
            threadImage.setStatus(ThreadImageStatus.BUILDING);
            threadImage.setCreatedAt(LocalDateTime.now());
            
            threadImage = threadImageRepository.save(threadImage);
            
            // TODO: 异步执行镜像构建
            // 这里应该调用ThreadImageLifecycleManager的构建方法
            
            return threadImage;
        } catch (Exception e) {
            log.error("构建Thread镜像失败", e);
            throw new RuntimeException("构建Thread镜像失败", e);
        }
    }

    @Override
    public Page<ThreadImage> getThreadImages(Long projectId, Long issueId, String containerType, String status, Pageable pageable) {
        // 这里应该实现复杂的查询逻辑
        // 简化版本，返回所有镜像
        return threadImageRepository.findAll(pageable);
    }

    @Override
    public ThreadImage getThreadImage(Long id) {
        return threadImageRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Thread镜像不存在: " + id));
    }

    @Override
    public void deleteThreadImage(Long id) {
        ThreadImage threadImage = getThreadImage(id);
        
        try {
            // 删除镜像仓库中的镜像
            imageRegistryService.deleteImage(threadImage.getImageTag());
        } catch (Exception e) {
            log.warn("删除镜像仓库中的镜像失败: {}", threadImage.getImageTag(), e);
        }
        
        // 删除数据库记录
        threadImageRepository.delete(threadImage);
    }

    @Override
    public void pushThreadImage(Long id) {
        ThreadImage threadImage = getThreadImage(id);
        
        if (threadImage.getStatus() != ThreadImageStatus.READY) {
            throw new RuntimeException("镜像状态不正确，无法推送: " + threadImage.getStatus());
        }
        
        try {
            threadImage.setStatus(ThreadImageStatus.PUSHING);
            threadImageRepository.save(threadImage);
            
            imageRegistryService.push(threadImage.getImageTag());
            
            threadImage.setStatus(ThreadImageStatus.AVAILABLE);
            threadImageRepository.save(threadImage);
            
        } catch (Exception e) {
            threadImage.setStatus(ThreadImageStatus.FAILED);
            threadImage.setBuildLog("Push failed: " + e.getMessage());
            threadImageRepository.save(threadImage);
            throw new RuntimeException("推送镜像失败", e);
        }
    }

    @Override
    public ThreadImage getLatestThreadImage(Long projectId, Long issueId) {
        if (issueId != null) {
            return threadImageRepository.findLatestIssueThreadImage(projectId, issueId)
                .map(imageTag -> threadImageRepository.findByImageTag(imageTag))
                .orElse(null);
        } else {
            return threadImageRepository.findLatestRootThreadImage(projectId)
                .map(imageTag -> threadImageRepository.findByImageTag(imageTag))
                .orElse(null);
        }
    }

    @Override
    public String getBuildLog(Long id) {
        ThreadImage threadImage = getThreadImage(id);
        return threadImage.getBuildLog();
    }

    @Override
    public ThreadImage rebuildThreadImage(Long id) {
        ThreadImage threadImage = getThreadImage(id);
        
        // 重置状态
        threadImage.setStatus(ThreadImageStatus.BUILDING);
        threadImage.setBuildLog(null);
        threadImage.setUpdatedAt(LocalDateTime.now());
        
        threadImage = threadImageRepository.save(threadImage);
        
        // TODO: 异步执行重新构建
        
        return threadImage;
    }

    @Override
    public CacheStatistics getUserImageStatistics() {
        // 简化版本的统计
        CacheStatistics stats = new CacheStatistics();
        
        Long userId = getCurrentUserId();
        if (userId != null) {
            int userImageCount = threadImageRepository.countByUserId(userId);
            stats.setTotalCacheCount(userImageCount);
            // 其他统计信息...
        }
        
        return stats;
    }

    /**
     * 生成镜像标签
     */
    private String generateImageTag(ThreadImageBuildRequest request) {
        String registry = "992382636473.dkr.ecr.us-east-1.amazonaws.com";
        String timestamp = String.valueOf(System.currentTimeMillis());
        
        if ("ISSUE_THREAD".equals(request.getContainerType())) {
            return String.format("%s/clacky/docker:issue-thread-%s-%s-%s",
                registry, request.getProjectId(), request.getIssueId(), timestamp);
        } else {
            return String.format("%s/clacky/docker:root-thread-%s-%s",
                registry, request.getProjectId(), timestamp);
        }
    }

    /**
     * 获取当前用户ID (需要从安全上下文获取)
     */
    private Long getCurrentUserId() {
        // TODO: 从Spring Security上下文获取当前用户ID
        return 1L; // 临时返回
    }

    /**
     * 获取当前租户ID (需要从安全上下文获取)
     */
    private Long getCurrentTenantId() {
        // TODO: 从Spring Security上下文获取当前租户ID
        return 1L; // 临时返回
    }
}
