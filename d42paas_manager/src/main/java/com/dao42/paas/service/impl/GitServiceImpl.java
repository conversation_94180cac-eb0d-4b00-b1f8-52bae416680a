package com.dao42.paas.service.impl;

import com.dao42.paas.bean.CurrentUserBean;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.constants.Constant;
import com.dao42.paas.common.constants.RedisPrefix;
import com.dao42.paas.constants.Constant.CHECKOUT_TASK_RESULT;
import com.dao42.paas.external.sdk.dto.git.GitBranchDTO;
import com.dao42.paas.external.sdk.dto.git.GitCommitDTO;
import com.dao42.paas.external.sdk.dto.git.GitCreateAndCheckBranchDTO;
import com.dao42.paas.external.sdk.dto.git.GitCreateBranchDTO;
import com.dao42.paas.external.sdk.dto.git.GitDiffFileDTO;
import com.dao42.paas.external.sdk.dto.git.GitFetchDTO;
import com.dao42.paas.external.sdk.dto.git.GitPullDTO;
import com.dao42.paas.external.sdk.dto.git.GitSetRemoteUrlDTO;
import com.dao42.paas.external.sdk.dto.git.GitShowFileDTO;
import com.dao42.paas.external.sdk.dto.git.GitStatusDTO;
import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.model.codezone.CodeZone;
import com.dao42.paas.model.tenant.Tenant;
import com.dao42.paas.model.user.TenantUser;
import com.dao42.paas.redis.RedisRedissonUtil;
import com.dao42.paas.repository.TenantRepository;
import com.dao42.paas.repository.user.TenantUserRepository;
import com.dao42.paas.service.RedisExternalService;
import com.dao42.paas.service.codezone.GitExternalService;
import com.dao42.paas.service.codezone.GitService;
import com.dao42.paas.service.file.FileUtilService;

import java.io.File;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.Proxy.Type;
import java.net.ProxySelector;
import java.net.SocketAddress;
import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class GitServiceImpl implements GitService {

    private final SystemProperties systemProperties;
    private final TenantRepository tenantRepository;
    private final GitExternalService gitExternalService;
    private final FileUtilService fileUtilService;
    private final TenantUserRepository tenantUserRepository;
    private final RedisRedissonUtil redisRedissonUtil;
    private final RedisExternalService redisExternalService;

    @PostConstruct
    public void init() {
        this.initGitProxy();
    }

    private void initGitProxy() {
        String proxy = systemProperties.getCodeZone().getGithubProxy();
        if (StringUtils.isNotBlank(proxy)) {
            String[] split = proxy.split(":");
            if (split.length != 2) {
                return;
            }
            String host = split[0];
            Integer port = Integer.parseInt(split[1]);
            ProxySelector.setDefault(new ProxySelector() {
                final ProxySelector delegate = ProxySelector.getDefault();

                @Override
                public List<Proxy> select(URI uri) {
                    if (uri.toString().contains("github")) {
                        return Arrays.asList(new Proxy(Type.HTTP, InetSocketAddress.createUnresolved(host, port)));
                    }
                    return delegate == null ? Arrays.asList(Proxy.NO_PROXY) : delegate.select(uri);
                }

                @Override
                public void connectFailed(URI uri, SocketAddress sa, IOException ioe) {
                    if (delegate != null) {
                        delegate.connectFailed(uri, sa, ioe);
                    }
                }
            });
        }
    }

    @Override
    public void gitInit(String filePath) {
        File file = new File(filePath);
        file.mkdirs();
        this.gitExternalService.gitInit(filePath);
    }

    @Override
    public void add(String addFilePath, String rootPath) {
        this.gitExternalService.add(addFilePath,
            rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator);
    }

    @Override
    public String commit(String message, String rootPath, CurrentUserBean currentUserBean) {
        Tenant tenant = this.tenantRepository.findById(currentUserBean.getTenantId()).get();
        TenantUser tenantUser = this.tenantUserRepository.findByTenantAndUserId(tenant, currentUserBean.getUserId());
        // If no user information is available, display tenant name
        if (tenantUser == null) {
            tenantUser = new TenantUser();
            tenantUser.setName(tenant.getName());
        }
        return this.gitExternalService.commit(message,
            rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator, tenantUser);
    }

    @Override
    public String addAndCommit(String addFilePath, String message, String rootPath, CurrentUserBean currentUserBean) {
        Tenant tenant = this.tenantRepository.findById(currentUserBean.getTenantId()).get();
        TenantUser tenantUser = this.tenantUserRepository.findByTenantAndUserId(tenant, currentUserBean.getUserId());
        // If no user information is available, display tenant name
        if (tenantUser == null) {
            tenantUser = new TenantUser();
            tenantUser.setName(tenant.getName());
        }
        // Check large files
        this.gitExternalService.checkFilesSize(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator);
        this.gitExternalService.add(addFilePath,
            rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator);
        return this.gitExternalService.commit(message,
            rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator, tenantUser);
    }

    @Override
    public List<GitStatusDTO> status(String rootPath) {
        return this.gitExternalService.status(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator);
    }

    @Override
    public HashMap<String, Object> statusRemoteBranch(String rootPath) {
            return this.gitExternalService.statusRemoteBranch(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator);
    }

    @Override
    public List<GitCommitDTO> log(String rootPath, String commitId, Integer pageCount) {
        return this.gitExternalService.log(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator,
            commitId, pageCount);
    }

    @Override
    public List<GitBranchDTO> branch(String rootPath) {
        return this.gitExternalService.branch(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator);
    }

    @Override
    public GitBranchDTO getCurrentBranch(String rootPath) {
        return this.gitExternalService.getCurrentBranch(
            rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator);
    }

    @Override
    public void createBranch(String rootPath, GitCreateBranchDTO gitBranchDTO) {
        this.gitExternalService.createBranch(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator,
            gitBranchDTO);
    }

    @Override
    public void forkSnapshotFile(String codeZoneRootPath, String commitId, String toPath, String ignore) {
        // 1.cp file
        this.fileUtilService.copy(codeZoneRootPath, toPath, ignore);
        // 2.revert
        this.gitExternalService.revert(toPath, null);
        // 3.reset commit
        this.gitExternalService.reset(toPath, commitId);
        // 4.rm .git
        this.fileUtilService.delete(toPath + ".git");
    }

    @Override
    public List<GitDiffFileDTO> show(String rootPath, String commitId) {
        return this.gitExternalService.show(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator,
            commitId);
    }

    @Override
    public void reset(String rootPath, String commitId, CurrentUserBean currentUserBean) {
        this.gitExternalService.reset(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator, commitId);
    }

    @Override
    public void revert(String rootPath, String commitId, CurrentUserBean currentUserBean) {
        this.gitExternalService.revert(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator,
            commitId);
    }

    @Override
    public GitShowFileDTO showFile(String rootPath, String commitId, String path) {
        String content = this.gitExternalService.showFile(
            rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator, commitId, path);
        GitShowFileDTO gitShowFileDTO = new GitShowFileDTO();
        gitShowFileDTO.setContent(content);
        return gitShowFileDTO;
    }

    @Override
    public String getLastCommitId(String path) {
        return this.gitExternalService.getLastCommitId(
            path + Constant.CODE_ZONE_SOURCE_PATH);
    }

    @Override
    public void fetch(String rootPath, GitFetchDTO gitFetchDTO, CurrentUserBean currentUserBean) {
        this.gitExternalService.fetch(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator,
            gitFetchDTO);
    }

    @Override
    public void pull(String rootPath, GitPullDTO gitPullDTO, CurrentUserBean currentUserBean) {
        this.gitExternalService.pull(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator,
            gitPullDTO);
    }

    @Override
    public void setRemoteUrl(String rootPath, GitSetRemoteUrlDTO gitSetRemoteUrlDTO, CurrentUserBean currentUserBean) {
        this.gitExternalService.setRemoteUrl(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator,
            gitSetRemoteUrlDTO.getRemoteName(),gitSetRemoteUrlDTO.getRemoteUrl());
    }

    @Override
    public void addRemoteUrl(String rootPath, GitSetRemoteUrlDTO gitSetRemoteUrlDTO, CurrentUserBean currentUserBean) {
        this.gitExternalService.addRemoteUrl(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator,
            gitSetRemoteUrlDTO.getRemoteName(),gitSetRemoteUrlDTO.getRemoteUrl());
    }

    @Override
    public void createAndCheckBranch(String rootPath, GitCreateAndCheckBranchDTO gitCreateAndCheckBranchDTO) {
        this.gitExternalService.createAndCheckBranch(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator,
            gitCreateAndCheckBranchDTO);
    }

    @Override
    public Map<String, String> asyncCreateAndCheckBranch(Long id,String rootPath, GitCreateAndCheckBranchDTO gitCreateAndCheckBranchDTO) {
        // 添加 checkout 锁 , 避免客户端因网络抖动而重复调用
        String redisLockKey =  RedisPrefix.CHECKOUT_BRANCH_LOCK + id;
        if (!redisExternalService.getLock(redisLockKey, 3)) {
            log.warn("asyncCreateAndCheckBranch, Get lock key fail：{}", redisLockKey);
            throw new CustomRuntimeException("asyncCreateAndCheckBranch, Get lock key fail: "+redisLockKey);
        }
        Map<String, String> resultMap = new HashMap<>();
        String taskId = String.valueOf(id);
        String redisKey = RedisPrefix.CHECKOUT_BRANCH_TASK + taskId;
        resultMap.put("taskId", taskId);

        // 获取当前任务状态，并添加空值检查
        String currentStatus = redisRedissonUtil.get(redisKey);

        // 如果任务已经在运行中，则直接返回任务ID（目前业务只有 codeZoneID ）
        if (currentStatus != null && currentStatus.equals(Constant.CHECKOUT_TASK_RESULT.CHECKOUT_BRANCH_STATUS_PROCESSING)) {
            redisExternalService.releaseLock(redisLockKey);
            return resultMap;
        }

        // 写入到缓存
        log.info("asyncCreateAndCheckBranch, entity Id :{} , taskId: {}",id,taskId);
        redisRedissonUtil.set(redisKey, Constant.CHECKOUT_TASK_RESULT.CHECKOUT_BRANCH_STATUS_PROCESSING, 3600, TimeUnit.SECONDS); // Use CLONING status initially, similar to asyncClone

        // Execute asynchronously
        CompletableFuture.runAsync(() -> {
            try {
                // Call the existing synchronous method
                this.gitExternalService.createAndCheckBranch(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator, gitCreateAndCheckBranchDTO);
                // If successful, update Redis status
                redisRedissonUtil.set(redisKey, CHECKOUT_TASK_RESULT.CHECKOUT_BRANCH_STATUS_COMPLETED, 3600,TimeUnit.SECONDS); // Use COMPLETED status
            } catch (Exception e) {
                log.error("asyncCreateAndCheckBranch failure for codeZoneId: {} , taskId: {}, error: {}", id, taskId, e.getMessage(), e);
                redisRedissonUtil.set(redisKey, CHECKOUT_TASK_RESULT.CHECKOUT_BRANCH_STATUS_FAILED, 3600, TimeUnit.SECONDS);
            }
        });
        redisExternalService.releaseLock(redisLockKey);
        return resultMap;
    }

    @Override
    public void createDefaultBranch(String rootPath, String branchName) {
        this.gitExternalService.createDefaultBranch(rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator, branchName);
    }
}
