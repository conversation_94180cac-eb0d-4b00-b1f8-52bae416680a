package com.dao42.paas.service.meta;

import com.dao42.paas.model.meta.MetaEnvironment;

/**
 * @meta环境同步服务接口
 *
 * <AUTHOR>
 */
public interface MetaEnvironmentSyncService {

    /**
     * 同步指定的@meta环境
     *
     * @param metaEnvironment @meta环境
     */
    void syncEnvironment(MetaEnvironment metaEnvironment);

    /**
     * 同步所有@meta环境
     */
    void syncAllEnvironments();

    /**
     * 检查@meta环境是否需要更新
     *
     * @param metaEnvironment @meta环境
     * @return 是否需要更新
     */
    boolean needsUpdate(MetaEnvironment metaEnvironment);

    /**
     * 从S3下载@meta环境
     *
     * @param s3Path S3路径
     * @param localPath 本地路径
     */
    void downloadFromS3(String s3Path, String localPath);

    /**
     * 验证@meta环境完整性
     *
     * @param metaEnvironment @meta环境
     * @return 是否完整
     */
    boolean validateEnvironment(MetaEnvironment metaEnvironment);
}
