package com.dao42.paas.service.meta.impl;

import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.enums.MetaEnvironmentStatus;
import com.dao42.paas.model.meta.MetaEnvironment;
import com.dao42.paas.repository.meta.MetaEnvironmentRepository;
import com.dao42.paas.service.meta.MetaEnvironmentSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @meta环境同步服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MetaEnvironmentSyncServiceImpl implements MetaEnvironmentSyncService {

    private final MetaEnvironmentRepository metaEnvironmentRepository;
    private final SystemProperties systemProperties;

    @Override
    @Async
    public void syncEnvironment(MetaEnvironment metaEnvironment) {
        try {
            log.info("开始同步@meta环境: {}", metaEnvironment.getName());
            
            // 更新状态为同步中
            metaEnvironment.setStatus(MetaEnvironmentStatus.SYNCING);
            metaEnvironmentRepository.save(metaEnvironment);

            // 准备本地缓存目录
            String localCachePath = prepareLocalCachePath(metaEnvironment);
            metaEnvironment.setLocalCachePath(localCachePath);

            // 从S3下载环境文件
            downloadFromS3(metaEnvironment.getS3Path(), localCachePath);

            // 验证环境完整性
            if (!validateEnvironment(metaEnvironment)) {
                throw new RuntimeException("@meta环境验证失败");
            }

            // 更新环境信息
            updateEnvironmentInfo(metaEnvironment, localCachePath);

            // 更新状态为可用
            metaEnvironment.setStatus(MetaEnvironmentStatus.AVAILABLE);
            metaEnvironment.setLastSyncedAt(LocalDateTime.now());
            metaEnvironmentRepository.save(metaEnvironment);

            log.info("@meta环境同步完成: {}", metaEnvironment.getName());

        } catch (Exception e) {
            log.error("@meta环境同步失败: {}", metaEnvironment.getName(), e);
            
            // 更新状态为失败
            metaEnvironment.setStatus(MetaEnvironmentStatus.FAILED);
            metaEnvironmentRepository.save(metaEnvironment);
        }
    }

    @Override
    public void syncAllEnvironments() {
        try {
            List<MetaEnvironment> environments = metaEnvironmentRepository.findAll();
            
            for (MetaEnvironment env : environments) {
                if (needsUpdate(env)) {
                    syncEnvironment(env);
                }
            }
            
            log.info("所有@meta环境同步检查完成");

        } catch (Exception e) {
            log.error("同步所有@meta环境失败", e);
        }
    }

    @Override
    public boolean needsUpdate(MetaEnvironment metaEnvironment) {
        try {
            // 检查是否从未同步过
            if (metaEnvironment.getLastSyncedAt() == null) {
                return true;
            }

            // 检查是否超过更新间隔
            int updateIntervalDays = systemProperties.getImage().getMeta().getUpdateIntervalDays();
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(updateIntervalDays);
            
            if (metaEnvironment.getLastSyncedAt().isBefore(cutoffTime)) {
                return true;
            }

            // 检查本地缓存是否存在
            if (metaEnvironment.getLocalCachePath() == null || 
                !Files.exists(Paths.get(metaEnvironment.getLocalCachePath()))) {
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("检查@meta环境更新需求失败: {}", metaEnvironment.getName(), e);
            return true; // 出错时默认需要更新
        }
    }

    @Override
    public void downloadFromS3(String s3Path, String localPath) {
        try {
            // 使用AWS CLI下载S3文件
            ProcessBuilder pb = new ProcessBuilder(
                "aws", "s3", "sync", s3Path, localPath, "--delete"
            );
            
            Process process = pb.start();
            int exitCode = process.waitFor();
            
            if (exitCode != 0) {
                throw new RuntimeException("S3下载失败，退出码: " + exitCode);
            }
            
            log.debug("S3下载完成: {} -> {}", s3Path, localPath);

        } catch (IOException | InterruptedException e) {
            log.error("S3下载失败: {} -> {}", s3Path, localPath, e);
            throw new RuntimeException("S3下载失败", e);
        }
    }

    @Override
    public boolean validateEnvironment(MetaEnvironment metaEnvironment) {
        try {
            String localPath = metaEnvironment.getLocalCachePath();
            if (localPath == null || !Files.exists(Paths.get(localPath))) {
                return false;
            }

            // 检查关键目录和文件是否存在
            Path envPath = Paths.get(localPath);
            
            // 检查是否为目录
            if (!Files.isDirectory(envPath)) {
                return false;
            }

            // 检查是否有内容
            try (var stream = Files.list(envPath)) {
                if (stream.findAny().isEmpty()) {
                    return false;
                }
            }

            // 可以添加更多验证逻辑，比如检查特定文件、计算校验和等

            return true;

        } catch (Exception e) {
            log.error("验证@meta环境失败: {}", metaEnvironment.getName(), e);
            return false;
        }
    }

    /**
     * 定期同步@meta环境 (每天凌晨2点)
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void scheduledSync() {
        log.info("开始定期@meta环境同步检查");
        syncAllEnvironments();
    }

    /**
     * 准备本地缓存路径
     */
    private String prepareLocalCachePath(MetaEnvironment metaEnvironment) throws IOException {
        String basePath = systemProperties.getImage().getMeta().getLocalCachePath();
        String envPath = basePath + "/" + metaEnvironment.getName() + "/" + metaEnvironment.getVersion();
        
        Path path = Paths.get(envPath);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
        }
        
        return envPath;
    }

    /**
     * 更新环境信息
     */
    private void updateEnvironmentInfo(MetaEnvironment metaEnvironment, String localPath) {
        try {
            Path envPath = Paths.get(localPath);
            
            // 计算目录大小和文件数量
            final long[] sizeAndCount = {0, 0};
            
            Files.walk(envPath)
                .filter(Files::isRegularFile)
                .forEach(file -> {
                    try {
                        sizeAndCount[0] += Files.size(file); // 总大小
                        sizeAndCount[1]++; // 文件数量
                    } catch (IOException e) {
                        log.warn("计算文件大小失败: {}", file, e);
                    }
                });
            
            metaEnvironment.setSizeBytes(sizeAndCount[0]);
            metaEnvironment.setFileCount((int) sizeAndCount[1]);
            
            log.debug("@meta环境信息更新: name={}, size={}, files={}", 
                     metaEnvironment.getName(), sizeAndCount[0], sizeAndCount[1]);

        } catch (Exception e) {
            log.error("更新@meta环境信息失败: {}", metaEnvironment.getName(), e);
        }
    }
}
