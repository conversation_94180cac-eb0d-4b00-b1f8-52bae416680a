package com.dao42.paas.service.resource;

import com.dao42.paas.common.enums.URLResourceStatus;
import com.dao42.paas.common.enums.URLResourceType;
import com.dao42.paas.enums.DockerURLResourceTypeEnum;
import com.dao42.paas.enums.ProtocolEnum;
import com.dao42.paas.exception.KongRequestException;
import com.dao42.paas.external.sdk.dto.resource.URLResourceDTO;
import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.docker.DockerURLResource;
import com.dao42.paas.model.resource.URLResource;
import com.dao42.paas.model.resource.URLSuffix;
import com.dao42.paas.repository.docker.DockerURLResourceRepository;
import com.dao42.paas.repository.resource.URLResourceRepository;
import com.vladmihalcea.concurrent.Retry;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

@RequiredArgsConstructor
@Slf4j
@Service
public class URLResourceService {

    private final URLResourceRepository urlResourceRepository;

    private final DockerURLResourceRepository dockerURLResourceRepository;

    private final GatewayService gatewayService;

    @Value("${kong.domain.lsp}")
    private String lspDomain;

    @Value("${kong.domain.app}")
    private String appDomain;

    @Value("${kong.domain.ssh}")
    private String sshDomain;

    @Value("${kong.domain.agentserver}")
    private String agentServerDomain;

    /**
     * 请求资源
     *
     * @param docker 请求资源的对象
     */
    public void request(DockerContainer docker) throws KongRequestException {
        // 服务 URL
//        if (docker.getHostPort() > 0) {
//            URLResource serviceUrl = this.getServiceUrlResource(docker);
//            gatewayService.bind(serviceUrl, docker.getDockerServer().getIp(), docker.getHostPort(), ProtocolEnum.HTTP);
//        }
        // LSP URL
        if (docker.getLspPort() > 0) {
            URLResource lspUrl = this.getLspUrlResource(docker);
            gatewayService.bind(lspUrl, docker.getDockerServer().getIp(), docker.getLspPort(),ProtocolEnum.HTTP);
        }
        // SSH　URL　
        if (docker.getSshPort() > 0) {
            URLResource sshUrl = this.getSshUrlResource(docker);
            gatewayService.bind(sshUrl, docker.getDockerServer().getIp(), docker.getSshPort(),ProtocolEnum.TCP);
        }
        // 用户容器 URL
        if (docker.getAgentServerPort() > 0) {
            URLResource agentServerUrl = this.getAgentServerUrlResource(docker);
            gatewayService.bind(agentServerUrl, docker.getDockerServer().getIp(), docker.getAgentServerPort(),ProtocolEnum.HTTP);
        }
    }

    public void recycle(DockerContainer docker) {
        recycle(docker, false);
    }

    /**
     * 归还资源
     *
     * @param docker 请求资源的对象
     */
    public void recycle(DockerContainer docker, boolean del) {
        List<DockerURLResource> urlResources =
            dockerURLResourceRepository.findAllByDockerContainerId(docker.getId());
        for (DockerURLResource dockerURLResource : urlResources) {
            this.recycleURLResource(dockerURLResource.getResourceId());
            if (del) {
                dockerURLResourceRepository.delete(dockerURLResource);
            }
        }
    }

    /**
     * 生成并绑定 **临时** URL资源数据
     *
     * @param dockerContainer docker容器
     * @param type            url资源类型
     * @param tenantId        租户id
     * @return
     */
    @Retry(times = 3, on = {ConstraintViolationException.class})
    public URLResource bindTempURLResource(
        DockerContainer dockerContainer, DockerURLResourceTypeEnum type, Long tenantId) {
        log.info("DockerScheduling-Active, bindTempURLResource, dockerId:{}, type: {}", dockerContainer.getId(), type);
        final URLResource urlResource = this.createTemp(tenantId, type);
        urlResource.setStatus(URLResourceStatus.IDLE);
        urlResourceRepository.save(urlResource);
        final DockerURLResource dockerUrl = dockerURLResourceRepository
            .findByDockerContainerIdAndDockerURLResourceType(dockerContainer.getId(), type);
        if (dockerUrl == null) {
            final DockerURLResource dockerURLResource = new DockerURLResource();
            dockerURLResource.setResourceId(urlResource.getId());
            dockerURLResource.setDockerURLResourceType(type);
            dockerURLResource.setDockerContainerId(dockerContainer.getId());
            this.dockerURLResourceRepository.save(dockerURLResource);
            log.debug(
                "DockerScheduling-Active, bindTempURLResource,insert dockerId:{}, type: {}, DockerURLResourceID: {}",
                dockerContainer.getId(),
                type,
                dockerURLResource.getId());
        } else {
            dockerUrl.setResourceId(urlResource.getId());
            this.dockerURLResourceRepository.save(dockerUrl);
            log.debug(
                "DockerScheduling-Active, bindTempURLResource,update dockerId:{}, type: {}, DockerURLResourceID: {}",
                dockerContainer.getId(),
                type,
                dockerUrl.getId());
        }

        return urlResource;
    }

    /**
     * 创建持久资源
     *
     * @param tenantId
     * @param urlSuffix
     * @param domain
     * @return
     */
    public URLResource createDurable(Long tenantId, URLSuffix urlSuffix, String domain) {
        String url = domain + "." + urlSuffix.getSuffix();
        if (urlResourceRepository.existsByUrl(url)) {
            throw new CustomRuntimeException("URL has existed");
        }
        URLResource urlResource = new URLResource();
        urlResource.setUrl(url);
        urlResource.setMachineId(domain);
        urlResource.setTenantId(tenantId);
        urlResource.setStatus(URLResourceStatus.IDLE);
        urlResource.setType(URLResourceType.DURABLE);
        urlResource.setTenantId(tenantId);
        urlResourceRepository.save(urlResource);
        return urlResource;
    }

    /**
     * 创建临时资源
     *
     * @param tenantId
     * @return
     */
    private URLResource createTemp(Long tenantId, DockerURLResourceTypeEnum type) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        // 默认为服务 URL
        String domain = this.appDomain;
        if (type == DockerURLResourceTypeEnum.LSP_URL_RESOURCE) {
            domain = this.lspDomain;
        }

        if (type == DockerURLResourceTypeEnum.SSH_URL_RESOURCE) {
            domain = this.sshDomain;
        }

        if (type == DockerURLResourceTypeEnum.AGENT_SERVER_RESOURCE) {
            domain = this.agentServerDomain;
        }

        String machineId = DigestUtils.md5DigestAsHex(uuid.getBytes(StandardCharsets.UTF_8));
        String url = machineId + domain;
        URLResource urlResource = new URLResource();
        urlResource.setUrl(url);
        urlResource.setMachineId(machineId);
        urlResource.setTenantId(tenantId);
        urlResource.setStatus(URLResourceStatus.IDLE);
        urlResource.setType(URLResourceType.TEMP);
        urlResource.setTenantId(tenantId);
        urlResourceRepository.save(urlResource);
        return urlResource;
    }

    /**
     * 回收释放url资源
     *
     * @param urlResourceId
     */
    private void recycleURLResource(Long urlResourceId) {
        log.debug("回收释放url资源:{}", urlResourceId);
        urlResourceRepository
            .findById(urlResourceId)
            .ifPresent(
                urlResource -> {
                    switch (urlResource.getType()) {
                        case TEMP:
                            urlResource.setStatus(URLResourceStatus.DELETE);
                            break;
                        case DURABLE:
                            urlResource.setStatus(URLResourceStatus.IDLE);
                            break;
                        default:
                            break;
                    }
                    urlResource = urlResourceRepository.save(urlResource);

                    // 回收挂载到URLResource上的kong
                    gatewayService.unbind(urlResource);
                });
    }

    public URLResourceDTO covertToDTO(URLResource urlResource) {
        URLResourceDTO dto = new URLResourceDTO();
        dto.setId(urlResource.getId().toString());
        dto.setUrl(urlResource.getUrl());
        dto.setType(urlResource.getType());
        dto.setStatus(urlResource.getStatus());
        return dto;
    }

    public URLResource getSshUrlResource(DockerContainer dockerContainer) {
        return getSshUrlResource(dockerContainer, false);
    }

    /**
     * 获取 docker 容器的 ssh url
     *
     * @return
     */
    public URLResource getSshUrlResource(DockerContainer dockerContainer, boolean generate) {
        URLResource urlResource = null;
        DockerURLResource dockerUrlResource = dockerURLResourceRepository.findByDockerContainerIdAndDockerURLResourceType(
            dockerContainer.getId(), DockerURLResourceTypeEnum.SSH_URL_RESOURCE);
        if (dockerUrlResource != null) {
            urlResource = urlResourceRepository.findById(dockerUrlResource.getResourceId()).orElse(null);
        }
        if ((dockerUrlResource == null || urlResource == null) && generate) {
            return this.bindTempURLResource(dockerContainer, DockerURLResourceTypeEnum.SSH_URL_RESOURCE,
                dockerContainer.getTenantId());
        }
        return urlResource != null ? urlResource : new URLResource();
    }


    public URLResource getServiceUrlResource(DockerContainer dockerContainer) {
        return getServiceUrlResource(dockerContainer, false);
    }

    /**
     * 获取docker容器的服务 url
     *
     * @param dockerContainer 容器id
     * @return
     */
    public URLResource getServiceUrlResource(DockerContainer dockerContainer, boolean generate) {
        URLResource urlResource = null;
        DockerURLResource dockerUrlResource = dockerURLResourceRepository.findByDockerContainerIdAndDockerURLResourceType(
            dockerContainer.getId(), DockerURLResourceTypeEnum.SERVICE_URL_RESOURCE);
        if (dockerUrlResource != null) {
            urlResource = urlResourceRepository.findById(dockerUrlResource.getResourceId()).orElse(null);
        }
        if ((dockerUrlResource == null || urlResource == null) && generate) {
            return this.bindTempURLResource(dockerContainer, DockerURLResourceTypeEnum.SERVICE_URL_RESOURCE,
                dockerContainer.getTenantId());
        }
        return urlResource != null ? urlResource : new URLResource();
    }


    public URLResource getLspUrlResource(DockerContainer dockerContainer) {
        return getLspUrlResource(dockerContainer, false);
    }

    /**
     * 获取docker容器的LSP url
     *
     * @param dockerContainer 容器id
     * @return
     */
    public URLResource getLspUrlResource(DockerContainer dockerContainer, boolean generate) {
        URLResource urlResource = null;
        DockerURLResource dockerURLResource = dockerURLResourceRepository.findByDockerContainerIdAndDockerURLResourceType(
            dockerContainer.getId(), DockerURLResourceTypeEnum.LSP_URL_RESOURCE);
        if (dockerURLResource != null) {
            urlResource = urlResourceRepository.findById(dockerURLResource.getResourceId()).orElse(null);
        }
        if ((dockerURLResource == null || urlResource == null) && generate) {
            return this.bindTempURLResource(dockerContainer, DockerURLResourceTypeEnum.LSP_URL_RESOURCE,
                dockerContainer.getTenantId());
        }
        return urlResource != null ? urlResource : new URLResource();
    }

    public URLResource getAgentServerUrlResource(DockerContainer dockerContainer) {
        return getAgentServerUrlResource(dockerContainer, false);
    }

    /**
     * 获取docker容器的Agent server url
     *
     * @param dockerContainer 容器id
     * @return
     */
    public URLResource getAgentServerUrlResource(DockerContainer dockerContainer, boolean generate) {
       URLResource urlResource = null;
        DockerURLResource dockerURLResource = dockerURLResourceRepository.findByDockerContainerIdAndDockerURLResourceType(
            dockerContainer.getId(), DockerURLResourceTypeEnum.AGENT_SERVER_RESOURCE);
        if (dockerURLResource != null) {
            urlResource = urlResourceRepository.findById(dockerURLResource.getResourceId()).orElse(null);
        }
        if ((dockerURLResource == null || urlResource == null) && generate) {
            return this.bindTempURLResource(dockerContainer, DockerURLResourceTypeEnum.AGENT_SERVER_RESOURCE,
                dockerContainer.getTenantId());
        }
        return urlResource != null ? urlResource : new URLResource();
    }
}
