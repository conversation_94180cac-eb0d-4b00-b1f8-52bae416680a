package com.dao42.paas.service.image.impl;

import com.dao42.paas.common.enums.DockerServerStatus;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.dto.image.CacheStatistics;
import com.dao42.paas.enums.ImageCacheStatus;
import com.dao42.paas.model.docker.DockerServer;
import com.dao42.paas.model.image.ImageCache;
import com.dao42.paas.repository.image.ImageCacheRepository;
import com.dao42.paas.service.docker.DockerImageService;
import com.dao42.paas.service.docker.DockerServerService;
import com.dao42.paas.service.image.HotImageCacheManager;
import com.dao42.paas.service.image.ImageCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 热点镜像缓存管理器实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HotImageCacheManagerImpl implements HotImageCacheManager {

    private final ImageCacheRepository imageCacheRepository;
    private final ImageCacheService imageCacheService;
    private final DockerImageService dockerImageService;
    private final DockerServerService dockerServerService;
    private final SystemProperties systemProperties;

    @Override
    public List<ImageCache> identifyHotImages() {
        try {
            int hotImageThreshold = systemProperties.getImage().getCache().getHotImageThreshold();
            return imageCacheRepository.findHotImages(hotImageThreshold);
        } catch (Exception e) {
            log.error("识别热点镜像失败", e);
            return List.of();
        }
    }

    @Override
    @Async
    public void preloadHotImageToAllServers(String imageTag) {
        try {
            List<DockerServer> activeServers = dockerServerService.findAll(DockerServerStatus.ACTIVE);
            List<String> serverIds = activeServers.stream()
                .map(server -> server.getId().toString())
                .collect(Collectors.toList());
            
            preloadHotImageToServers(imageTag, serverIds);
            
        } catch (Exception e) {
            log.error("预拉取热点镜像到所有服务器失败: imageTag={}", imageTag, e);
        }
    }

    @Override
    @Async
    public void preloadHotImageToServers(String imageTag, List<String> serverIds) {
        try {
            log.info("开始预拉取热点镜像: imageTag={}, servers={}", imageTag, serverIds.size());
            
            for (String serverId : serverIds) {
                try {
                    // 检查是否已经存在
                    if (imageCacheService.hasImage(serverId, imageTag)) {
                        log.debug("镜像已存在，跳过预拉取: serverId={}, imageTag={}", serverId, imageTag);
                        continue;
                    }
                    
                    // 异步预拉取
                    imageCacheService.preloadImage(serverId, imageTag);
                    
                } catch (Exception e) {
                    log.error("预拉取镜像失败: serverId={}, imageTag={}", serverId, imageTag, e);
                }
            }
            
            log.info("热点镜像预拉取任务已启动: imageTag={}", imageTag);
            
        } catch (Exception e) {
            log.error("预拉取热点镜像失败: imageTag={}", imageTag, e);
        }
    }

    @Override
    public void cleanupExpiredCaches() {
        try {
            int inactiveDays = systemProperties.getImage().getCache().getInactiveDays();
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(inactiveDays);
            
            List<ImageCache> expiredCaches = imageCacheRepository.findExpiredCaches(cutoffTime);
            
            log.info("开始清理过期镜像缓存，数量: {}", expiredCaches.size());
            
            for (ImageCache cache : expiredCaches) {
                try {
                    // 删除服务器上的镜像
                    dockerImageService.removeImage(
                        cache.getDockerServerId().toString(), 
                        cache.getImageTag()
                    );
                    
                    // 删除缓存记录
                    imageCacheRepository.delete(cache);
                    
                    log.debug("清理过期缓存: serverId={}, imageTag={}", 
                             cache.getDockerServerId(), cache.getImageTag());
                    
                } catch (Exception e) {
                    log.error("清理过期缓存失败: serverId={}, imageTag={}", 
                             cache.getDockerServerId(), cache.getImageTag(), e);
                }
            }
            
            log.info("过期镜像缓存清理完成，清理数量: {}", expiredCaches.size());
            
        } catch (Exception e) {
            log.error("清理过期镜像缓存失败", e);
        }
    }

    @Override
    public void cleanupLowUsageCaches() {
        try {
            // 查找所有可用的缓存
            List<ImageCache> availableCaches = imageCacheRepository.findByStatus(ImageCacheStatus.AVAILABLE);
            
            // 按命中次数排序，清理使用率最低的缓存
            List<ImageCache> lowUsageCaches = availableCaches.stream()
                .filter(cache -> cache.getHitCount() <= 1) // 命中次数小于等于1
                .filter(cache -> {
                    // 排除热点镜像
                    int hotThreshold = systemProperties.getImage().getCache().getHotImageThreshold();
                    return cache.getHitCount() < hotThreshold;
                })
                .collect(Collectors.toList());
            
            log.info("开始清理低使用率镜像缓存，数量: {}", lowUsageCaches.size());
            
            for (ImageCache cache : lowUsageCaches) {
                try {
                    // 删除服务器上的镜像
                    dockerImageService.removeImage(
                        cache.getDockerServerId().toString(), 
                        cache.getImageTag()
                    );
                    
                    // 删除缓存记录
                    imageCacheRepository.delete(cache);
                    
                    log.debug("清理低使用率缓存: serverId={}, imageTag={}, hitCount={}", 
                             cache.getDockerServerId(), cache.getImageTag(), cache.getHitCount());
                    
                } catch (Exception e) {
                    log.error("清理低使用率缓存失败: serverId={}, imageTag={}", 
                             cache.getDockerServerId(), cache.getImageTag(), e);
                }
            }
            
            log.info("低使用率镜像缓存清理完成，清理数量: {}", lowUsageCaches.size());
            
        } catch (Exception e) {
            log.error("清理低使用率镜像缓存失败", e);
        }
    }

    @Override
    public void optimizeStorageCost() {
        try {
            log.info("开始存储成本优化");
            
            // 1. 清理过期缓存
            cleanupExpiredCaches();
            
            // 2. 清理低使用率缓存
            cleanupLowUsageCaches();
            
            // 3. 预拉取热点镜像
            List<ImageCache> hotImages = identifyHotImages();
            for (ImageCache hotImage : hotImages) {
                preloadHotImageToAllServers(hotImage.getImageTag());
            }
            
            // 4. 重复镜像去重 (相同镜像在同一服务器上的多个版本)
            deduplicateImages();
            
            log.info("存储成本优化完成");
            
        } catch (Exception e) {
            log.error("存储成本优化失败", e);
        }
    }

    @Override
    public CacheStatistics getCacheStatistics() {
        try {
            CacheStatistics stats = new CacheStatistics();
            
            // 基本统计
            stats.setTotalCacheCount(imageCacheRepository.findAll().size());
            stats.setAvailableCacheCount(imageCacheRepository.countByStatus(ImageCacheStatus.AVAILABLE));
            stats.setFailedCacheCount(imageCacheRepository.countByStatus(ImageCacheStatus.FAILED));
            stats.setPullingCacheCount(imageCacheRepository.countByStatus(ImageCacheStatus.PULLING));
            
            // 热点镜像统计
            int hotThreshold = systemProperties.getImage().getCache().getHotImageThreshold();
            stats.setHotImageCount(imageCacheRepository.findHotImages(hotThreshold).size());
            
            // 过期缓存统计
            int inactiveDays = systemProperties.getImage().getCache().getInactiveDays();
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(inactiveDays);
            stats.setExpiredCacheCount(imageCacheRepository.findExpiredCaches(cutoffTime).size());
            
            // 大小统计
            List<ImageCache> allCaches = imageCacheRepository.findByStatus(ImageCacheStatus.AVAILABLE);
            if (!allCaches.isEmpty()) {
                long totalSize = allCaches.stream()
                    .filter(cache -> cache.getCacheSize() != null)
                    .mapToLong(ImageCache::getCacheSize)
                    .sum();
                
                stats.setTotalCacheSize(totalSize);
                stats.setAverageImageSize(totalSize / allCaches.size());
                
                stats.setMaxImageSize(allCaches.stream()
                    .filter(cache -> cache.getCacheSize() != null)
                    .mapToLong(ImageCache::getCacheSize)
                    .max().orElse(0));
                
                stats.setMinImageSize(allCaches.stream()
                    .filter(cache -> cache.getCacheSize() != null)
                    .mapToLong(ImageCache::getCacheSize)
                    .min().orElse(0));
            }
            
            // 服务器统计
            List<DockerServer> activeServers = dockerServerService.findAll(DockerServerStatus.ACTIVE);
            stats.setServerCount(activeServers.size());
            
            if (activeServers.size() > 0) {
                stats.setAverageCachePerServer((double) stats.getTotalCacheCount() / activeServers.size());
            }
            
            // 命中率计算 (简化版本)
            long totalHits = allCaches.stream()
                .mapToLong(ImageCache::getHitCount)
                .sum();
            
            if (stats.getTotalCacheCount() > 0) {
                stats.setHitRate((double) totalHits / stats.getTotalCacheCount());
            }
            
            return stats;
            
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            return new CacheStatistics();
        }
    }

    @Override
    public void forceCleanupImageCache(String serverId, String imageTag) {
        try {
            // 删除服务器上的镜像
            dockerImageService.removeImage(serverId, imageTag);
            
            // 删除缓存记录
            imageCacheService.removeImageCache(serverId, imageTag);
            
            log.info("强制清理镜像缓存完成: serverId={}, imageTag={}", serverId, imageTag);
            
        } catch (Exception e) {
            log.error("强制清理镜像缓存失败: serverId={}, imageTag={}", serverId, imageTag, e);
            throw new RuntimeException("强制清理镜像缓存失败", e);
        }
    }

    /**
     * 定期清理过期缓存 (每天凌晨3点)
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void scheduledCleanupExpiredCaches() {
        if (systemProperties.getImage().getCache().isCleanupEnabled()) {
            log.info("开始定期清理过期镜像缓存");
            cleanupExpiredCaches();
        }
    }

    /**
     * 定期预拉取热点镜像 (每6小时)
     */
    @Scheduled(cron = "0 0 */6 * * ?")
    public void scheduledPreloadHotImages() {
        if (systemProperties.getImage().getCache().isPreloadEnabled()) {
            log.info("开始定期预拉取热点镜像");
            List<ImageCache> hotImages = identifyHotImages();
            for (ImageCache hotImage : hotImages) {
                preloadHotImageToAllServers(hotImage.getImageTag());
            }
        }
    }

    /**
     * 重复镜像去重
     */
    private void deduplicateImages() {
        try {
            // 按镜像标签分组，找出重复的镜像
            Map<String, List<ImageCache>> imageGroups = imageCacheRepository
                .findByStatus(ImageCacheStatus.AVAILABLE)
                .stream()
                .collect(Collectors.groupingBy(ImageCache::getImageTag));
            
            for (Map.Entry<String, List<ImageCache>> entry : imageGroups.entrySet()) {
                List<ImageCache> caches = entry.getValue();
                if (caches.size() > 1) {
                    // 保留命中次数最高的，删除其他的
                    caches.sort((a, b) -> Integer.compare(b.getHitCount(), a.getHitCount()));
                    
                    for (int i = 1; i < caches.size(); i++) {
                        ImageCache cache = caches.get(i);
                        try {
                            dockerImageService.removeImage(
                                cache.getDockerServerId().toString(), 
                                cache.getImageTag()
                            );
                            imageCacheRepository.delete(cache);
                            
                            log.debug("去重删除镜像: serverId={}, imageTag={}", 
                                     cache.getDockerServerId(), cache.getImageTag());
                        } catch (Exception e) {
                            log.error("去重删除镜像失败: serverId={}, imageTag={}", 
                                     cache.getDockerServerId(), cache.getImageTag(), e);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("重复镜像去重失败", e);
        }
    }
}
