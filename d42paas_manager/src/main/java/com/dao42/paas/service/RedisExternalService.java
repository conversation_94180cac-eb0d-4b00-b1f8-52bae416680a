package com.dao42.paas.service;

import com.dao42.paas.common.constants.RedisPrefix;
import com.dao42.paas.common.message.DockerInfoMQMsg;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.constants.Constant;
import com.dao42.paas.constants.RedisConstant;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.utils.JsonUtil;
import com.github.dockerjava.api.model.Statistics;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.TimeoutUtils;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class RedisExternalService {

    private static Long THIRTY_SECONDS = 30L;
    private static Integer IDE_SERVICE_HEARTBEAT_SECONDS = 15;
    private static Integer PLAYGROUND_HEARTBEAT_SECONDS = 5;
    private final SystemProperties systemProperties;
    private final StringRedisTemplate stringRedisTemplate;

    public void set(String key, String value) {
        stringRedisTemplate.opsForValue().set(key, value);
    }

    public void set(String key, String value, Duration duration) {
        log.debug("add redis key:{},value:{}", key, value);
        log.debug("hasMillis:{}", TimeoutUtils.hasMillis(duration));
        stringRedisTemplate.opsForValue().set(key, value, duration);
    }

    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    public List<String> getList(String key) {
        Set<String> keySet = stringRedisTemplate.keys(key);
        if (keySet != null) {
            return new ArrayList<>(keySet);
        }
        return null;
    }

    public int size(String pattern) {
        Set<String> keys = stringRedisTemplate.keys(pattern);
        if (keys == null) {
            return 0;
        }
        return keys.size();
    }

    public void startPlaygroundMonitor(Playground playground) {
        if (playground == null) {
            return;
        }
    	//批量创建 48小时内定时器不处理,当使用一次 批量状态会改为fasle
        Long batchCreate = systemProperties.getPlayground().getInactiveSeconds().getBatchCreate();
        if(batchCreate == null) {
        	batchCreate= 60 * 60 * 48L;
        }
		if (playground != null && playground.getDockerContainer() != null && playground.getDockerContainer().isPreCreate()
				&& System.currentTimeMillis() - playground.getCreatedDate().getTime() < batchCreate * 1000) {
			return;
		}

        Long seconds = systemProperties.getPlayground().getInactiveSeconds().getCodeZone();
        switch (playground.getBindType()) {
            case CODE_ZONE:
                seconds = systemProperties.getPlayground().getInactiveSeconds().getCodeZone();
                break;
            case CODE_ZONE_SNAPSHOT:
                seconds = systemProperties.getPlayground().getInactiveSeconds().getCodeZoneSnapshot();
                break;
            case CODE_ZONE_DUPLICATE:
                seconds = systemProperties.getPlayground().getInactiveSeconds().getCodeZoneCopy();
                break;
            default:
                log.error("Unknown bind type :{}", playground.getBindType());
                break;
        }
        try {
            String key = RedisPrefix.PREFIX_PLAYGROUND_ACTIVE + playground.getId();
            this.set(key, "1", Duration.ofSeconds(seconds));
        } catch (Exception e) {
            log.error("Start monitor playground active exception", e);
        }
    }

    public void stopPlaygroundMonitor(Long playgroundId) {
        String key = RedisPrefix.PREFIX_PLAYGROUND_ACTIVE + playgroundId;
        stringRedisTemplate.delete(key);
    }

    public Boolean hasPlaygroundMonitor(Long playgroundId) {
        String key = RedisPrefix.PREFIX_PLAYGROUND_ACTIVE + playgroundId;
        return stringRedisTemplate.hasKey(key);
    }

    public void startDockerMonitor(DockerContainer dockerContainer) {
        if (dockerContainer == null) {
            return;
        }
        Long seconds = THIRTY_SECONDS;
        try {
            String key = RedisPrefix.PREFIX_DOCKER_ACTIVE + dockerContainer.getId();
            this.set(key, "1", Duration.ofSeconds(seconds));
        } catch (Exception e) {
            log.error("Start monitor playground active exception", e);
        }
    }

    public void stopDockerMonitor(Long dockerId) {
        String key = RedisPrefix.PREFIX_DOCKER_ACTIVE + dockerId;
        log.info("remove docker active key:" + dockerId);
        stringRedisTemplate.delete(key);
    }

    public boolean havePlaygroundMonitor(Long playgroundId) {
        String key = RedisPrefix.PREFIX_PLAYGROUND_ACTIVE + playgroundId;
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(key));
    }


    public void setRunCmdResult(String runId, String content) {
        this.set(RedisConstant.RUN_CMD_RESULT + runId, content, Duration.ofSeconds(60000));
    }

    public String getRunCmdResult(String runId) {
        return stringRedisTemplate.opsForValue().get(RedisConstant.RUN_CMD_RESULT + runId);
    }

    /**
     * 【写】测试用例运行结果
     *
     * @param runId   runId
     * @param content content
     */
    public void setUnitTestResult(String runId, String content) {
        this.set(RedisConstant.TEST_CASE_DIR + runId, content, Duration.ofMillis(Constant.HOUR_TO_MILLISECOND));
    }

    /**
     * 【读】测试用例运行结果
     *
     * @param runId runId
     * @return
     */
    public String getUnitTestResult(String runId) {
        return stringRedisTemplate.opsForValue().get(RedisConstant.TEST_CASE_DIR + runId);
    }


    public boolean getLock(String lockName, long lockSeconds) {
        return Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
            .setIfAbsent(lockName, "1", Duration.ofSeconds(lockSeconds)));
    }

    public void releaseLock(String lockName) {
        stringRedisTemplate.delete(lockName);
    }

    public void removeKeys(String pattern) {
        stringRedisTemplate.keys(pattern).forEach(stringRedisTemplate::delete);
    }

    public void setMQMessageWait(String messageId, int seconds) {
        this.set(RedisPrefix.PREFIX_MQ_WAIT + messageId, "1", Duration.ofSeconds(seconds));
    }

    public boolean hasMQWait(String messageId) {
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(RedisPrefix.PREFIX_MQ_WAIT + messageId));
    }

    public boolean hasMQReplay(String messageId) {
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(RedisPrefix.PREFIX_MQ_REPLY_CONTENT + messageId));
    }

    public String getMQReplayContent(String messageId) {
        return this.get(RedisPrefix.PREFIX_MQ_REPLY_CONTENT + messageId);
    }

    public void setMQReplyContent(String messageId, String content) {
        this.set(RedisPrefix.PREFIX_MQ_REPLY_CONTENT + messageId, content, Duration.ofSeconds(10));
    }

    public void setMQWaitDockerInfo(Long dockerId) {
        log.debug("redis >>> set {}", RedisPrefix.PREFIX_MQ_WAIT_DOCKER_INFO + dockerId);
        this.set(
            RedisPrefix.PREFIX_MQ_WAIT_DOCKER_INFO + dockerId,
            String.valueOf(dockerId),
            Duration.ofSeconds(30));
    }

    public String getMQWaitDockerInfo(String messageId) {
        String get = this.get(RedisPrefix.PREFIX_MQ_WAIT_DOCKER_INFO + messageId);
        log.debug("redis >>> get {} -> {}", RedisPrefix.PREFIX_MQ_WAIT_DOCKER_INFO + messageId, get);
        return get;
    }

    public void removeMQWaitDockerInfo(String messageId) {
        log.debug("redis >>> remove {}", RedisPrefix.PREFIX_MQ_WAIT_DOCKER_INFO + messageId);
        this.stringRedisTemplate.delete(RedisPrefix.PREFIX_MQ_WAIT_DOCKER_INFO + messageId);
    }

    /**
     * 存储docker容器监控信息
     *
     * @param dockerId
     * @param statistics
     * @param duration
     */
    public void setDockerMonitorInfo(String dockerId, Statistics statistics, Duration duration) {
        this.set(RedisPrefix.PREFIX_DOCKER_STATS + dockerId, JsonUtil.pojoToJson(statistics), duration);
    }

    public void clearMQMessageKey(String messageId) {
        stringRedisTemplate.delete(RedisPrefix.PREFIX_MQ_WAIT + messageId);
        stringRedisTemplate.delete(RedisPrefix.PREFIX_MQ_REPLY + messageId);
        stringRedisTemplate.delete(RedisPrefix.PREFIX_MQ_REPLY_CONTENT + messageId);
    }

    public String getDockerInfo(Long dockerId) {
        return stringRedisTemplate.opsForValue().get(RedisConstant.DOCKER_INFO + dockerId);
    }

    public Set<String> getDockerInfoList() {
        return stringRedisTemplate.keys(RedisConstant.DOCKER_INFO + "*");
    }

    public void removeDockerInfo(Long dockerId) {
        stringRedisTemplate.delete(RedisConstant.DOCKER_INFO + dockerId);
    }

    public void setDockerInfo(DockerInfoMQMsg mqMessage) {
        this.set(RedisPrefix.PREFIX_DOCKER_INFO + mqMessage.getDockerId(), JsonUtil.pojoToJson(mqMessage));
    }

    public void setMiddlewareRunCmdResult(String runId, String content) {
        this.set(RedisConstant.MIDDLEWARE_RUN_CMD_RESULT + runId, content, Duration.ofSeconds(60000));
    }

    public String getMiddlewareRunCmdResult(String runId) {
        return stringRedisTemplate.opsForValue().get(RedisConstant.MIDDLEWARE_RUN_CMD_RESULT + runId);
    }

    public void setMiddlewareHealth(String id, String healthy) {
        this.set(RedisConstant.MIDDLEWARE_HEALTH_RESULT + id, healthy);
    }

    public String getMiddlewareHealth(String id) {
        return this.get(RedisConstant.MIDDLEWARE_HEALTH_RESULT + id);
    }

    public void removeMiddlewareHealth(String id) {
        stringRedisTemplate.delete(RedisConstant.MIDDLEWARE_HEALTH_RESULT + id);
    }

    public void setIdeServerHeartBeat(String code) {
        this.set(RedisPrefix.IDE_SERVER_HEARTBEAT + code, "1", Duration.ofSeconds(IDE_SERVICE_HEARTBEAT_SECONDS));
    }

    /**
     * 激活重试次数++
     *
     * @param playgroundId playgroundId
     */
    public Long incrementPlaygroundActiveRetry(Long playgroundId) {
        return stringRedisTemplate.opsForValue().increment(RedisPrefix.PLAYGROUND_ACTIVE_RETRY + playgroundId);
    }

    /**
     * 获取已重试次数
     *
     * @param playgroundId playgroundId
     * @return 当前重试次数
     */
    public Long getPlaygroundActiveRetry(Long playgroundId) {
        String value = stringRedisTemplate.opsForValue().get(RedisPrefix.PLAYGROUND_ACTIVE_RETRY + playgroundId);
        if (value == null) {
            return null;
        }
        return Long.valueOf(value);
    }

    public Long incrementPlaygroundHeartBeatRetry(Long playgroundId) {
        return stringRedisTemplate.opsForValue().increment(RedisPrefix.PLAYGROUND_HEARTBEAT_RETRY + playgroundId);
    }


    public Long getPlaygroundHeartbeatRetry(Long playgroundId) {
        String value = stringRedisTemplate.opsForValue().get(RedisPrefix.PLAYGROUND_HEARTBEAT_RETRY + playgroundId);
        if (value == null) {
            return null;
        }
        return Long.valueOf(value);
    }

    /**
     * 移除激活重试计数
     *
     * @param playgroundId playgroundId
     */
    public void removePlaygroundActiveRetry(Long playgroundId) {
        stringRedisTemplate.delete(RedisPrefix.PLAYGROUND_ACTIVE_RETRY + playgroundId);
    }

    /**
     * 移除心跳失败重试计数
     *
     * @param playgroundId playgroundId
     */
    public void removePlaygroundHeartbeatRetry(Long playgroundId) {
        stringRedisTemplate.delete(RedisPrefix.PLAYGROUND_HEARTBEAT_RETRY + playgroundId);
    }

    /**
     * 移除 playground 激活信息
     *
     * @param playgroundId playgroundId
     */
    public void removePlaygroundActive(Long playgroundId) {
        stringRedisTemplate.delete(RedisPrefix.PREFIX_PLAYGROUND_ACTIVE + playgroundId);
    }

    public void setPlaygroundHeartbeatWait(Long messageId) {
        this.set(RedisPrefix.PLAYGROUND_HEARTBEAT + messageId, "", Duration.ofSeconds(systemProperties.getPlayground().getHeartbeatSeconds()));
    }

    public void deletePlaygroundHeartbeatWait(Long messageId) {
        stringRedisTemplate.delete(RedisPrefix.PLAYGROUND_HEARTBEAT + messageId);
    }

}
