package com.dao42.paas.service.codezone;

import com.dao42.paas.bean.CurrentUserBean;
import com.dao42.paas.common.ResourcesLimit;
import com.dao42.paas.external.sdk.dto.ForkCodeZoneDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneCreateDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneFileEditDto;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneGithubImportByUrlDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneGithubImportDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneImportDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZonePreCreateRequest;
import com.dao42.paas.external.sdk.dto.codezone.CodeZonePreCreateResponse;
import com.dao42.paas.external.sdk.dto.codezone.CodeZonePushDTO;
import com.dao42.paas.external.sdk.dto.git.GitSetRemoteUrlDTO;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.codezone.CodeZone;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.tenant.Tenant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import io.swagger.models.auth.In;
import org.springframework.web.multipart.MultipartFile;

public interface CodeZoneService {

    /**
     * 创建codeZone
     */
    CodeZone create(CurrentUserBean currentUserBean, String environmentVerId, String unitTestFrameworkId, String usage);

    /**
     * 根据github仓库创建codeZone
     *
     * @param currentUserBean
     * @param codeZoneGithubImportDTO
     * @return
     */
    String createFromGithub(CurrentUserBean currentUserBean, CodeZoneGithubImportDTO codeZoneGithubImportDTO);

    /**
     * 根据github仓库创建codeZone（异步）
     *
     * @param currentUserBean
     * @param codeZoneGithubImportDTO
     * @return
     */
    Map<String, String> createFromGithubAsync(CurrentUserBean currentUserBean,
        CodeZoneGithubImportDTO codeZoneGithubImportDTO);

    /**
     * 根据git仓库创建codeZone（异步）
     *
     * @param currentUserBean
     * @param codeZoneGithubImportDTO
     * @return
     */
    Map<String, String> asyncClone(CurrentUserBean currentUserBean,
        CodeZoneGithubImportByUrlDTO codeZoneGithubImportDTO);

    /**
     * 更新codeZone
     */
    CodeZone update(CodeZone codeZone, String environmentVerId, String unitTestFrameworkId, Integer cpu, Integer memery,
        String purpose);

    CodeZone upgrade(CodeZone codeZone, CodeZoneCreateDTO codeZoneDTO);

    /**
     * forkCodeZone
     *
     * @param codeZone
     * @param userId
     * @param dto
     * @return
     */
    CodeZone fork(CodeZone codeZone, String userId, ForkCodeZoneDTO dto);

    /**
     * 获取指定codeZone的容器。容器不存在时返回NULL
     *
     * @param codeZone
     * @return
     */
    DockerContainer getDockerByCodeZone(CodeZone codeZone);

    /**
     * 导入codeZone
     *
     * @param codeZoneImportDTO
     * @return
     */
    void importCodeZone(CodeZoneImportDTO codeZoneImportDTO, CurrentUserBean tenant);

    void importCodeZoneFile(MultipartFile file, CodeZone codeZone);

    void setImage(String imageCode, CodeZone codeZone);

    /**
     * 下载 codeZone
     *
     * @param codeZone
     * @param request
     * @param response
     */
    void download(CodeZone codeZone, HttpServletRequest request, HttpServletResponse response);

    /**
     * 删除 codeZone
     *
     * @param codeZone
     */
    void delete(CodeZone codeZone);

    CodeZone getCodeZone(Long id);


    /**
     * Pre-create a CodeZone with associated Playground and Git initialization
     *
     * @param request The pre-create request containing necessary parameters
     * @return CodeZonePreCreateResponse containing the created codeZoneId and playgroundId
     */
    CodeZonePreCreateResponse preCreate(CodeZone codeZone, Playground playground);

    /**
     * 添加环境变量
     *
     * @param tenant
     * @param codeZone
     * @param envMap
     * @return
     */
    String setEnvs(Tenant tenant, CodeZone codeZone, HashMap<String, String> envMap);

    /**
     * 文件编辑
     *
     * @param id
     * @param tenant
     * @param codeZoneFileEditDto
     */
    void fileEdit(Long id, CurrentUserBean tenant, CodeZoneFileEditDto codeZoneFileEditDto);

    void updateResources(CodeZone codeZone, ResourcesLimit resourcesLimit);

    void setCodeZoneEnvs(CodeZone codeZone, Map<String, String> envMap);

    String push(CodeZonePushDTO codeZonePushDTO, String rootPath);

    /**
     * Get GitHub import progress and logs by task ID
     *
     * @param taskId The import task ID
     * @return Map containing progress and logs
     */
    Map<String, String> getGithubImportProgress(String taskId);


    /**
     * 查看 切换分支 任务状态
     *
     * @param taskId
     * @return
     */
    Map<String, String> getAsyncCreateAndCheckBranchStatus(String taskId);

    /**
     * Delete codezones by IDs with specific purpose
     *
     * @param ids       List of codeZone IDs
     * @param purpose   Purpose value to check
     * @param rateLimit Whether to enable rate limiting for this operation
     * @return Map containing deleted IDs under key "deletedIds" and skipped IDs under key "skippedIds"
     */
    Map<String, List<Long>> deleteByIdsWithPurpose(Integer expireDays, List<Long> ids, String purpose,
        boolean rateLimit);

    /**
     * Batch delete CodeZones based on time range and expiration days.
     *
     * @param startTime  The start time for the last modification date range (inclusive). Format "yyyy-MM-dd HH:mm:ss".
     *                   Can be null.
     * @param endTime    The end time for the last modification date range (inclusive). Format "yyyy-MM-dd HH:mm:ss".
     *                   Can be null.
     * @param expireDays The number of days after creation for a CodeZone to be considered expired. If null, uses
     *                   default configuration.
     * @param ids        A list of CodeZone IDs to consider for deletion. If null or empty, all CodeZones matching
     *                   time/expiration criteria will be considered.
     * @param purpose    Purpose value to check
     * @param rateLimit  Whether to enable rate limiting for this operation
     * @return A map containing two lists: "deletedIds" for successfully deleted CodeZone IDs, and "skippedIds" for
     * CodeZone IDs that were not deleted because they didn't meet the criteria or were not found.
     */
    Map<String, List<Long>> batchDeleteByTimeRange(String startTime, String endTime, Integer expireDays, List<Long> ids,
        String purpose, boolean rateLimit);

    Map<String, List<Long>> batchDeleteAllByTimeRange(String startTime, String endTime, Integer expireDays,
        String purpose, boolean rateLimit);

    void upgradeSSHFile(CurrentUserBean currentUserBean, CodeZone codeZone, GitSetRemoteUrlDTO gitSetRemoteUrlDTO);

}
