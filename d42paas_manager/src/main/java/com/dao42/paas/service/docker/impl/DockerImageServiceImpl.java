package com.dao42.paas.service.docker.impl;

import com.dao42.paas.common.enums.DockerServerStatus;
import com.dao42.paas.dto.image.CommitOptions;
import com.dao42.paas.dto.image.ImageInfo;
import com.dao42.paas.service.docker.DockerImageService;
import com.dao42.paas.service.docker.DockerClientFactory;
import com.dao42.paas.service.docker.DockerServerService;
import com.dao42.paas.model.docker.DockerServer;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.command.CommitCmd;
import com.github.dockerjava.api.command.InspectImageResponse;
import com.github.dockerjava.api.command.ListImagesCmd;
import com.github.dockerjava.api.command.PullImageCmd;
import com.github.dockerjava.api.command.RemoveImageCmd;
import com.github.dockerjava.api.model.Image;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Docker镜像服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DockerImageServiceImpl implements DockerImageService {

    private final DockerClientFactory dockerClientFactory;
    private final DockerServerService dockerServerService;

    @Override
    public String commit(String containerId, String imageTag, CommitOptions options) {
        try {
            // 获取第一个可用的Docker服务器作为默认服务器
            DockerServer defaultServer = dockerServerService.findAll(DockerServerStatus.ACTIVE).get(0);
            DockerClient dockerClient = dockerClientFactory.get(defaultServer);

            CommitCmd commitCmd = dockerClient.commitCmd(containerId)
                .withRepository(extractRepository(imageTag))
                .withTag(extractTag(imageTag));

            if (options.getAuthor() != null) {
                commitCmd.withAuthor(options.getAuthor());
            }

            if (options.getMessage() != null) {
                commitCmd.withMessage(options.getMessage());
            }

            commitCmd.withPause(options.isPause());

            String imageId = commitCmd.exec();
            log.info("Docker commit成功: containerId={}, imageTag={}, imageId={}",
                    containerId, imageTag, imageId);

            return imageId;
        } catch (Exception e) {
            log.error("Docker commit失败: containerId={}, imageTag={}", containerId, imageTag, e);
            throw new RuntimeException("Docker commit失败", e);
        }
    }

    @Override
    public void pullImage(String serverId, String imageTag) {
        try {
            DockerServer server = dockerServerService.get(Long.parseLong(serverId));
            DockerClient dockerClient = dockerClientFactory.get(server);

            PullImageCmd pullCmd = dockerClient.pullImageCmd(imageTag);
            pullCmd.exec(new PullImageResultCallback()).awaitCompletion();

            log.info("镜像拉取成功: serverId={}, imageTag={}", serverId, imageTag);
        } catch (Exception e) {
            log.error("镜像拉取失败: serverId={}, imageTag={}", serverId, imageTag, e);
            throw new RuntimeException("镜像拉取失败", e);
        }
    }

    @Override
    public void removeLocal(String imageId) {
        try {
            DockerServer defaultServer = dockerServerService.findAll(DockerServerStatus.ACTIVE).get(0);
            DockerClient dockerClient = dockerClientFactory.get(defaultServer);

            RemoveImageCmd removeCmd = dockerClient.removeImageCmd(imageId);
            removeCmd.exec();

            log.info("本地镜像删除成功: imageId={}", imageId);
        } catch (Exception e) {
            log.error("本地镜像删除失败: imageId={}", imageId, e);
            throw new RuntimeException("本地镜像删除失败", e);
        }
    }

    @Override
    public void removeImage(String serverId, String imageTag) {
        try {
            DockerServer server = dockerServerService.get(Long.parseLong(serverId));
            DockerClient dockerClient = dockerClientFactory.get(server);

            RemoveImageCmd removeCmd = dockerClient.removeImageCmd(imageTag);
            removeCmd.exec();

            log.info("服务器镜像删除成功: serverId={}, imageTag={}", serverId, imageTag);
        } catch (Exception e) {
            log.error("服务器镜像删除失败: serverId={}, imageTag={}", serverId, imageTag, e);
            throw new RuntimeException("服务器镜像删除失败", e);
        }
    }

    @Override
    public List<ImageInfo> listImages(String serverId) {
        try {
            DockerServer server = dockerServerService.get(Long.parseLong(serverId));
            DockerClient dockerClient = dockerClientFactory.get(server);

            ListImagesCmd listCmd = dockerClient.listImagesCmd();
            List<Image> images = listCmd.exec();

            return images.stream()
                .map(this::convertToImageInfo)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取镜像列表失败: serverId={}", serverId, e);
            throw new RuntimeException("获取镜像列表失败", e);
        }
    }

    @Override
    public boolean imageExists(String serverId, String imageTag) {
        try {
            DockerServer server = dockerServerService.get(Long.parseLong(serverId));
            DockerClient dockerClient = dockerClientFactory.get(server);

            InspectImageResponse response = dockerClient.inspectImageCmd(imageTag).exec();
            return response != null;
        } catch (Exception e) {
            log.debug("镜像不存在: serverId={}, imageTag={}", serverId, imageTag);
            return false;
        }
    }

    @Override
    public ImageInfo getImageInfo(String serverId, String imageTag) {
        try {
            DockerServer server = dockerServerService.get(Long.parseLong(serverId));
            DockerClient dockerClient = dockerClientFactory.get(server);

            InspectImageResponse response = dockerClient.inspectImageCmd(imageTag).exec();
            return convertToImageInfo(response);
        } catch (Exception e) {
            log.error("获取镜像信息失败: serverId={}, imageTag={}", serverId, imageTag, e);
            throw new RuntimeException("获取镜像信息失败", e);
        }
    }

    /**
     * 从镜像标签中提取仓库名
     */
    private String extractRepository(String imageTag) {
        int colonIndex = imageTag.lastIndexOf(':');
        if (colonIndex > 0) {
            return imageTag.substring(0, colonIndex);
        }
        return imageTag;
    }

    /**
     * 从镜像标签中提取标签
     */
    private String extractTag(String imageTag) {
        int colonIndex = imageTag.lastIndexOf(':');
        if (colonIndex > 0 && colonIndex < imageTag.length() - 1) {
            return imageTag.substring(colonIndex + 1);
        }
        return "latest";
    }

    /**
     * 转换为ImageInfo
     */
    private ImageInfo convertToImageInfo(Image image) {
        ImageInfo info = new ImageInfo();
        info.setId(image.getId());
        info.setSize(image.getSize());
        info.setRepoTags(List.of(image.getRepoTags()));
        info.setParentId(image.getParentId());
        
        if (image.getCreated() != null) {
            info.setCreated(LocalDateTime.ofInstant(
                Instant.ofEpochSecond(image.getCreated()), 
                ZoneId.systemDefault()));
        }
        
        return info;
    }

    /**
     * 转换为ImageInfo
     */
    private ImageInfo convertToImageInfo(InspectImageResponse response) {
        ImageInfo info = new ImageInfo();
        info.setId(response.getId());
        info.setSize(response.getSize());
        info.setRepoTags(response.getRepoTags() != null ? Arrays.asList(response.getRepoTags()) : List.of());
        info.setParentId(response.getParent());

        if (response.getCreated() != null) {
            info.setCreated(LocalDateTime.parse(response.getCreated().substring(0, 19)));
        }

        return info;
    }

    /**
     * 拉取镜像回调
     */
    private static class PullImageResultCallback extends com.github.dockerjava.api.async.ResultCallback.Adapter<com.github.dockerjava.api.model.PullResponseItem> {
        @Override
        public void onNext(com.github.dockerjava.api.model.PullResponseItem item) {
            // 可以在这里处理拉取进度
            log.debug("Pull progress: {}", item);
        }
    }
}
