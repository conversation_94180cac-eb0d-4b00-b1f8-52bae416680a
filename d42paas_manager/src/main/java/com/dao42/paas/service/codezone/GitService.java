package com.dao42.paas.service.codezone;

import com.dao42.paas.bean.CurrentUserBean;
import com.dao42.paas.external.sdk.dto.git.*;
import com.dao42.paas.model.codezone.CodeZone;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface GitService {

    /**
     * git init
     *
     * @param filePath
     */
    void gitInit(String filePath);

    /**
     * git add
     *
     * @param addFilePath -addFilePath
     * @param rootPath    -
     */
    void add(String addFilePath, String rootPath);

    /**
     * git commit
     *
     * @param message  -message
     * @param rootPath -
     */
    String commit(String message, String rootPath, CurrentUserBean currentUserBean);

    /**
     * git commit
     *
     * @param message  -message
     * @param rootPath -
     */
    String addAndCommit(String addFilePath, String message, String rootPath, CurrentUserBean currentUserBean);

    /**
     * git status
     *
     * @return
     */
    List<GitStatusDTO> status(String rootPath);

    /**
     * git log
     *
     * @param rootPath
     * @return
     */
    List<GitCommitDTO> log(String rootPath, String commitId, Integer pageCount);

    /**
     * 获取分支列表
     *
     * @param rootPath
     */
    List<GitBranchDTO> branch(String rootPath);

    /**
     * 获取当前分支
     *
     * @param rootPath
     * @return
     */
    GitBranchDTO getCurrentBranch(String rootPath);

    /**
     * 获取分支列表
     *
     * @param rootPath
     */
    void createBranch(String rootPath, GitCreateBranchDTO gitBranchDTO);

    /**
     * 发布镜像fork
     *
     * @param rootPath
     * @param commitId
     * @param toPath
     * @param ignore
     */
    void forkSnapshotFile(String rootPath, String commitId, String toPath, String ignore);

    List<GitDiffFileDTO> show(String rootPath, String commitId);

    void reset(String rootPath, String commitId, CurrentUserBean currentUserBean);

    void revert(String rootPath, String commitId, CurrentUserBean currentUserBean);

    GitShowFileDTO showFile(String rootPath, String commitId, String path);

    String getLastCommitId(String rootPath);

    void fetch(String rootPath, GitFetchDTO gitFetchDTO, CurrentUserBean currentUserBean);

    void pull(String rootPath, GitPullDTO gitPullDTO, CurrentUserBean currentUserBean);

    void setRemoteUrl(String rootPath, GitSetRemoteUrlDTO gitSetRemoteUrlDTO, CurrentUserBean currentUserBean);

    void createAndCheckBranch(String rootPath, GitCreateAndCheckBranchDTO gitCreateAndCheckBranchDTO);

    /**
     * 异步创建并切换分支
     *
     * @param gitCreateAndCheckBranchDTO 创建分支参数
     * @return 包含taskId的Map
     */
    Map<String, String> asyncCreateAndCheckBranch(Long id,String rootPath, GitCreateAndCheckBranchDTO gitCreateAndCheckBranchDTO);

    HashMap<String, Object> statusRemoteBranch(String localRepoPath);

    void createDefaultBranch(String rootPath, String branchName);

    void addRemoteUrl(String rootPath, GitSetRemoteUrlDTO gitSetRemoteUrlDTO, CurrentUserBean currentUserBean);
}