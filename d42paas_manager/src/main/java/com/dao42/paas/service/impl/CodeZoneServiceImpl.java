package com.dao42.paas.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.setting.yaml.YamlUtil;
import com.dao42.paas.bean.CurrentUserBean;
import com.dao42.paas.bean.FileDownloadBean;
import com.dao42.paas.common.ResourcesLimit;
import com.dao42.paas.common.constants.MsgType;
import com.dao42.paas.common.constants.RedisPrefix;
import com.dao42.paas.common.enums.PlaygroundStatus;
import com.dao42.paas.common.message.DockerEnvMQMsg;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.constants.Constant;
import com.dao42.paas.constants.Constant.CHECKOUT_TASK_RESULT;
import com.dao42.paas.enums.DockerStatus;
import com.dao42.paas.enums.PlaygroundBindType;
import com.dao42.paas.enums.SnapshotPublicationEnum;
import com.dao42.paas.external.sdk.dto.ForkCodeZoneDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneCreateDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneFileEditDto;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneGithubImportByUrlDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneGithubImportDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneImportDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneNfsNodeDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZonePreCreateResponse;
import com.dao42.paas.external.sdk.dto.codezone.CodeZonePushDTO;
import com.dao42.paas.external.sdk.dto.git.GitSetRemoteUrlDTO;
import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.model.Environment;
import com.dao42.paas.model.EnvironmentVer;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.codezone.CodeZone;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.docker.DockerImages;
import com.dao42.paas.model.middleware.MiddlewareConfig;
import com.dao42.paas.model.tenant.Tenant;
import com.dao42.paas.model.unittest.UnitTestFramework;
import com.dao42.paas.rabbit.MQMessageSender;
import com.dao42.paas.redis.RedisRedissonUtil;
import com.dao42.paas.repository.EnvironmentVerRepository;
import com.dao42.paas.repository.PlaygroundRepository;
import com.dao42.paas.repository.TenantRepository;
import com.dao42.paas.repository.codezone.CodeZoneRepository;
import com.dao42.paas.repository.docker.DockerImagesRepository;
import com.dao42.paas.repository.docker.DockerRepository;
import com.dao42.paas.repository.unitTest.UnitTestFrameworkRepository;
import com.dao42.paas.service.storage.BtrfsService;
import com.dao42.paas.service.codezone.CodeZoneForkService;
import com.dao42.paas.service.codezone.CodeZoneService;
import com.dao42.paas.service.codezone.GitService;
import com.dao42.paas.service.file.FileService;
import com.dao42.paas.service.file.FileUtilService;
import com.dao42.paas.service.middleware.MiddlewareFactory;
import com.dao42.paas.service.permission.PermissionService;
import com.dao42.paas.service.storage.StorageProvider;
import com.dao42.paas.service.storage.StorageService;
import com.dao42.paas.utils.DirUtil;
import com.dao42.paas.utils.FileUnpackUtil;
import com.dao42.paas.utils.FileUtil;
import com.dao42.paas.utils.DateTimeUtil;
import com.dao42.paas.utils.RateLimiterUtil;
import com.google.common.util.concurrent.RateLimiter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jgit.api.CloneCommand;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.PushCommand;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.lib.Ref;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.revwalk.RevWalk;
import org.eclipse.jgit.transport.CredentialsProvider;
import org.eclipse.jgit.transport.PushResult;
import org.eclipse.jgit.transport.RemoteRefUpdate;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.redisson.api.RRateLimiter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RequiredArgsConstructor
@Service
public class CodeZoneServiceImpl implements CodeZoneService {

    private final SystemProperties systemProperties;

    private final FileUtilService fileUtilService;

    private final CodeZoneRepository codeZoneRepository;

    private final UnitTestFrameworkRepository unitTestFrameworkRepository;

    private final EnvironmentVerRepository environmentVerRepository;

    private final PlaygroundRepository playgroundRepository;

    private final DockerRepository dockerRepository;

    private final GitService gitService;

    private final FileService fileService;

    private final TenantRepository tenantRepository;

    private final BtrfsService shellService;

    private final CodeZoneForkService codeZoneForkService;

    private final MiddlewareFactory middlewareFactory;

    private final PermissionService permissionService;

    private final BtrfsService btrfsService;

    private final MQMessageSender mqMessageSender;

    private final RedisRedissonUtil redisRedissonUtil;

    private final DockerImagesRepository dockerImagesRepository;

    private final RateLimiterUtil rateLimiterUtil;

    private final StorageProvider storageProvider;

    private CodeZone createCodeZone(CurrentUserBean currentUserBean, String environmentVerId,
        String unitTestFrameworkId, String purpose) {
        // 构建codeZone的文件夹
        CodeZoneNfsNodeDTO codeZoneNfsNodeDTO = buildCodeZonePath(currentUserBean.getTenantId());
        CodeZone codeZone = new CodeZone();
        codeZone.setTenantId(currentUserBean.getTenantId());
        codeZone.setUserId(currentUserBean.getUserId());
        EnvironmentVer envVer = getEnvVer(environmentVerId);
        codeZone.setEnvironmentVer(envVer);
        codeZone.setUnitTestFramework(getUnitTestFramework(unitTestFrameworkId, codeZone));
        codeZone.setRootPath(codeZoneNfsNodeDTO.getRootPath());
        codeZone.setNfsNodeId(codeZoneNfsNodeDTO.getNfsNodeId());
        codeZone.setPurpose(purpose);
        codeZone.setImage(envVer.getEnvironment().getImage());
        codeZone = this.codeZoneRepository.save(codeZone);
        log.info("upgrade docker container createCodeZone, codeZoneId:{},docker's environment:{} ",codeZone.getId(), environmentVerId);
        return codeZone;
    }

    @Override
    @Transactional
    public CodeZone create(CurrentUserBean currentUserBean, String environmentVerId, String unitTestFrameworkId,
        String purpose) {
        CodeZone codeZone = createCodeZone(currentUserBean, environmentVerId, unitTestFrameworkId, purpose);
        String codeZoneSourcePath = DirUtil.join(codeZone.getRootPath(), Constant.CODE_ZONE_SOURCE_PATH);
        // 创建 .paas-gitignore
        this.createInitFile(codeZoneSourcePath, codeZone.getEnvironmentVer(), currentUserBean.getTenantId());
        // 创建git仓库
        this.gitService.gitInit(codeZoneSourcePath);
        return codeZone;
    }

    @Override
    @Transactional
    public CodeZonePreCreateResponse preCreate(CodeZone codeZone, Playground playground) {
        try {
            // Initialize Git repository and create main branch
//            gitService.createDefaultBranch(codeZone.getRootPath(), "main");

            // Return response with IDs
            return new CodeZonePreCreateResponse(codeZone.getId().toString(), playground.getId().toString());
        } catch (Exception e) {
            log.warn("preCreate error ,system msg  ", e);
            throw e;
        }
    }

    @Deprecated
    @Override
    @Transactional
    public String createFromGithub(CurrentUserBean currentUserBean, CodeZoneGithubImportDTO codeZoneGithubImportDTO) {
        try {
            log.info("systemProperties.getRedisRateLimter().getCloneRate():{}",
                systemProperties.getRedisRateLimter().getCloneRate());
            RRateLimiter rateLimiter = redisRedissonUtil.getRateLimiter(
                systemProperties.getRedisRateLimter().getCloneRate(), 10L, RedisPrefix.REDISSON_REDIS_CLONE);
            if (!rateLimiter.tryAcquire(1000, TimeUnit.MILLISECONDS)) {
                throw new CustomRuntimeException("No clone token was obtained", "No clone token was obtained");
            }
            CodeZone codeZone = createCodeZone(currentUserBean, codeZoneGithubImportDTO.getEnvironmentVerId(),
                codeZoneGithubImportDTO.getUnitTestFrameworkId(), codeZoneGithubImportDTO.getPurpose());

            // create SSH file
            String privateKey = codeZoneGithubImportDTO.getPrivateKey().replace("\\n", "\n");
            createSSHFile(codeZone.getRootPath(), privateKey);

            String url =
                StringUtils.isBlank(codeZoneGithubImportDTO.getToken()) ? String.format("https://github.com/%s/%s.git",
                    codeZoneGithubImportDTO.getOwner(), codeZoneGithubImportDTO.getRepo())
                    : String.format(
                        "https://x-access-token:" + codeZoneGithubImportDTO.getToken() + "@github.com/%s/%s.git",
                        codeZoneGithubImportDTO.getOwner(), codeZoneGithubImportDTO.getRepo());
            String codeZoneSourcePath = DirUtil.join(codeZone.getRootPath(), Constant.CODE_ZONE_SOURCE_PATH);
            CloneCommand cloneCommand = Git.cloneRepository().setURI(url).setDirectory(new File(codeZoneSourcePath))
                .setTimeout(30);
            if (StringUtils.isNotBlank(codeZoneGithubImportDTO.getToken()) && StringUtils.isNotBlank(
                codeZoneGithubImportDTO.getUsername())) {
                String username = codeZoneGithubImportDTO.getUsername();
                String token = codeZoneGithubImportDTO.getToken();
                CredentialsProvider credentialsProvider = new UsernamePasswordCredentialsProvider(username, token);
                cloneCommand.setCredentialsProvider(credentialsProvider);
            }
            if (StringUtils.isNotBlank(codeZoneGithubImportDTO.getRef())) {
                cloneCommand.setBranch(codeZoneGithubImportDTO.getRef());
            }
            cloneCommand.call();

            // create init file
            this.createInitFile(codeZoneSourcePath, codeZone.getEnvironmentVer(), currentUserBean.getTenantId());

            // update git remote url
            GitSetRemoteUrlDTO gitSetRemoteUrlDTO = new GitSetRemoteUrlDTO();
            gitSetRemoteUrlDTO.setRemoteName("origin");
            gitSetRemoteUrlDTO.setRemoteUrl(
                String.format("**************:%s/%s.git", codeZoneGithubImportDTO.getOwner(),
                    codeZoneGithubImportDTO.getRepo()));
            gitService.setRemoteUrl(codeZone.getRootPath(), gitSetRemoteUrlDTO, currentUserBean);

            return codeZone.getId().toString();
        } catch (CustomRuntimeException e) {
            log.warn("import failure ,customize warn  ", e);
            throw e;
        } catch (Exception e) {
            log.warn("import failure ,system msg  ", e);
            throw new CustomRuntimeException("import failure");
        }
    }

    @Override
    @Transactional
    public Map<String, String> createFromGithubAsync(CurrentUserBean currentUserBean,
        CodeZoneGithubImportDTO codeZoneGithubImportDTO) {
        Map<String, String> result = new HashMap<>();
        String taskId = UUID.randomUUID().toString();
        result.put("taskId", taskId);
        log.info("createFromGithubAsync, taskId: {}", taskId);

        // create codeZone
        CodeZone codeZone = createCodeZone(currentUserBean, codeZoneGithubImportDTO.getEnvironmentVerId(),
            codeZoneGithubImportDTO.getUnitTestFrameworkId(), codeZoneGithubImportDTO.getPurpose());
        result.put("codeZoneId", codeZone.getId().toString());
        log.info("createFromGithubAsync, codeZoneId: {}", codeZone.getId());

        // create SSH file
        long startTime = System.currentTimeMillis();
        String privateKey = codeZoneGithubImportDTO.getPrivateKey().replace("\\n", "\n");
        createSSHFile(codeZone.getRootPath(), privateKey);
        log.info("createFromGithubAsync, create SSH file, taskId: {}, codeZoneId: {}, timeCost: {}", taskId,
            codeZone.getId(), System.currentTimeMillis() - startTime);

        // Initialize clone status
        String progressKey = RedisPrefix.GITHUB_IMPORT_PROGRESS + taskId;
        String logKey = RedisPrefix.GITHUB_IMPORT_LOG + taskId;
        redisRedissonUtil.set(progressKey, RedisPrefix.CLONE_STATUS_PENDING, 24 * 30, TimeUnit.HOURS);
        redisRedissonUtil.set(logKey, "", 24 * 30, TimeUnit.HOURS);

        // Execute import asynchronously
        CompletableFuture.runAsync(() -> {
            try {
                log.info("systemProperties.getRedisRateLimter().getCloneRate():{}",
                    systemProperties.getRedisRateLimter().getCloneRate());
                RRateLimiter rateLimiter = redisRedissonUtil.getRateLimiter(
                    systemProperties.getRedisRateLimter().getCloneRate().intValue(), 10L,
                    RedisPrefix.REDISSON_REDIS_CLONE);

                if (!rateLimiter.tryAcquire(1000, TimeUnit.MILLISECONDS)) {
                    throw new CustomRuntimeException("No clone token was obtained");
                }

                String url = StringUtils.isBlank(codeZoneGithubImportDTO.getToken())
                    ? String.format("https://github.com/%s/%s.git", codeZoneGithubImportDTO.getOwner(),
                    codeZoneGithubImportDTO.getRepo())
                    : String.format("https://x-access-token:" + codeZoneGithubImportDTO.getToken()
                            + "@github.com/%s/%s.git",
                        codeZoneGithubImportDTO.getOwner(), codeZoneGithubImportDTO.getRepo());

                // 代码挪到dependency/home/<USER>
                String codeZoneSourcePath = DirUtil.join(codeZone.getRootPath(), Constant.CODE_ZONE_SOURCE_PATH);

                CloneCommand cloneCommand = Git.cloneRepository()
                    .setURI(url)
                    .setDirectory(new File(codeZoneSourcePath));

                // Add progress monitor that only tracks progress, not final status
                cloneCommand.setProgressMonitor(new GitProgressMonitor(taskId, (id, status, message) -> {
                    // Only update if status is CLONING to avoid interfering with final status
                    if (RedisPrefix.CLONE_STATUS_CLONING.equals(status)) {
                        updateImportProgress(id, status, message);
                    }
                }));

                if (StringUtils.isNotBlank(codeZoneGithubImportDTO.getToken()) &&
                    StringUtils.isNotBlank(codeZoneGithubImportDTO.getUsername())) {
                    String username = codeZoneGithubImportDTO.getUsername();
                    String token = codeZoneGithubImportDTO.getToken();
                    CredentialsProvider credentialsProvider = new UsernamePasswordCredentialsProvider(username, token);
                    cloneCommand.setCredentialsProvider(credentialsProvider);
                }

                if (StringUtils.isNotBlank(codeZoneGithubImportDTO.getRef())) {
                    cloneCommand.setBranch(codeZoneGithubImportDTO.getRef());
                }

                long beginTime = System.currentTimeMillis();
                Git git = cloneCommand.call();
                git.close();
                log.info("createFromGithubAsync, cloneCommand, taskId: {}, codeZoneId: {}, timeCost: {}", taskId,
                    codeZone.getId(), System.currentTimeMillis() - beginTime);

                // create init file
                beginTime = System.currentTimeMillis();
                this.createInitFile(codeZoneSourcePath, codeZone.getEnvironmentVer(), currentUserBean.getTenantId());
                log.info("createFromGithubAsync, createInitFile, taskId: {}, codeZoneId: {}, timeCost: {}", taskId,
                    codeZone.getId(), System.currentTimeMillis() - beginTime);

                // 检查并等待 index.lock 文件释放
                checkAndWaitForIndexLock(codeZoneSourcePath, taskId, codeZone.getId());

                // 更新状态为完成并设置远程仓库URL
                updateCompletionStatus(taskId, codeZone, codeZoneGithubImportDTO, currentUserBean);

            } catch (Exception e) {
                String errorMessage = String.format("Import failed: %s", e.getMessage());
                log.error("Import failure for taskId: {}, codeZoneId: {}", taskId, codeZone.getId(), e);
                updateImportProgress(taskId, RedisPrefix.CLONE_STATUS_FAILED, errorMessage);
                throw new CustomRuntimeException(errorMessage, e);
            }
        });

        return result;
    }

    @Override
    @Transactional
    public Map<String, String> asyncClone(CurrentUserBean currentUserBean,
        CodeZoneGithubImportByUrlDTO codeZoneGithubImportDTO) {
        Map<String, String> result = new HashMap<>();
        String taskId = UUID.randomUUID().toString();
        result.put("taskId", taskId);
        log.info("asyncClone, taskId: {}", taskId);

        // create codeZone
        CodeZone codeZone = createCodeZone(currentUserBean, codeZoneGithubImportDTO.getEnvironmentVerId(),
            codeZoneGithubImportDTO.getUnitTestFrameworkId(), codeZoneGithubImportDTO.getPurpose());
        result.put("codeZoneId", codeZone.getId().toString());
        log.info("asyncClone, codeZoneId: {}", codeZone.getId());

        // create SSH file
        long startTime = System.currentTimeMillis();
        String privateKey = codeZoneGithubImportDTO.getPrivateKey().replace("\\n", "\n");
        createSSHFile(codeZone.getRootPath(), privateKey, codeZoneGithubImportDTO.getSshUrl());
        log.info("asyncClone, create SSH file, taskId: {}, codeZoneId: {}, timeCost: {}", taskId,
            codeZone.getId(), System.currentTimeMillis() - startTime);

        // Initialize clone status
        String progressKey = RedisPrefix.GITHUB_IMPORT_PROGRESS + taskId;
        String logKey = RedisPrefix.GITHUB_IMPORT_LOG + taskId;
        redisRedissonUtil.set(progressKey, RedisPrefix.CLONE_STATUS_PENDING, 24 * 30, TimeUnit.HOURS);
        redisRedissonUtil.set(logKey, "", 24 * 30, TimeUnit.HOURS);

        // Execute import asynchronously
        CompletableFuture.runAsync(() -> {
            try {
                log.info("systemProperties.getRedisRateLimter().getCloneRate():{}",
                    systemProperties.getRedisRateLimter().getCloneRate());
                RRateLimiter rateLimiter = redisRedissonUtil.getRateLimiter(
                    systemProperties.getRedisRateLimter().getCloneRate().intValue(), 10L,
                    RedisPrefix.REDISSON_REDIS_CLONE);

                if (!rateLimiter.tryAcquire(1000, TimeUnit.MILLISECONDS)) {
                    throw new CustomRuntimeException("No clone token was obtained");
                }

                // 代码挪到dependency/home/<USER>
                String codeZoneSourcePath = DirUtil.join(codeZone.getRootPath(), Constant.CODE_ZONE_SOURCE_PATH);

                CloneCommand cloneCommand = Git.cloneRepository()
                    .setURI(codeZoneGithubImportDTO.getHttpUrl())
                    .setDirectory(new File(codeZoneSourcePath));

                // Add progress monitor that only tracks progress, not final status
                cloneCommand.setProgressMonitor(new GitProgressMonitor(taskId, (id, status, message) -> {
                    // Only update if status is CLONING to avoid interfering with final status
                    if (RedisPrefix.CLONE_STATUS_CLONING.equals(status)) {
                        updateImportProgress(id, status, message);
                    }
                }));

                // url 中自带 token，不需要 setCredentialsProvider
                // if (StringUtils.isNotBlank(codeZoneGithubImportDTO.getToken()) &&
                //     StringUtils.isNotBlank(codeZoneGithubImportDTO.getUsername())) {
                //     String username = codeZoneGithubImportDTO.getUsername();
                //     String token = codeZoneGithubImportDTO.getToken();
                //     CredentialsProvider credentialsProvider = new UsernamePasswordCredentialsProvider(username, token);
                //     cloneCommand.setCredentialsProvider(credentialsProvider);
                // }

                if (StringUtils.isNotBlank(codeZoneGithubImportDTO.getRef())) {
                    cloneCommand.setBranch(codeZoneGithubImportDTO.getRef());
                }

                long beginTime = System.currentTimeMillis();
                Git git = cloneCommand.call();
                git.close();
                log.info("asyncClone, cloneCommand, taskId: {}, codeZoneId: {}, timeCost: {}", taskId,
                    codeZone.getId(), System.currentTimeMillis() - beginTime);

                // create init file
                beginTime = System.currentTimeMillis();
                this.createInitFile(codeZoneSourcePath, codeZone.getEnvironmentVer(), currentUserBean.getTenantId());
                log.info("asyncClone, createInitFile, taskId: {}, codeZoneId: {}, timeCost: {}", taskId,
                    codeZone.getId(), System.currentTimeMillis() - beginTime);

                // 检查并等待 index.lock 文件释放
                checkAndWaitForIndexLock(codeZoneSourcePath, taskId, codeZone.getId());

                // 更新状态为完成并设置远程仓库URL
                updateCloneStatus(taskId, codeZone, codeZoneGithubImportDTO, currentUserBean);

            } catch (Exception e) {
                String errorMessage = String.format("Import failed: %s", e.getMessage());
                log.error("asyncClone failure for taskId: {}, codeZoneId: {}", taskId, codeZone.getId(), e);
                updateImportProgress(taskId, RedisPrefix.CLONE_STATUS_FAILED, errorMessage);
                throw new CustomRuntimeException(errorMessage, e);
            }
        });

        return result;
    }

    /**
     * 检查并等待 index.lock 文件释放
     *
     * @param codeZoneSourcePath 代码路径
     * @param taskId             任务ID
     * @param codeZoneId         代码区域ID
     * @throws InterruptedException   如果等待被中断
     * @throws CustomRuntimeException 如果index.lock文件在重试后仍然存在
     */
    private void checkAndWaitForIndexLock(String codeZoneSourcePath, String taskId, Long codeZoneId)
        throws InterruptedException {
        String indexLockPath = codeZoneSourcePath + "/.git/index.lock";
        log.info("clone checkAndWaitForIndexLock indexLockPath: {}, taskId: {}, codeZoneId: {}", indexLockPath, taskId,
            codeZoneId);

        int maxRetries = 10;
        int retryIntervalMs = 1000;

        for (int i = 0; i < maxRetries; i++) {
            File indexLockFile = new File(indexLockPath);
            if (!indexLockFile.exists()) {
                log.info("clone index.lock file not exists, taskId: {}, codeZoneId: {}", taskId, codeZoneId);
                return;
            }

            log.warn("clone index.lock file exists, attempt {}/{}, taskId: {}, codeZoneId: {}",
                (i + 1), maxRetries, taskId, codeZoneId);
            Thread.sleep(retryIntervalMs);
        }

        log.error("clone index.lock file still exists after {} retries, taskId: {}, codeZoneId: {}",
            maxRetries, taskId, codeZoneId);
        throw new CustomRuntimeException("clone index.lock file still exists after " + maxRetries + " retries");
    }

    /**
     * 更新完成状态并设置远程仓库URL
     */
    private void updateCompletionStatus(String taskId, CodeZone codeZone,
        CodeZoneGithubImportDTO codeZoneGithubImportDTO,
        CurrentUserBean currentUserBean) {
        // 更新克隆完成状态
        String timestamp = getCurrentTimestamp();
        updateImportProgress(taskId, RedisPrefix.CLONE_STATUS_COMPLETED,
            timestamp + " Clone completed successfully!");

        // 设置Git远程仓库URL
        GitSetRemoteUrlDTO gitSetRemoteUrlDTO = new GitSetRemoteUrlDTO();
        gitSetRemoteUrlDTO.setRemoteName("origin");
        gitSetRemoteUrlDTO.setRemoteUrl(
            String.format("**************:%s/%s.git",
                codeZoneGithubImportDTO.getOwner(),
                codeZoneGithubImportDTO.getRepo()));
        gitService.setRemoteUrl(codeZone.getRootPath(), gitSetRemoteUrlDTO, currentUserBean);

        // 更新创建分支状态
        timestamp = getCurrentTimestamp();
        updateImportProgress(taskId, RedisPrefix.CLONE_STATUS_COMPLETED,
            timestamp + " Creating new branch...");
    }

    /**
     * 更新完成状态并设置远程仓库URL
     */
    private void updateCloneStatus(String taskId, CodeZone codeZone,
        CodeZoneGithubImportByUrlDTO codeZoneGithubImportDTO,
        CurrentUserBean currentUserBean) {
        // 更新克隆完成状态
        String timestamp = getCurrentTimestamp();
        updateImportProgress(taskId, RedisPrefix.CLONE_STATUS_COMPLETED,
            timestamp + " Clone completed successfully!");

        // 设置Git远程仓库URL
        GitSetRemoteUrlDTO gitSetRemoteUrlDTO = new GitSetRemoteUrlDTO();
        gitSetRemoteUrlDTO.setRemoteName("origin");
        gitSetRemoteUrlDTO.setRemoteUrl(codeZoneGithubImportDTO.getSshUrl());
        gitService.setRemoteUrl(codeZone.getRootPath(), gitSetRemoteUrlDTO, currentUserBean);

        // 更新创建分支状态
        timestamp = getCurrentTimestamp();
        updateImportProgress(taskId, RedisPrefix.CLONE_STATUS_COMPLETED,
            timestamp + " Creating new branch...");
    }

    /**
     * 获取当前时间戳字符串
     */
    private String getCurrentTimestamp() {
        return java.time.LocalDateTime.now()
            .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private void updateImportProgress(String taskId, String progress, String message) {
        String progressKey = RedisPrefix.GITHUB_IMPORT_PROGRESS + taskId;
        String logKey = RedisPrefix.GITHUB_IMPORT_LOG + taskId;

        redisRedissonUtil.set(progressKey, progress, 24 * 30, TimeUnit.HOURS);

        // Append new log message with timestamp
        String currentLog = redisRedissonUtil.get(logKey);
        redisRedissonUtil.set(logKey, currentLog + message + "\n", 24 * 30, TimeUnit.HOURS);
    }

    private void createInitFile(String codeZoneSourcePath, EnvironmentVer environmentVer, Long tenantId) {
        Tenant tenant = this.tenantRepository.findById(tenantId).get();
        String envPath = systemProperties.getCodeZone().getEnvPath() + "/";
        if (!FileUtil.exist(envPath)) {
            log.debug("envPath not exists:{}", envPath);
            throw new CustomRuntimeException("envPath not exists");
        }
        // create .gitignore
        String gitignoreFile = codeZoneSourcePath + "/.gitignore";
        if (!FileUtil.exist(gitignoreFile)) {
            FileUtil.touch(gitignoreFile);
        }

        // create .1024
        String configFile = codeZoneSourcePath + "/../.clackyai/.environments.yaml";
        if (!FileUtil.exist(configFile)) {
            cn.hutool.core.io.FileUtil.copyFile(envPath + environmentVer.getEnvVerKey() + "/.1024", configFile);
        }
    }

    @Override
    @Transactional
    public void upgradeSSHFile(CurrentUserBean currentUserBean, CodeZone codeZone,
        GitSetRemoteUrlDTO gitSetRemoteUrlDTO) {
        // create SSH file
        String privateKey = gitSetRemoteUrlDTO.getPrivateKey().replace("\\n", "\n");
        createSSHFile(codeZone.getRootPath(), privateKey, gitSetRemoteUrlDTO.getRemoteUrl());
    }

    private void createSSHFile(String rootPath, String privateKey) {
        // create id_rsa file
        log.info("private_key:{}", privateKey);
        String idRsaPath = rootPath + "dependency" + "/home/<USER>/id_rsa";
        cn.hutool.core.io.FileUtil.writeString(privateKey, idRsaPath, StandardCharsets.UTF_8);

        // set id_rsa permission
        ProcessBuilder chmodIdRsaBuilder = new ProcessBuilder("chmod", "600", idRsaPath);
        try {
            chmodIdRsaBuilder.start();
        } catch (Exception e) {
            log.warn("chmodIdRsaBuilder failed, path:{},privilege :600", idRsaPath);
            throw new CustomRuntimeException("chmodIdRsaBuilder failed, path:{}", idRsaPath);
        }

        // add known_hosts
        String knownHostsPath = rootPath + "dependency" + "/home/<USER>/known_hosts";
        ProcessBuilder keyscanBuilder = new ProcessBuilder("/bin/sh", "-c",
            "ssh-keyscan -t ed25519 github.com > " + knownHostsPath);
        try {
            keyscanBuilder.start();
        } catch (Exception e) {
            log.warn("keyscanBuilder failed, path:{}", knownHostsPath);
            throw new CustomRuntimeException("keyscanBuilder failed, path:{}", knownHostsPath);
        }

        // add authorized_keys
        String authorizedKeys = "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIImjxHbz3NnaeejU5lxpbUc6U9u+ejAuCEyLO2n9IOYh clacky";
        String authorizedKeysPath = rootPath + "dependency" + "/home/<USER>/authorized_keys";
        cn.hutool.core.io.FileUtil.writeString(authorizedKeys, authorizedKeysPath, StandardCharsets.UTF_8);

        // set authorized_keys permission
        ProcessBuilder chmodAuthorizedKeysBuilder = new ProcessBuilder("chmod", "600", authorizedKeysPath);
        try {
            chmodAuthorizedKeysBuilder.start();
        } catch (Exception e) {
            log.warn("chmodAuthorizedKeysBuilder failed, path:{},privilege :600", authorizedKeysPath);
            throw new CustomRuntimeException("chmodAuthorizedKeysBuilder failed, path:{}", authorizedKeysPath);
        }
    }

    private void createSSHFile(String rootPath, String privateKey, String sshUrl) {
        // create id_rsa file
        log.info("private_key:{}", privateKey);
        String idRsaPath = rootPath + "dependency" + "/home/<USER>/id_rsa";
        cn.hutool.core.io.FileUtil.writeString(privateKey, idRsaPath, StandardCharsets.UTF_8);

        // set id_rsa permission
        ProcessBuilder chmodIdRsaBuilder = new ProcessBuilder("chmod", "600", idRsaPath);
        try {
            chmodIdRsaBuilder.start();
        } catch (Exception e) {
            log.warn("chmodIdRsaBuilder failed, path:{},privilege :600", idRsaPath);
            throw new CustomRuntimeException("chmodIdRsaBuilder failed, path:{}", idRsaPath);
        }

        // add known_hosts
        String knownHostsPath = rootPath + "dependency" + "/home/<USER>/known_hosts";
        String[] hostInfo = extractHostAndPortFromSshUrl(sshUrl);
        String host = hostInfo[0];
        String port = hostInfo[1];

        // Build ssh-keyscan command based on whether port is specified
        String keyscanCommand;
        if (StringUtils.isNotBlank(port)) {
            keyscanCommand = String.format("ssh-keyscan -t rsa,ed25519,ecdsa -p %s %s > %s", port, host,
                knownHostsPath);
        } else {
            keyscanCommand = String.format("ssh-keyscan -t rsa,ed25519,ecdsa %s > %s", host, knownHostsPath);
        }

        log.info("keyscanCommand:{}", keyscanCommand);
        ProcessBuilder keyscanBuilder = new ProcessBuilder("/bin/sh", "-c", keyscanCommand);
        try {
            keyscanBuilder.start();
        } catch (Exception e) {
            log.warn("keyscanBuilder failed, path:{}", knownHostsPath);
            throw new CustomRuntimeException("keyscanBuilder failed, path:{}", knownHostsPath);
        }

        // add authorized_keys
        String authorizedKeys = "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIImjxHbz3NnaeejU5lxpbUc6U9u+ejAuCEyLO2n9IOYh clacky";
        String authorizedKeysPath = rootPath + "dependency" + "/home/<USER>/authorized_keys";
        cn.hutool.core.io.FileUtil.writeString(authorizedKeys, authorizedKeysPath, StandardCharsets.UTF_8);

        // set authorized_keys permission
        ProcessBuilder chmodAuthorizedKeysBuilder = new ProcessBuilder("chmod", "600", authorizedKeysPath);
        try {
            chmodAuthorizedKeysBuilder.start();
        } catch (Exception e) {
            log.warn("chmodAuthorizedKeysBuilder failed, path:{},privilege :600", authorizedKeysPath);
            throw new CustomRuntimeException("chmodAuthorizedKeysBuilder failed, path:{}", authorizedKeysPath);
        }
    }

    /**
     * Extract host and port from SSH URL
     *
     * @param sshUrl SSH URL
     * @return String array where [0] is host and [1] is port (may be empty)
     */
    private String[] extractHostAndPortFromSshUrl(String sshUrl) {
        if (StringUtils.isBlank(sshUrl)) {
            return new String[]{"github.com", ""};
        }

        try {
            String hostWithPort;

            // Handle git@host:port/user/repo.git format
            if (sshUrl.startsWith("git@")) {
                hostWithPort = sshUrl.substring(4, sshUrl.indexOf("/"));
                if (hostWithPort.contains(":")) {
                    hostWithPort = hostWithPort.substring(0, hostWithPort.indexOf(":"));
                }
                return new String[]{hostWithPort, ""};
            }

            // Handle ssh://[git@]host[:port]/user/repo.git format
            if (sshUrl.startsWith("ssh://")) {
                String withoutProtocol = sshUrl.substring(6);
                // Remove username if present
                if (withoutProtocol.contains("@")) {
                    withoutProtocol = withoutProtocol.substring(withoutProtocol.indexOf("@") + 1);
                }
                // Extract host and port
                hostWithPort = withoutProtocol.substring(0, withoutProtocol.indexOf("/"));
                if (hostWithPort.contains(":")) {
                    String[] parts = hostWithPort.split(":");
                    return new String[]{parts[0], parts[1]};
                }
                return new String[]{hostWithPort, ""};
            }
        } catch (Exception e) {
            log.warn("Failed to extract host and port from SSH URL: {}", sshUrl, e);
        }

        // Default fallback
        return new String[]{"github.com", ""};
    }

//    private void coverInitFile(String rootPath, EnvironmentVer environmentVer) {
//        // cover nix file
//        String envPath= systemProperties.getCodeZone().getEnvPath()+"/";
//        if ( !FileUtil.exist(envPath)){
//            log.debug("envPath not exists:{}",envPath);
//            throw new CustomRuntimeException("envPath not exists");
//        }
//        String targetNixFile=rootPath + Constant.CODE_ZONE_SOURCE_PATH + File.separator + Constant.SHELL_NIX_RENAME;
//        cn.hutool.core.io.FileUtil.copy(envPath + environmentVer.getEnvVerKey() + "/shell.nix",
//            targetNixFile,true);
//    }

    /**
     * 返回NFS共享盘上CodeZone所在的绝对路径
     *
     * @param tenantId 租户id
     * @return
     */
    private CodeZoneNfsNodeDTO buildCodeZonePath(Long tenantId) {


        String folderName = getParentFolderName(tenantId);
//        String folderPath = systemProperties.getCodeZone().getPath() + folderName;

        StorageService storageService = storageProvider.getAvailableBtrfsService();
        String folderPath = storageService.getPath() +Constant.CODE_ZONE_PATH + folderName;

        String folderPathOnBtrfs = systemProperties.getCodeZone().getPathInBtrfs() + folderName;
        // 在NFS上创建codeZone的父级目录
        File file = new File(folderPath);
        file.mkdirs();
        if (!file.exists()) {
            log.error("Mkdir fail:, {}", folderPath);
            throw new CustomRuntimeException("error.mkdir", "Failed to create path");
        }

        // codeZone根目录的名称
        String codeZonePathName = "@" + UUID.randomUUID();
        // 复制原始codeZone到新建的codeZone
        String cmd = systemProperties.getBtrfs().getCreateCmd()
            .replace("{source}", systemProperties.getCodeZone().getMeta())
            .replace("{target}", DirUtil.join(folderPathOnBtrfs, codeZonePathName));

        Long nfsNodeId = storageService.getNfsNodeId();
        boolean success = storageService.execCommandBtrfs(cmd);
        if (!success) {
            throw new CustomRuntimeException("Failed to copy subvolume");
        }

        return new CodeZoneNfsNodeDTO().setRootPath(folderPath + codeZonePathName + "/").setNfsNodeId(storageService.getNfsNodeId());
    }

    private UnitTestFramework getUnitTestFramework(String unitTestFrameworkId, CodeZone codeZone) {
        // 获取单元测试框架 非必填 不填为默认测试框架
        if (StringUtils.isNotBlank(unitTestFrameworkId)) {
            Optional<UnitTestFramework> unitTestFrameworkOptional = this.unitTestFrameworkRepository.findById(
                Long.valueOf(unitTestFrameworkId));
            if (!unitTestFrameworkOptional.isPresent()) {
                throw new CustomRuntimeException(String.format("UnitTestFramework(ID:%s)不存在", unitTestFrameworkId));
            }
            return unitTestFrameworkOptional.get();
        } else {
            Environment environment = codeZone.getEnvironmentVer().getEnvironment();
            List<UnitTestFramework> unitTestFrameworks = this.unitTestFrameworkRepository
                .findAllByDefaultFrameworkTrueAndEnvironmentId(environment.getId());
            if (!unitTestFrameworks.isEmpty()) {
                return unitTestFrameworks.get(0);
            }
            return null;
        }
    }

    @Override
    public CodeZone update(CodeZone codeZone, String environmentVerId, String unitTestFrameworkId, Integer cpu,
        Integer memory, String purpose) {

        if (purpose != null && !purpose.isBlank()) {
            codeZone.setPurpose(purpose);
        }
        if (environmentVerId != null && !environmentVerId.isBlank()) {
            codeZone.setEnvironmentVer(getEnvVer(environmentVerId));
            log.info("upgrade docker container update, codeZoneId:{},docker's environment:{} ",codeZone.getId(), environmentVerId);
        }
        if (unitTestFrameworkId != null && !unitTestFrameworkId.isBlank()) {
            codeZone.setUnitTestFramework(getUnitTestFramework(unitTestFrameworkId, codeZone));
        }
        if (cpu != null && memory != null && cpu > 0 && memory > 0) {
            ResourcesLimit lt = new ResourcesLimit(cpu, memory, 0);
            codeZone.setResourcesLimit(lt);
        }
        return this.codeZoneRepository.save(codeZone);
    }

    @Override
    public CodeZone upgrade(CodeZone codeZone, CodeZoneCreateDTO codeZoneDTO) {
        Playground playground = playgroundRepository.findByBindTypeAndBindObject(
            PlaygroundBindType.CODE_ZONE, codeZone.getId());

        String environmentVerId = codeZoneDTO.getEnvironmentVerId() == null ? "" : codeZoneDTO.getEnvironmentVerId();
        String environmentVerCode =
            codeZoneDTO.getEnvironmentVerCode() == null ? "" : codeZoneDTO.getEnvironmentVerCode();

        //先更新文件
        EnvironmentVer envVer = null;
        if (!Objects.equals(environmentVerId, "")) {
            envVer = getEnvVer(environmentVerId);
        } else if (!Objects.equals(environmentVerCode, "")) {
            envVer = getEnvVerByEnvCode(environmentVerCode);
        }

        if (envVer == null) {
            throw new CustomRuntimeException("can't upgrade", "cant upgrade, env is empty");
        }

        EnvironmentVer blankEnvironmentVer = environmentVerRepository.findByName("blank");

        if (playground != null && playground.getDockerContainer() != null &&
            playground.getDockerContainer().getStatus() != DockerStatus.NOT_INIT &&
            !Objects.equals(playground.getEnvironmentVer().getId(), blankEnvironmentVer.getId()) &&
            !Objects.equals(playground.getEnvironmentVer().getId(), envVer.getId())
        ) {
            throw new CustomRuntimeException("can't upgrade",
                "You have chosen a different environment, and the original environment ("
                    + playground.getEnvironmentVer().getName() + ") has already been activated");
        }

//        coverInitFile(codeZone.getRootPath(), envVer);
        //再更新数据库配置
        codeZone.setEnvironmentVer(envVer);
        codeZone.setUnitTestFramework(getUnitTestFramework(codeZoneDTO.getUnitTestFrameworkId(), codeZone));
        codeZone.setImage(envVer.getEnvironment().getImage());

        if (playground != null && playground.getDockerContainer() != null) {
            DockerContainer docker =  playground.getDockerContainer();
            docker.setImage(envVer.getEnvironment().getImage());
            docker.setEnvironmentVer(envVer);
            docker.setCreatedBy("1");
            DockerContainer dk = dockerRepository.save(docker);
            log.info("upgrade docker container finished, playgroundId:{}, codeZoneId: {}, dockerId: {}, docker's environment:{}, dkEnvId: {}",
            playground.getId(), codeZone.getId(), docker.getId(), docker.getEnvironmentVer().getId(), dk.getEnvironmentVer().getId());
        }else {
            log.warn("upgrade docker container is canceled, codeZone id: {} ", codeZone.getId());
        }
        return this.codeZoneRepository.save(codeZone);
    }

    @Override
    public CodeZone fork(CodeZone codeZone, String userId, ForkCodeZoneDTO dto) {

        StorageService storageService = storageProvider.getStorageService(codeZone.getNfsNodeId());

        // 复制到新子卷
        String folderName = getParentFolderName(codeZone.getTenantId());
//        String folderPath = systemProperties.getCodeZone().getPath() + folderName;

        String folderPath = storageService.getPath()+Constant.CODE_ZONE_PATH + folderName;

        String folderPathOnBtrfs = systemProperties.getCodeZone().getPathInBtrfs() + folderName;
        String nfsCodeZonePath = codeZoneForkService.fork(codeZone.getRootPath(), dto.getCommitId(), folderPath,
            folderPathOnBtrfs, SnapshotPublicationEnum.CODE_ZONE, codeZone);

        // 构建新CodeZone
        CodeZone newCodeZone = new CodeZone();
        newCodeZone.setUserId(userId);
        newCodeZone.setTenantId(codeZone.getTenantId());
        EnvironmentVer environmentVer = codeZone.getEnvironmentVer();
        newCodeZone.setEnvironmentVer(environmentVer);
        newCodeZone.setNfsNodeId(storageService.getNfsNodeId());
        log.info("upgrade docker container update, codeZoneId:{},docker's environment:{} ",codeZone.getId(), environmentVer.getId());
        if (StringUtils.isNotBlank(codeZone.getImage())) {
            newCodeZone.setImage(codeZone.getImage());
        } else {
            newCodeZone.setImage(environmentVer.getEnvironment().getImage());
        }
        newCodeZone.setUnitTestFramework(codeZone.getUnitTestFramework());
        newCodeZone.setRootPath(nfsCodeZonePath);
        newCodeZone.setPurpose(dto.getPurpose());
        for (MiddlewareConfig config : codeZone.getMiddlewareConfigList()) {
            MiddlewareConfig newConfig;
            if (dto.isWithMiddlewareData()) {
                newConfig = middlewareFactory.forkConfig(newCodeZone.getRootPath(), config);
                newConfig.setExternalSwitch(config.getExternalSwitch());
            } else {
                newConfig = middlewareFactory.newConfig(config.getDefine());
            }
            newCodeZone.getMiddlewareConfigList().add(newConfig);
        }

        // 如果不需要复制数据，则删除数据内容
        if (!dto.isWithMiddlewareData()) {
            for (MiddlewareConfig config : newCodeZone.getMiddlewareConfigList()) {
                String path = DirUtil.join(newCodeZone.getRootPath(), Constant.CODE_ZONE_MIDDLEWARE_PATH,
                    config.getDefine().getCode());
                File file = new File(path);
                FileUtils.deleteQuietly(file);
            }
        }

        if (dto.getMemory() > 0) {
            // 兼容ai agent跑ai swe测试用例需要高配置docker(16G)
            ResourcesLimit lt = new ResourcesLimit(dto.getCpu(), dto.getMemory(), 0);
            newCodeZone.setResourcesLimit(lt);
        } else {
            // 复制资源配置
            newCodeZone.setResourcesLimit(codeZone.getResourcesLimit());
        }

        return this.codeZoneRepository.save(newCodeZone);
    }

    @Override
    public DockerContainer getDockerByCodeZone(CodeZone codeZone) {
        Playground playground = playgroundRepository.findByBindTypeAndBindObject(PlaygroundBindType.CODE_ZONE,
            codeZone.getId());
        if (playground == null) {
            return null;
        } else {
            return playground.getDockerContainer();
        }
    }

    @Override
    public void importCodeZoneFile(MultipartFile multipartFile, CodeZone codeZone) {
        // 保存文件
        File file = FileUtil.multipartToFile(multipartFile, systemProperties.getCodeZone().getImportFile());
        if (file == null) {
            throw new CustomRuntimeException("file.is.null", "文件为空");
        }
        String path = codeZone.getRootPath() + File.separator + Constant.CODE_ZONE_SOURCE_PATH + File.separator;
        File f = new File(path);
        Set<String> before = Arrays.stream(f.listFiles()).map(File::getName).collect(Collectors.toSet());
        FileUnpackUtil.decompress(file, path);
        FileUtil.delete(file);
        // 解压
        Set<String> after = Arrays.stream(f.listFiles()).map(File::getName).collect(Collectors.toSet());
        after.removeAll(before);
        if (after.size() == 1) {
            after.forEach(name -> {
                File topLevelDirectory = new File(path, name);
                if (topLevelDirectory.isDirectory()) {
                    File[] files = topLevelDirectory.listFiles();
                    for (File f2 : files) {
                        f2.renameTo(new File(path + "/" + f2.getName()));
                    }
                    //递归删除文件
                    FileUtil.deleteDirectory(topLevelDirectory);
                }

            });

        }
    }

    @Override
    public void setImage(String imageCode, CodeZone codeZone) {
        if (codeZone == null) {
            throw new CustomRuntimeException("docker image", "the codeZone is not exist");
        }

        Optional<DockerImages> imageOp = dockerImagesRepository.findByImageCode(imageCode);
        if (imageOp.isEmpty()) {
            throw new CustomRuntimeException("docker image", "the docker image is not exist");
        }

        DockerImages image = imageOp.get();
        codeZone.setImage(image.getImage());
        codeZoneRepository.save(codeZone);

        log.debug("docker-set-image success, codeZoneId: {}, imageCode: {}", codeZone.getId(), imageCode);
    }

    @Override
    public void download(CodeZone codeZone, HttpServletRequest request, HttpServletResponse response) {
        List<String> ignoreList = new ArrayList<>();
        String[] strArray = null;
        if (codeZone.getEnvironmentVer().getEnvironment().getFileTreeIgnore() != null) {
            strArray = codeZone.getEnvironmentVer().getEnvironment().getFileTreeIgnore().split(";");
        }
        if (strArray != null) {
            ignoreList = Arrays.asList(strArray);
        }

        List<FileDownloadBean> fileDownloadBeans = new ArrayList<>();
        fileDownloadBeans.add(new FileDownloadBean(codeZone.getEnvironmentVer().getEnvironment().getName(),
            DirUtil.join(codeZone.getRootPath(), Constant.CODE_ZONE_SOURCE_PATH), ignoreList));
        this.fileService.download(fileDownloadBeans, systemProperties.getCodeZone().getDownload(), request, response);
    }

    @Override
    public void delete(CodeZone codeZone) {
        // 删除数据
        codeZone.setDeleted(true);
        this.codeZoneRepository.save(codeZone);
        Playground playground = this.playgroundRepository.findByBindTypeAndBindObject(PlaygroundBindType.CODE_ZONE,
            codeZone.getId());
        if (playground != null) {
            playground.setDeleted(true);
            playgroundRepository.save(playground);
            if (playground.getDockerContainer() != null) {
                playground.getDockerContainer().setDeleted(true);
                dockerRepository.save(playground.getDockerContainer());
            }
        }
        StorageService storageService = storageProvider.getStorageService(codeZone.getNfsNodeId());

        // 删除文件
        String cmd = systemProperties.getBtrfs().getRemoveCmd().replace("{target}", codeZone.getRootPath()
            .replace(storageService.getPath()+Constant.CODE_ZONE_PATH, systemProperties.getCodeZone().getPathInBtrfs())
            .replace(systemProperties.getDocker().getStorage().getNfsPath(),
                systemProperties.getDocker().getStorage().getFileServerPath()));

        storageService.execCommandBtrfs(cmd);
    }

    private EnvironmentVer getEnvVer(String id) {
        return environmentVerRepository.findById(Long.valueOf(id))
            .orElseThrow(() -> new CustomRuntimeException(String.format("EnvironmentVer(ID:%s)不存在", id)));
    }

    private EnvironmentVer getEnvVerByEnvCode(String envCode) {
        EnvironmentVer environmentVer = environmentVerRepository.findByEnvCode(envCode);
        if (environmentVer == null) {
            throw new CustomRuntimeException("env is empty");
        }

        return environmentVer;
    }

    @Override
    public CodeZone getCodeZone(Long codeZoneId) {
        CodeZone codeZone = codeZoneRepository.findById(codeZoneId)
            .orElseThrow(() -> new CustomRuntimeException(String.format("CodeZone(ID:%s)不存在", codeZoneId)));
        permissionService.checkPermission(codeZone.getTenantId());
        return codeZone;
    }


    /**
     * 设置环境变量
     *
     * @param tenant   租户信息
     * @param codeZone codeZone信息
     * @param envMap   添加的环境变量
     * @return 修改后的配置文件内容
     */
    @Override
    public String setEnvs(Tenant tenant, CodeZone codeZone, HashMap<String, String> envMap) {
        // 配置文件文件名
        String configFilePath =
            codeZone.getRootPath() + File.separator + Constant.CODE_ZONE_SOURCE_PATH + File.separator
                + tenant.getConfigFileName();
        // 配置文件结构 https://github.com/dao42/d42paas_agent/blob/bc6a09e8e15651a4b22f2996abd594406bdc242e/config/localConfig/localConfig.go
        Dict config = YamlUtil.loadByPath(configFilePath);
        if (config == null) {
            log.error("CodeZone配置文件未找到{}", configFilePath);
            config = new Dict();
        }
        HashMap env = (HashMap) config.get("env");
        if (env == null) {
            env = new HashMap<>();
        }
        env.putAll(envMap);
        config.put("env", env);
        try {
            FileWriter writer = new FileWriter(configFilePath);
            YamlUtil.dump(config, writer);
        } catch (IOException e) {
            throw new CustomRuntimeException("write.codeZone.config.fail", "写入CodeZone配置文件失败", e);
        }
        return cn.hutool.core.io.FileUtil.readString(configFilePath, StandardCharsets.UTF_8);
    }

    @Override
    public void importCodeZone(CodeZoneImportDTO dto, CurrentUserBean tenant) {
        final CodeZone codeZone = getCodeZone(Long.valueOf(dto.getCodeZoneId()));
        dto.getCodeZoneImportFileListDTOList().forEach(item -> this.fileUtilService.write(
            codeZone.getRootPath() + Constant.CODE_ZONE_SOURCE_PATH + "/" + item.getFileName(), item.getFileContent()));
    }

    /**
     * 生成新子卷父级目录名 格式：/{年份}/{租户Id}/{月-日}/
     *
     * @param tenantId 租户ID
     * @return 父级目录名
     */
    public static String getParentFolderName(Long tenantId) {
        LocalDate today = LocalDate.now();
        HashMap<Object, Object> params = new HashMap<>(4);
        params.put("year", today.getYear());
        params.put("tenant", tenantId);
        params.put("month", today.getMonthValue());
        params.put("day", today.getDayOfMonth());
        String templateStr =
            File.separator + "{year}" + File.separator + "{tenant}" + File.separator + "{month}-{day}" + File.separator;
        return StrUtil.format(templateStr, params);
    }

    @Override
    public void fileEdit(Long id, CurrentUserBean tenant, CodeZoneFileEditDto codeZoneFileEditDto) {
        CodeZone codeZone = getCodeZone(id);
        if (codeZone.getTenantId() != tenant.getTenantId()) {
            return;
        }
        String filePath = DirUtil.join(codeZone.getRootPath(), Constant.CODE_ZONE_SOURCE_PATH,
            codeZoneFileEditDto.getFile());
        FileUtil.write(filePath, codeZoneFileEditDto.getContent(), false);
    }

    @Override
    public void updateResources(CodeZone codeZone, ResourcesLimit resourcesLimit) {
        codeZone.setResourcesLimit(resourcesLimit);
        codeZoneRepository.save(codeZone);
    }

    @Override
    public void setCodeZoneEnvs(CodeZone codeZone, Map<String, String> envMap) {
        if (envMap == null) {
            envMap = new HashMap<>();
        }
        codeZone.setEnv(envMap);
        codeZoneRepository.save(codeZone);
        List<Playground> playgrounds = playgroundRepository.findAllByCodeZoneAndStatus(codeZone,
            PlaygroundStatus.ACTIVE);
        Map<String, String> finalEnvMap = envMap;
        playgrounds.parallelStream().forEach(playground -> {
            DockerEnvMQMsg dockerEnvMQMsg = new DockerEnvMQMsg();
            dockerEnvMQMsg.setValue(finalEnvMap);
            mqMessageSender.sendToDocker(MsgType.ENV, playground, dockerEnvMQMsg);
        });
    }

    @Override
    public String push(CodeZonePushDTO codeZonePushDTO, String rootPath) {
        // 本地仓库路径
        String localRepoPath = rootPath + File.separator + "source";
        // 远程仓库地址
        String remoteRepoUrl = codeZonePushDTO.getRepo();
        String token = codeZonePushDTO.getToken();
        String branchToPush = codeZonePushDTO.getRef();  // Specify the branch you want to push
        try (Git git = Git.open(new File(localRepoPath))) {

            // Check out the branch to push
            if (StringUtils.isNotBlank(branchToPush)) {
                git.checkout().setName(branchToPush).call();
            }
            // Set up the push command
            PushCommand pushCommand = git.push();
            if (StringUtils.isNotBlank(token)) {
                pushCommand.setCredentialsProvider(new UsernamePasswordCredentialsProvider("", token));
            }
            if (StringUtils.isBlank(remoteRepoUrl)) {
                log.error("remoteRepoUrl is null");
                return null;
            }
            pushCommand.setRemote(remoteRepoUrl);
            if (StringUtils.isNotBlank(branchToPush)) {
                pushCommand.add("refs/heads/" + branchToPush);
            }

            // 获取分支的最新 commit ID
            String latestCommitId = getLatestCommitId(git.getRepository(), branchToPush);
            log.info("latestCommitId:{}", latestCommitId);

            // Execute the push and get the result
            Iterable<PushResult> pushResults = pushCommand.call();
            // 检查推送结果，处理可能的错误
            for (PushResult result : pushResults) {
                for (RemoteRefUpdate update : result.getRemoteUpdates()) {
                    if (update.getStatus() != RemoteRefUpdate.Status.OK) {
                        log.warn("Push failed for ref {}: {}", update.getRemoteName(), update.getStatus());
                        String message = update.getMessage();
                        if (StringUtils.isNotBlank(message)) {
                            log.error("Error message: {}", message);
                        }
                        throw new CustomRuntimeException("Push failed", update.getStatus().toString());
                    }
                }
            }
            log.info("Push successful!");
            return latestCommitId;
        } catch (IOException | GitAPIException e) {
            log.warn("Push failed!", e);
            throw new CustomRuntimeException("Push failed!");
        }
    }

    // 获取指定分支的最新 commit ID
    public String getLatestCommitId(Repository repository, String branchName) throws IOException {
        // 获取分支的 Ref 对象
        Ref branchRef = repository.findRef("refs/heads/" + branchName);

        // 使用 RevWalk 获取最新 commit
        try (RevWalk walk = new RevWalk(repository)) {
            RevCommit commit = walk.parseCommit(branchRef.getObjectId());
            return commit.getName();
        }
    }

    @Override
    public Map<String, String> getGithubImportProgress(String taskId) {
        String progressKey = RedisPrefix.GITHUB_IMPORT_PROGRESS + taskId;
        String logKey = RedisPrefix.GITHUB_IMPORT_LOG + taskId;

        Map<String, String> result = new HashMap<>();
        result.put("progress", redisRedissonUtil.get(progressKey));
        result.put("logs", redisRedissonUtil.get(logKey));

        return result;
    }

    @Override
    public Map<String, String> getAsyncCreateAndCheckBranchStatus(String taskId) {
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("taskId", taskId);
        String redisKey = RedisPrefix.CHECKOUT_BRANCH_TASK + taskId;
        String status = redisRedissonUtil.get(redisKey);
        if (status == null) {
            resultMap.put("status", CHECKOUT_TASK_RESULT.CHECKOUT_BRANCH_STATUS_NOT_FOUND);
        } else {
            resultMap.put("status", status);
        }
        return resultMap;
    }

    // Add new GitProgressMonitor class
    private static class GitProgressMonitor implements org.eclipse.jgit.lib.ProgressMonitor {

        private final String taskId;
        private final TriConsumer<String, String, String> progressUpdater;
        private String currentTask;
        private String lastMessage;

        public GitProgressMonitor(String taskId, TriConsumer<String, String, String> progressUpdater) {
            this.taskId = taskId;
            this.progressUpdater = progressUpdater;
        }

        @Override
        public void start(int totalTasks) {
            updateProgress("Starting clone operation...");
        }

        @Override
        public void beginTask(String title, int totalWork) {
            if (title == null) {
                return;
            }
            this.currentTask = title;

            // Only log at the start of each major task
            String message = formatTaskMessage(title);
            if (!message.equals(lastMessage)) {
                updateProgress(message);
            }
        }

        @Override
        public void update(int completed) {
            // Skip progress updates to reduce log volume
        }

        @Override
        public void endTask() {
            if (currentTask != null) {
                String message = formatTaskMessage(currentTask) + " completed.";
                if (!message.equals(lastMessage)) {
                    updateProgress(message);
                }
                currentTask = null;
            }
        }

        @Override
        public boolean isCancelled() {
            return false;
        }

        private String formatTaskMessage(String title) {
            if (title == null) {
                return "Processing...";
            }

            if (title.contains("Receiving objects")) {
                return "Receiving objects...";
            } else if (title.contains("Resolving deltas")) {
                return "Resolving deltas...";
            } else if (title.contains("Finding sources")) {
                return "Finding sources...";
            } else if (title.contains("Compressing objects")) {
                return "Compressing objects...";
            } else if (title.contains("Counting objects")) {
                return "Counting objects...";
            } else if (title.contains("Checking out files")) {
                return "Checking out files...";
            } else {
                return title;
            }
        }

        private void updateProgress(String message) {
            // Format current time
            String timestamp = java.time.LocalDateTime.now()
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            // Combine timestamp with message
            String timestampedMessage = timestamp + " " + message;

            lastMessage = message; // Keep original message for comparison
            progressUpdater.accept(taskId, RedisPrefix.CLONE_STATUS_CLONING, timestampedMessage);
        }
    }

    @FunctionalInterface
    private interface TriConsumer<T, U, V> {

        void accept(T t, U u, V v);
    }

    @Override
    @Transactional
    public Map<String, List<Long>> deleteByIdsWithPurpose(Integer expireDays, List<Long> ids, String purpose,
        boolean rateLimit) {
        List<Long> deletedIds = new ArrayList<>();
        List<Long> skippedIds = new ArrayList<>();

        RateLimiter limiter = null;
        if (rateLimit) {
            double permitsPerSecond = systemProperties.getCodeZone().getMaxDeleteRatePerSecond();
            limiter = rateLimiterUtil.getRateLimiter("codezone_batch_delete_purpose", permitsPerSecond);
            log.info("Rate limiting enabled for batch delete by purpose. Rate: {} permits/second", permitsPerSecond);
        }

        // 获取默认过期天数
        int defaultExpireDays = systemProperties.getCodeZone().getDefaultExpireDays();
        int effectiveExpireDays = expireDays >= defaultExpireDays ? expireDays : defaultExpireDays;

        // 计算过期日期
        Date currentDate = new Date();
        Date expirationDate = DateTimeUtil.getSpecifyTime(currentDate,
            -24 * effectiveExpireDays); // Ensure long for multiplication

        for (Long id : ids) {
            try {
                if (rateLimit && limiter != null) {
                    double waitTime = limiter.acquire();
                    if (waitTime > 0) {
                        log.info("Rate limiter acquired for CodeZone ID: {}. Waited for {} seconds.", id, waitTime);
                    }
                }

                CodeZone codeZone = this.codeZoneRepository.findById(id)
                    .orElse(null);

                if (codeZone == null || codeZone.isDeleted()) {
                    deletedIds.add(id);
                    log.info("Skipping deletion for CodeZone ID: {}. Reason: Not found or already deleted.", id);
                    continue;
                }

                // 判断是否超过过期时间 (based on createdDate as per initial requirement)
                boolean isExpired = codeZone.getLastModifiedDate().before(expirationDate);

                // Check if purpose matches the required value
                if (isExpired && codeZone.getPurpose().startsWith(purpose)) {
                    // The existing delete method handles playground pausing/deletion
                    this.delete(codeZone);
                    deletedIds.add(id);
                    log.info("Successfully deleted CodeZone ID: {} with purpose: {}", id, purpose);
                } else {
                    skippedIds.add(id);
                    log.info(
                        "Skipping deletion for CodeZone ID: {}. Reason: Purpose mismatch (expected: {}, actual: {}).",
                        id, purpose, codeZone.getPurpose());
                }
            } catch (Exception e) {
                log.error("Failed to process CodeZone deletion for ID {}: {}", id, e.getMessage(), e);
                skippedIds.add(id);
            }
        }

        Map<String, List<Long>> result = new HashMap<>();
        result.put("deletedIds", deletedIds);
        result.put("skippedIds", skippedIds);
        return result;
    }

    @Override
    @Transactional
    public Map<String, List<Long>> batchDeleteByTimeRange(String startTime, String endTime, Integer expireDays,
        List<Long> ids, String purpose, boolean rateLimit) {
        List<Long> deletedIds = new ArrayList<>();
        List<Long> skippedIds = new ArrayList<>();

        RateLimiter limiter = null;
        if (rateLimit) {
            double permitsPerSecond = systemProperties.getCodeZone().getMaxDeleteRatePerSecond();
            limiter = rateLimiterUtil.getRateLimiter("codezone_batch_delete_time_range", permitsPerSecond);
            log.info("Rate limiting enabled for batch delete by time range. Rate: {} permits/second", permitsPerSecond);
        }

        // 获取默认过期天数
        int defaultExpireDays = systemProperties.getCodeZone().getDefaultExpireDays();
        int effectiveExpireDays = expireDays >= defaultExpireDays ? expireDays : defaultExpireDays;

        // 转换开始时间和结束时间
        Date startDate = StringUtils.isNotBlank(startTime) ?
            DateTimeUtil.stringToDate(startTime, "yyyy-MM-dd HH:mm:ss") : null;
        Date endDate = StringUtils.isNotBlank(endTime) ?
            DateTimeUtil.stringToDate(endTime, "yyyy-MM-dd HH:mm:ss") : null;

        // 计算过期日期
        Date currentDate = new Date();
        Date expirationDate = DateTimeUtil.getSpecifyTime(currentDate,
            -24 * effectiveExpireDays); // Ensure long for multiplication

        log.info("Batch delete by time range - startDate: {}, endDate: {}, expirationDate: {}, effectiveExpireDays: {}",
            startDate, endDate, expirationDate, effectiveExpireDays);

        // 如果ID列表为空，跳过删除
        if (ids == null || ids.isEmpty()) {
            log.info("No CodeZone IDs provided for batch deletion");
            Map<String, List<Long>> result = new HashMap<>();
            result.put("deletedIds", deletedIds);
            result.put("skippedIds", skippedIds);
            return result;
        }

        for (Long id : ids) {
            try {
                if (rateLimit && limiter != null) {
                    double waitTime = limiter.acquire();
                    if (waitTime > 0) {
                        log.info("Rate limiter acquired for CodeZone ID: {}. Waited for {} seconds.", id, waitTime);
                    }
                }

                CodeZone codeZone = this.codeZoneRepository.findById(id).orElse(null);

                if (codeZone == null || codeZone.isDeleted()) {
                    skippedIds.add(id);
                    log.info("Skipping deletion for CodeZone ID: {}. Reason: Not found or already deleted.", id);
                    continue;
                }

                // 判断最后修改时间是否在时间范围内
                Date lastModifiedDate = codeZone.getLastModifiedDate();
                boolean inTimeRange = true;

                if (startDate != null && lastModifiedDate.before(startDate)) {
                    inTimeRange = false;
                }

                if (endDate != null && lastModifiedDate.after(endDate)) {
                    inTimeRange = false;
                }

                // 判断是否超过过期时间 (based on createdDate as per initial requirement)
                boolean isExpired = codeZone.getLastModifiedDate().before(expirationDate);

                // Check purpose if provided
                boolean purposeMatch = StringUtils.isBlank(purpose) || codeZone.getPurpose().startsWith(purpose);

                if (inTimeRange && isExpired && purposeMatch) {
                    // 删除CodeZone
                    this.delete(codeZone);
                    deletedIds.add(id);
                    log.info("Successfully deleted CodeZone ID: {}. Last modified: {}, Created date: {}, Purpose: {}",
                        id, lastModifiedDate, codeZone.getCreatedDate(), codeZone.getPurpose());
                } else {
                    skippedIds.add(id);
                    log.info(
                        "Skipping deletion for CodeZone ID: {}. Reason: Not in time range or not expired or purpose mismatch. "
                            +
                            "In time range: {}, Is expired: {}, Purpose match: {}", id, inTimeRange, isExpired,
                        purposeMatch);
                }
            } catch (Exception e) {
                log.error("Failed to process CodeZone deletion for ID {}: {}", id, e.getMessage(), e);
                skippedIds.add(id);
            }
        }

        Map<String, List<Long>> result = new HashMap<>();
        result.put("deletedIds", deletedIds);
        result.put("skippedIds", skippedIds);

        log.info("Batch deletion by time range completed. Total processed: {}, Successfully deleted: {}, Skipped: {}",
            ids.size(), deletedIds.size(), skippedIds.size());

        return result;
    }

    @Override
    @Transactional
    public Map<String, List<Long>> batchDeleteAllByTimeRange(String startTime, String endTime, Integer expireDays,
        String purpose, boolean rateLimit) {
        List<Long> deletedIds = new ArrayList<>();
        List<Long> skippedIds = new ArrayList<>();

        RateLimiter limiter = null;
        if (rateLimit) {
            double permitsPerSecond = systemProperties.getCodeZone().getMaxDeleteRatePerSecond();
            limiter = rateLimiterUtil.getRateLimiter("codezone_batch_delete_time_range", permitsPerSecond);
            log.info("Rate limiting enabled for batch delete by time range. Rate: {} permits/second", permitsPerSecond);
        }

        // 获取默认过期天数
        int defaultExpireDays = systemProperties.getCodeZone().getDefaultExpireDays();
        int effectiveExpireDays = expireDays >= defaultExpireDays ? expireDays : defaultExpireDays;

        // 转换开始时间和结束时间
        Date startDate = StringUtils.isNotBlank(startTime) ?
            DateTimeUtil.stringToDate(startTime, "yyyy-MM-dd HH:mm:ss") : null;
        Date endDate = StringUtils.isNotBlank(endTime) ?
            DateTimeUtil.stringToDate(endTime, "yyyy-MM-dd HH:mm:ss") : null;

        // 计算过期日期
        Date currentDate = new Date();
        Date expirationDate = DateTimeUtil.getSpecifyTime(currentDate,
            -24 * effectiveExpireDays); // Ensure long for multiplication

        log.info("Batch delete by time range - startDate: {}, endDate: {}, expirationDate: {}, effectiveExpireDays: {}",
            startDate, endDate, expirationDate, effectiveExpireDays);

        List<CodeZone> codeZoneList = this.codeZoneRepository.findAllByLastModifiedDateBetweenAndPurpose(startDate,
            endDate, purpose);

        for (CodeZone codeZone : codeZoneList) {
            try {
                Long id = codeZone.getId();

                if (rateLimit && limiter != null) {
                    double waitTime = limiter.acquire();
                    if (waitTime > 0) {
                        log.info("Rate limiter acquired for CodeZone ID: {}. Waited for {} seconds.", id, waitTime);
                    }
                }

                if (codeZone == null || codeZone.isDeleted()) {
                    skippedIds.add(id);
                    log.info("Skipping deletion for CodeZone ID: {}. Reason: Not found or already deleted.", id);
                    continue;
                }

                // 判断最后修改时间是否在时间范围内
                Date lastModifiedDate = codeZone.getLastModifiedDate();
                boolean inTimeRange = true;

                if (startDate != null && lastModifiedDate.before(startDate)) {
                    inTimeRange = false;
                }

                if (endDate != null && lastModifiedDate.after(endDate)) {
                    inTimeRange = false;
                }

                // 判断是否超过过期时间 (based on createdDate as per initial requirement)
                boolean isExpired = codeZone.getCreatedDate().before(expirationDate);

                // Check purpose if provided
                boolean purposeMatch = StringUtils.isBlank(purpose) || codeZone.getPurpose().startsWith(purpose);

                if (inTimeRange && isExpired && purposeMatch) {
                    // 删除CodeZone
                    this.delete(codeZone);
                    deletedIds.add(id);
                    log.info("Successfully deleted CodeZone ID: {}. Last modified: {}, Created date: {}, Purpose: {}",
                        id, lastModifiedDate, codeZone.getCreatedDate(), codeZone.getPurpose());
                } else {
                    skippedIds.add(id);
                    log.info(
                        "Skipping deletion for CodeZone ID: {}. Reason: Not in time range or not expired or purpose mismatch. "
                            +
                            "In time range: {}, Is expired: {}, Purpose match: {}", id, inTimeRange, isExpired,
                        purposeMatch);
                }
            } catch (Exception e) {
                log.error("Failed to process CodeZone deletion for ID {}: {}", codeZone.getId(), e.getMessage(), e);
                skippedIds.add(codeZone.getId());
            }
        }

        Map<String, List<Long>> result = new HashMap<>();
        result.put("deletedIds", deletedIds);
        result.put("skippedIds", skippedIds);

        log.info("Batch deletion by time range completed. Successfully deleted: {}, Skipped: {}",
            deletedIds.size(), skippedIds.size());

        return result;
    }


}
