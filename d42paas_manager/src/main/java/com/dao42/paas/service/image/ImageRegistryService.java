package com.dao42.paas.service.image;

/**
 * 镜像仓库服务接口
 *
 * <AUTHOR>
 */
public interface ImageRegistryService {

    /**
     * 推送镜像到仓库
     *
     * @param imageTag 镜像标签
     */
    void push(String imageTag);

    /**
     * 从仓库拉取镜像
     *
     * @param imageTag 镜像标签
     */
    void pull(String imageTag);

    /**
     * 删除仓库中的镜像
     *
     * @param imageTag 镜像标签
     */
    void deleteImage(String imageTag);

    /**
     * 检查镜像是否存在于仓库中
     *
     * @param imageTag 镜像标签
     * @return 是否存在
     */
    boolean exists(String imageTag);

    /**
     * 获取镜像的摘要信息
     *
     * @param imageTag 镜像标签
     * @return 镜像摘要
     */
    String getImageDigest(String imageTag);
}
