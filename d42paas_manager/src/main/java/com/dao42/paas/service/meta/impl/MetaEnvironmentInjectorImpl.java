package com.dao42.paas.service.meta.impl;

import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.enums.MetaEnvironmentStatus;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.meta.MetaEnvironment;
import com.dao42.paas.repository.meta.MetaEnvironmentRepository;
import com.dao42.paas.service.meta.MetaEnvironmentInjector;
import com.dao42.paas.service.meta.MetaEnvironmentSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Optional;

/**
 * @meta环境注入器实现 - 基于Overlay文件系统
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MetaEnvironmentInjectorImpl implements MetaEnvironmentInjector {

    private final MetaEnvironmentRepository metaEnvironmentRepository;
    private final MetaEnvironmentSyncService metaEnvironmentSyncService;
    private final SystemProperties systemProperties;

    @Override
    public void injectMetaEnvironment(DockerContainer container, String metaEnvironmentName) {
        try {
            // 查找指定的@meta环境
            Optional<MetaEnvironment> metaEnvOpt = metaEnvironmentRepository.findByName(metaEnvironmentName);
            if (!metaEnvOpt.isPresent()) {
                throw new RuntimeException("@meta环境不存在: " + metaEnvironmentName);
            }

            MetaEnvironment metaEnv = metaEnvOpt.get();
            
            // 检查环境状态
            if (metaEnv.getStatus() != MetaEnvironmentStatus.AVAILABLE) {
                // 尝试同步环境
                metaEnvironmentSyncService.syncEnvironment(metaEnv);
                if (metaEnv.getStatus() != MetaEnvironmentStatus.AVAILABLE) {
                    throw new RuntimeException("@meta环境不可用: " + metaEnvironmentName);
                }
            }

            // 执行Overlay文件系统注入
            performOverlayInjection(container, metaEnv);
            
            log.info("@meta环境注入成功: container={}, metaEnv={}", 
                    container.getId(), metaEnvironmentName);

        } catch (Exception e) {
            log.error("@meta环境注入失败: container={}, metaEnv={}", 
                     container.getId(), metaEnvironmentName, e);
            throw new RuntimeException("@meta环境注入失败", e);
        }
    }

    @Override
    public void injectDefaultMetaEnvironment(DockerContainer container) {
        try {
            // 查找默认@meta环境
            Optional<MetaEnvironment> defaultEnvOpt = metaEnvironmentRepository.findByIsDefaultTrue();
            if (!defaultEnvOpt.isPresent()) {
                log.warn("未找到默认@meta环境，跳过注入: container={}", container.getId());
                return;
            }

            MetaEnvironment defaultEnv = defaultEnvOpt.get();
            injectMetaEnvironment(container, defaultEnv.getName());

        } catch (Exception e) {
            log.error("默认@meta环境注入失败: container={}", container.getId(), e);
            throw new RuntimeException("默认@meta环境注入失败", e);
        }
    }

    @Override
    public boolean isMetaEnvironmentInjected(DockerContainer container) {
        try {
            // 检查容器的@meta目录是否存在
            String metaPath = getContainerMetaPath(container);
            return Files.exists(Paths.get(metaPath));

        } catch (Exception e) {
            log.error("检查@meta环境注入状态失败: container={}", container.getId(), e);
            return false;
        }
    }

    @Override
    public String getContainerMetaEnvironment(DockerContainer container) {
        try {
            // 从容器的@meta目录读取环境信息
            String metaInfoPath = getContainerMetaPath(container) + "/.meta-info";
            if (Files.exists(Paths.get(metaInfoPath))) {
                return Files.readString(Paths.get(metaInfoPath)).trim();
            }
            return null;

        } catch (Exception e) {
            log.error("获取容器@meta环境信息失败: container={}", container.getId(), e);
            return null;
        }
    }

    @Override
    public void cleanupMetaEnvironment(DockerContainer container) {
        try {
            // 卸载Overlay文件系统
            unmountOverlayFileSystem(container);
            
            // 清理临时目录
            cleanupTempDirectories(container);
            
            log.info("@meta环境清理完成: container={}", container.getId());

        } catch (Exception e) {
            log.error("@meta环境清理失败: container={}", container.getId(), e);
        }
    }

    /**
     * 执行Overlay文件系统注入
     */
    private void performOverlayInjection(DockerContainer container, MetaEnvironment metaEnv) throws IOException {
        // 1. 准备目录结构
        String containerMetaPath = getContainerMetaPath(container);
        String lowerDir = metaEnv.getLocalCachePath(); // 只读层 (@meta环境)
        String upperDir = containerMetaPath + "/upper"; // 可写层
        String workDir = containerMetaPath + "/work";   // 工作目录
        String mergedDir = containerMetaPath + "/merged"; // 合并目录

        // 创建必要的目录
        createDirectories(upperDir, workDir, mergedDir);

        // 2. 挂载Overlay文件系统
        mountOverlayFileSystem(lowerDir, upperDir, workDir, mergedDir);

        // 3. 创建符号链接到容器的@meta目录
        createMetaSymlink(container, mergedDir);

        // 4. 写入环境信息
        writeMetaInfo(containerMetaPath, metaEnv.getName());
    }

    /**
     * 挂载Overlay文件系统
     */
    private void mountOverlayFileSystem(String lowerDir, String upperDir, 
                                       String workDir, String mergedDir) throws IOException {
        try {
            String mountCommand = String.format(
                "mount -t overlay overlay -o lowerdir=%s,upperdir=%s,workdir=%s %s",
                lowerDir, upperDir, workDir, mergedDir
            );

            ProcessBuilder pb = new ProcessBuilder("bash", "-c", mountCommand);
            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode != 0) {
                throw new RuntimeException("Overlay文件系统挂载失败: " + exitCode);
            }

            log.debug("Overlay文件系统挂载成功: merged={}", mergedDir);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Overlay文件系统挂载被中断", e);
        }
    }

    /**
     * 卸载Overlay文件系统
     */
    private void unmountOverlayFileSystem(DockerContainer container) {
        try {
            String mergedDir = getContainerMetaPath(container) + "/merged";
            
            ProcessBuilder pb = new ProcessBuilder("umount", mergedDir);
            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode != 0) {
                log.warn("Overlay文件系统卸载失败: container={}, exitCode={}", 
                        container.getId(), exitCode);
            } else {
                log.debug("Overlay文件系统卸载成功: container={}", container.getId());
            }

        } catch (Exception e) {
            log.error("卸载Overlay文件系统失败: container={}", container.getId(), e);
        }
    }

    /**
     * 创建必要的目录
     */
    private void createDirectories(String... dirs) throws IOException {
        for (String dir : dirs) {
            Path path = Paths.get(dir);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
            }
        }
    }

    /**
     * 创建@meta符号链接
     */
    private void createMetaSymlink(DockerContainer container, String mergedDir) throws IOException {
        String containerRootPath = container.getRootPath();
        String metaLinkPath = containerRootPath + "/@meta";
        
        // 删除已存在的链接
        Path linkPath = Paths.get(metaLinkPath);
        if (Files.exists(linkPath)) {
            Files.delete(linkPath);
        }
        
        // 创建新的符号链接
        Files.createSymbolicLink(linkPath, Paths.get(mergedDir));
        
        log.debug("@meta符号链接创建成功: {} -> {}", metaLinkPath, mergedDir);
    }

    /**
     * 写入@meta环境信息
     */
    private void writeMetaInfo(String containerMetaPath, String metaEnvironmentName) throws IOException {
        String metaInfoPath = containerMetaPath + "/.meta-info";
        Files.writeString(Paths.get(metaInfoPath), metaEnvironmentName);
    }

    /**
     * 清理临时目录
     */
    private void cleanupTempDirectories(DockerContainer container) {
        try {
            String containerMetaPath = getContainerMetaPath(container);
            Path metaPath = Paths.get(containerMetaPath);
            
            if (Files.exists(metaPath)) {
                // 递归删除目录
                Files.walk(metaPath)
                    .sorted((a, b) -> b.compareTo(a)) // 先删除文件，后删除目录
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.warn("删除文件失败: {}", path, e);
                        }
                    });
            }

        } catch (Exception e) {
            log.error("清理临时目录失败: container={}", container.getId(), e);
        }
    }

    /**
     * 获取容器的@meta路径
     */
    private String getContainerMetaPath(DockerContainer container) {
        return systemProperties.getImage().getMeta().getLocalCachePath() + 
               "/containers/" + container.getId();
    }
}
