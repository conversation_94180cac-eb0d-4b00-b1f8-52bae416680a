package com.dao42.paas.service.image;

import com.dao42.paas.dto.image.CacheStatistics;
import com.dao42.paas.dto.image.ThreadImageBuildRequest;
import com.dao42.paas.model.image.ThreadImage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Thread镜像服务接口
 *
 * <AUTHOR>
 */
public interface ThreadImageService {

    /**
     * 构建Thread镜像
     *
     * @param request 构建请求
     * @return Thread镜像
     */
    ThreadImage buildThreadImage(ThreadImageBuildRequest request);

    /**
     * 获取Thread镜像列表
     *
     * @param projectId 项目ID
     * @param issueId Issue ID
     * @param containerType 容器类型
     * @param status 状态
     * @param pageable 分页参数
     * @return Thread镜像分页列表
     */
    Page<ThreadImage> getThreadImages(Long projectId, Long issueId, String containerType, String status, Pageable pageable);

    /**
     * 获取Thread镜像详情
     *
     * @param id 镜像ID
     * @return Thread镜像
     */
    ThreadImage getThreadImage(Long id);

    /**
     * 删除Thread镜像
     *
     * @param id 镜像ID
     */
    void deleteThreadImage(Long id);

    /**
     * 推送Thread镜像
     *
     * @param id 镜像ID
     */
    void pushThreadImage(Long id);

    /**
     * 获取最新Thread镜像
     *
     * @param projectId 项目ID
     * @param issueId Issue ID
     * @return Thread镜像
     */
    ThreadImage getLatestThreadImage(Long projectId, Long issueId);

    /**
     * 获取构建日志
     *
     * @param id 镜像ID
     * @return 构建日志
     */
    String getBuildLog(Long id);

    /**
     * 重新构建Thread镜像
     *
     * @param id 镜像ID
     * @return Thread镜像
     */
    ThreadImage rebuildThreadImage(Long id);

    /**
     * 获取用户镜像统计
     *
     * @return 镜像统计
     */
    CacheStatistics getUserImageStatistics();
}
