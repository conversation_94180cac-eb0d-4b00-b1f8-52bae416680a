package com.dao42.paas.service.docker;

import cn.hutool.core.util.StrUtil;
import com.dao42.paas.common.constants.RedisPrefix;
import com.dao42.paas.common.enums.DockerServerStatus;
import com.dao42.paas.common.enums.PlaygroundStatus;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.enums.DockerServerDuty;
import com.dao42.paas.enums.DockerStatus;
import com.dao42.paas.framework.alert.ExceptionMsgBot;
import com.dao42.paas.framework.alert.ExceptionMsgBotBean;
import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.docker.DockerServer;
import com.dao42.paas.model.middleware.MiddlewareInstance;
import com.dao42.paas.repository.PlaygroundRepository;
import com.dao42.paas.repository.docker.DockerRepository;
import com.dao42.paas.repository.docker.DockerServerRepository;
import com.dao42.paas.repository.middleware.MiddlewareInstanceRepository;
import com.dao42.paas.service.QcloudService;
import com.dao42.paas.service.playground.PlaygroundService;
import com.dao42.paas.utils.DateTimeUtil;
import com.dao42.paas.utils.JsonUtil;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DockerServerService {

    private final PlaygroundRepository playgroundRepository;
    private final DockerServerRepository serverRepository;
    private final DockerRepository dockerRepository;
    private final MiddlewareInstanceRepository middlewareRepository;
    private final QcloudService qCloudService;
    private final DockerClientFactory dockerClientFactory;
    private final PlaygroundService playgroundService;
    private final DockerService dockerService;
    private final DockerContainerService dockerContainerService;
    private final StringRedisTemplate stringRedisTemplate;
    private ExceptionMsgBot exceptionMsgBot;

    private SystemProperties systemProperties;

    //mysql中存储的内存是实际内存的一半，所以使用时需要*2
    private final Long MEMORY_RATE= 2L;

    public DockerServerService(PlaygroundRepository playgroundRepository, DockerServerRepository serverRepository,
        DockerRepository dockerRepository, MiddlewareInstanceRepository middlewareRepository,
        QcloudService qCloudService, DockerClientFactory dockerClientFactory,
        @Lazy PlaygroundService playgroundService, @Lazy DockerService dockerService,
        DockerContainerService dockerContainerService, StringRedisTemplate stringRedisTemplate,ExceptionMsgBot exceptionMsgBot,SystemProperties systemProperties) {
        this.playgroundRepository = playgroundRepository;
        this.serverRepository = serverRepository;
        this.dockerRepository = dockerRepository;
        this.middlewareRepository = middlewareRepository;
        this.qCloudService = qCloudService;
        this.dockerClientFactory = dockerClientFactory;
        this.dockerService = dockerService;
        this.dockerContainerService = dockerContainerService;
        this.playgroundService = playgroundService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.exceptionMsgBot = exceptionMsgBot;
        this.systemProperties=systemProperties;
    }


    /**
     * 启动时，添加初始数据到redis
     */
    @PostConstruct
    public void init() {
        List<DockerServer> serverList = this.serverRepository.findAllByStatus(DockerServerStatus.ACTIVE);
        this.initRedisData(serverList);
    }

    /**
     * 查询所有
     *
     * @param status 当前可用状态
     * @return DockerServer集合
     */
    public List<DockerServer> findAll(DockerServerStatus status) {
        return serverRepository.findAllByStatus(status);
    }

    /**
     * 查询所有
     *
     * @param isTemp 是否临时服务器
     * @return DockerServer集合
     */
    public List<DockerServer> findAll(DockerServerStatus status, boolean isTemp) {
        return serverRepository.findAllByStatusAndTemp(status, isTemp);
    }

    /**
     * 查询
     *
     * @param serverId DockerServer ID
     * @return DockerServer
     */
    public DockerServer get(Long serverId) {
        return serverRepository
            .findById(serverId)
            .orElseThrow(() -> new CustomRuntimeException(String.format("server.not.existed(id=%s)", serverId),
                String.format("DockerServer(id=%s)不存在", serverId)));
    }

    public DockerServer getDockerServer(Long serverId) {
        Optional<DockerServer> dockerServer = serverRepository.findById(serverId);
        if (!dockerServer.isPresent()){
            log.warn("DockerServer(id=%s)不存在", serverId);
            return null;
        }
        return dockerServer.get();
    }

    /**
     * 批量查询
     *
     * @param serverId DockerServer ID
     * @return DockerServer
     */
    public List<DockerServer> get(Long[] serverId) {
        Iterable<DockerServer> byId = serverRepository
            .findAllById(Arrays.asList(serverId));

        if (byId != null) {
            return Lists.newArrayList(byId);
        }
        throw new CustomRuntimeException(String.format("DockerServer(id=%s)不存在", serverId));

    }

    /**
     * 按照职责查询所有可用的DockerServer
     *
     * @param duty 职责（普通容器/中间件）
     * @return DockerServer集合
     */
    private List<DockerServer> getActiveServerByDuty(DockerServerDuty duty) {
        return serverRepository.findAllByStatusAndDutyListContains(
            DockerServerStatus.ACTIVE, duty);
    }

    /**
     * 创建
     *
     * @param entity
     * @return
     */
    public DockerServer create(DockerServer entity) {
        return serverRepository.save(entity);
    }

    /**
     * 设置为上线（ACTIVE）
     *
     * @param serverId DockerServer ID
     */
    public void checkAndSetOnLine(Long serverId) {
        long startTime = System.currentTimeMillis();
        DockerServer server = this.get(serverId);

        log.info("DockerScheduling-aws-ec, 设置上线开始了, serverId: {}, instanceId: {}, docker_server_status: {}",
            serverId, server.getInstanceId(), server.getStatus());
        if (server.getStatus() == DockerServerStatus.ACTIVE) {
            return;
        }

        dockerClientFactory.addClientAndListener(server);
        server.setStatus(DockerServerStatus.ACTIVE);
        serverRepository.save(server);
        // 初始化redis
        this.initRedisData(Collections.singletonList(server));
        // 如果是扩容的机器，上线时添加腾讯云实例保护，避免缩容释放
        if (server.isTemp() && server.getInstanceInfo() != null) {
            try {
//                qCloudService.setInstanceProtect(new String[]{server.getInstanceInfo().getInstanceId()});
                log.info("DockerScheduling-aws-ec, 模拟添加亚马逊实例保护");
            } catch (Exception e) {
                log.error("DockerScheduling-aws-ec, DockerServer上线，设置实例保护失败", e);
            }
        }

        long endTime = System.currentTimeMillis();
        long diff = endTime - startTime;
        log.info("DockerScheduling-aws-ec, 设置上线结束了, serverId: {}, docker_server_status: {}, cost_time: {} ms",
            serverId, server.getStatus(), diff);
    }


    /**
     * 批量设置为上线（ACTIVE）
     *
     * @param serverId DockerServer ID
     */
    public void checkAndSetOnLine(Long[] serverId) {
        List<DockerServer> servers = this.get(serverId);
        Iterator<DockerServer> iterator = servers.iterator();

        while (iterator.hasNext()) {
            DockerServer server = iterator.next();
            if (server.getStatus() == DockerServerStatus.ACTIVE) {
                iterator.remove();
            }
            server.setStatus(DockerServerStatus.ACTIVE);
            dockerClientFactory.addClientAndListener(server);
        }

        serverRepository.saveAll(servers);
        // 初始化redis
        this.initRedisData(servers);
        // 如果是扩容的机器，上线时添加腾讯云实例保护，避免缩容释放
        if (CollectionUtils.isNotEmpty(servers)) {
            try {
//                String[] objects = (String[]) servers.stream().map(item -> item.getInstanceInfo().getInstanceId())
//                    .collect(Collectors.toList()).toArray();
//                qCloudService.setInstanceProtect(objects);
            } catch (Exception e) {
                log.error("DockerServer上线，设置实例保护失败", e);
            }
        }
    }


    /**
     * 设为下线（INACTIVE）
     *
     * @param serverId DockerServer ID
     */
    public void setOffLine(Long serverId) {
        DockerServer server = this.get(serverId);
        if (server.getStatus() != DockerServerStatus.ACTIVE) {
            return;
        }
        dockerService.stopAllByServer(server);
        server.setStatus(DockerServerStatus.INACTIVE);
        serverRepository.save(server);
        // 删除redis值
        stringRedisTemplate.opsForZSet().remove(RedisPrefix.DOCKER_SERVER_MEMORY_LEFT, serverId.toString());
        stringRedisTemplate.opsForZSet().remove(RedisPrefix.DOCKER_SERVER_CPU_LOAD, serverId.toString());

        // 如果是扩容的机器，下线时移除腾讯云实例保护，可以缩容释放
        if (server.isTemp() && server.getInstanceInfo() != null) {
            try {
//                qCloudService.unsetInstanceProtect(new String[]{server.getInstanceInfo().getInstanceId()});
            } catch (Exception e) {
                log.error("DockerServer下线，取消实例保护失败", e);
            }
        }
    }

    /**
     * 查询
     *
     * @param instanceId 腾讯云CVM实例ID
     * @return DockerServer
     */
    public DockerServer getByInstanceId(@NotNull String instanceId) {
        return serverRepository.findByInstanceInfoContains(instanceId)
            .orElseThrow(() -> new CustomRuntimeException("server.not.existed", "DockerServer不存在"));
    }

    /**
     * 查询
     *
     * @param instanceId 查询aws ec实例
     * @return DockerServer
     */
    public Optional<DockerServer> getEcByInstanceId(@NotNull String instanceId) {
        return serverRepository.findByInstanceId(instanceId);
    }

    /**
     * 删除
     *
     * @param instanceId 腾讯云CVM实例ID
     */
    public void deleteByInstanceId(String instanceId) {
        if (StrUtil.isBlank(instanceId)) {
            return;
        }
        log.debug("DockerScheduling-aws-ec, 删除dockerService：{}", instanceId);
        Optional<DockerServer> server = serverRepository.findByInstanceId(instanceId);
        if (!server.isPresent()) {
            log.info("DockerScheduling-aws-ec, server isPresent : {}", instanceId);
            return;
        }

        log.info("DockerScheduling-aws-ec, 缩容 继续生命周期前清理容器和playground, instanceId: {}", instanceId);
        List<Playground> activatedPlaygrounds = playgroundRepository.findAllByStatusAndDockerContainer_DockerServer(
            PlaygroundStatus.ACTIVE, server.get());
        // 将在此server上的容器修改为已删除
        activatedPlaygrounds.stream().map(Playground::getDockerContainer).forEach(d -> {
            log.info("DockerScheduling-aws-ec, 缩容 继续生命周期前清理容器和playground, instanceId: {}, dockerId: {}", instanceId, d.getId());
            d.setStatus(DockerStatus.DELETE_SUCCESS);
            dockerContainerService.updateStatus(d);
        });
        // 将与此server关联的playground修改为失活,并通知IDE Server
        activatedPlaygrounds.forEach(p -> {
            log.info("DockerScheduling-aws-ec, 缩容 继续生命周期前清理容器和playground, instanceId: {}, playgroundId: {}", instanceId, p.getId());
            p.setStatus(PlaygroundStatus.INACTIVE);
            playgroundRepository.save(p);
            playgroundService.notifyPlaygroundInfo(p);
        });
//         移除DockerClient和docker events事件监听（这里无需考虑容器die之后的资源回收，因为redis已经不存在这个server:free:mem了）

        dockerClientFactory.removeClientAndListener(server.get());  //TODO 放开到线上测试

        Optional<DockerServer> optionalDockerServer = serverRepository.findByInstanceId(instanceId);
        if (optionalDockerServer.isPresent()) {
            DockerServer dockerServer = optionalDockerServer.get();
            log.info("DockerScheduling-aws-ec, id==={}", dockerServer.getId());
            dockerServer.setDeleted(true);
            dockerServer.setStatus(DockerServerStatus.INACTIVE);
            serverRepository.save(dockerServer);
        }

    }

    /**
     * 初始化内存记录
     *
     * @param serverList 宿主机集合
     */
    private void initRedisData(List<DockerServer> serverList) {
        if (serverList == null) {
            return;
        }

        for (DockerServer dockerServer : serverList) {
            // 非活跃的dockerSever资源不能用于分配使用
            if (dockerServer.getStatus() == DockerServerStatus.INACTIVE) {
                continue;
            }

            // 已使用内存
            AtomicLong usedMemory = new AtomicLong(0);
            // 已使用CPU
            AtomicLong usedCpu = new AtomicLong(0);

            List<DockerContainer> dockerList =
                dockerRepository.findAllByDockerServerAndStatus(dockerServer, DockerStatus.START_SUCCESS);
//            List<MiddlewareInstance> middlewareList = middlewareRepository.findAllByStatusAndServer(
//                DockerStatus.START_SUCCESS, dockerServer);

            if (dockerList != null) {
                dockerList.forEach(d -> {
                    usedMemory.addAndGet(d.getResourcesLimit().getRAM());
                    usedCpu.addAndGet(d.getResourcesLimit().getCPU());
                    List<MiddlewareInstance> middlewareList = d.getMiddlewares();
                    if (middlewareList != null) {
                        middlewareList.forEach(m -> {
                            usedMemory.addAndGet(m.getResourcesLimit().getRAM());
                            usedCpu.addAndGet(m.getResourcesLimit().getCPU());
                        });
                    }

                });
            }

            long freeMem = (long)(dockerServer.getMemoryMB() * systemProperties.getDockerServer().getMemoryRate()) - usedMemory.get();
            stringRedisTemplate.opsForZSet()
                .addIfAbsent(RedisPrefix.DOCKER_SERVER_MEMORY_LEFT, String.valueOf(dockerServer.getId()), freeMem);

            float cpuLoad = usedCpu.get();
            stringRedisTemplate.opsForZSet()
                .addIfAbsent(RedisPrefix.DOCKER_SERVER_CPU_LOAD, String.valueOf(dockerServer.getId()), cpuLoad);

            log.info("DockerScheduling-aws-ec, 初始化redis资源池, docker_server_id: {}, freeMem: {}, cpuLoad: {}",
                dockerServer.getId(), freeMem, cpuLoad);
        }
    }

    /**
     * 空闲率达到了才会回收，n并且要避免该机器是刚扩容出来
     *
     * @param maxMemoryMB 百分比
     */
    public void checkReduction(double maxMemoryMB) {
        // 查询所有server的剩余内存
        Set<TypedTuple<String>> freeRAM = stringRedisTemplate.opsForZSet()
            .rangeWithScores(RedisPrefix.DOCKER_SERVER_MEMORY_LEFT, 0, -1);
        if (freeRAM == null) {
            exceptionMsgBot.send(new ExceptionMsgBotBean("本次缩容未找到需要下线机器，正在检查是否有需要回收的机器"));
            CheckShrinkage(null);
            return;
        }

        List<Long> collect = freeRAM.stream().map(item -> Long.parseLong(item.getValue())).collect(Collectors.toList());
        Long[] ids = collect.toArray(Long[]::new);
        List<DockerServer> servers = this.get(ids);
        Map<Long, DockerServer> mysqlDockerServer = servers.stream()
            .collect(Collectors.toMap(DockerServer::getId, (k1 -> k1)));
        List<Long> removeDockerServer = new ArrayList<>();
        Date date = new Date();

        for (TypedTuple<String> typedTuple : freeRAM) {
            String key = typedTuple.getValue();
            Double value = typedTuple.getScore();
            DockerServer dockerServer = mysqlDockerServer.get(Long.parseLong(key));
            if (dockerServer == null || dockerServer.getCreatedDate() == null) {
                continue;
            }
            Date specifyTime = DateTimeUtil.getSpecifyTime(dockerServer.getCreatedDate(), 1);
            if ((value / (dockerServer.getMemoryMB() * systemProperties.getDockerServer().getMemoryRate())) * 100 >= maxMemoryMB
                && specifyTime.compareTo(date) <= 0) {
                removeDockerServer.add(dockerServer.getId());
            }
        }

        CheckShrinkage(removeDockerServer.toArray(Long[]::new));

    }

    /**
     * @param serverId
     */
    public void CheckShrinkage(Long[] serverId) {
        //获取上次需要缩容的serverID进行缩容
        String serverIdStr = stringRedisTemplate.opsForValue().get(RedisPrefix.MACHINE_RECYCLING_ID);
        Set<Long> list = JsonUtil.jsonToPojo(serverIdStr, Set.class);
        List<Long> newOffLineId = null;
        //选出新的需要缩容的serverID
        if (serverId != null) {
            newOffLineId = new ArrayList<>(Arrays.asList(serverId));
        }
        //略过本次回收
        //Set<Long> serverIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(list) && newOffLineId != null) {
            newOffLineId.removeAll(list);
            //取消实例保护 缩容
            if (CollectionUtils.isNotEmpty(list)) {
                try {
//                    List<DockerServer> servers = this.get(list.toArray(Long[]::new));
//                    List<String> strings = servers.stream().map(item -> item.getInstanceInfo().instanceId())
//                        .collect(Collectors.toList());
//                    qCloudService.unsetInstanceProtect(strings.toArray(String[]::new));
                    qCloudService.incrementAWSDesiredCapacity(list.size() * -1, false);
                    exceptionMsgBot.send(new ExceptionMsgBotBean(String.format("缩容结束，本次缩容机器为%s",
                        list.stream().map(item -> item.toString()).collect(Collectors.joining(",")))));
                    stringRedisTemplate.opsForValue()
                        .set(RedisPrefix.MACHINE_RECYCLING_ID, JsonUtil.pojoToJson(new ArrayList<>()));
                } catch (Exception e) {
                    exceptionMsgBot.send(new ExceptionMsgBotBean("DockerServer下线，取消实例保护失败"));
                    log.error("DockerServer下线，取消实例保护失败", e);
                }
            }
        }
        //如果有新的需要缩容id
        if (CollectionUtils.isNotEmpty(newOffLineId)) {
            batchSetOffLine(newOffLineId.toArray(Long[]::new), null);
        }
    }

    /**
     * 批量设置离线（INACTIVE）
     *
     * @param serverIdSet 上次下线的机器，由于有人使用则重新投入回收容器，待下次回收
     * @param serverId    DockerServer ID
     */
    public void batchSetOffLine(Long[] serverId, Set<Long> serverIdSet) {
        List<DockerServer> servers = this.get(serverId);
        Iterator<DockerServer> iterator = servers.iterator();
        Set<Long> serverIdRedisSet = new HashSet<>(
            serverId.length + (CollectionUtils.isNotEmpty(serverIdSet) ? serverIdSet.size() : 0));
        //下线操作
        while (iterator.hasNext()) {
            DockerServer server = iterator.next();
            if (server.getStatus() == DockerServerStatus.ACTIVE && !server.isTemp()) {
                iterator.remove();
                continue;
            }
            serverIdRedisSet.add(server.getId());
            server.setStatus(DockerServerStatus.INACTIVE);
        }
        serverRepository.saveAll(servers);
        // 删除redis值
        for (DockerServer server : servers) {
            stringRedisTemplate.opsForZSet().remove(RedisPrefix.DOCKER_SERVER_MEMORY_LEFT, server.getId().toString());
            stringRedisTemplate.opsForZSet().remove(RedisPrefix.DOCKER_SERVER_CPU_LOAD, server.getId().toString());
        }
        //serverIdSet 已经下线过了，直接加入待回收容器
        if (CollectionUtils.isNotEmpty(serverIdSet)) {
            serverIdRedisSet.addAll(serverIdSet);
        }
        exceptionMsgBot.send(new ExceptionMsgBotBean(String.format("本次预下线机器为%s",
            serverIdRedisSet.stream().map(item -> item.toString()).collect(Collectors.joining(",")))));
        stringRedisTemplate.opsForValue().set(RedisPrefix.MACHINE_RECYCLING_ID, JsonUtil.pojoToJson(serverIdRedisSet));

    }

}