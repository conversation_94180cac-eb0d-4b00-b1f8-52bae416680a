package com.dao42.paas.service.meta;

import com.dao42.paas.model.docker.DockerContainer;

/**
 * @meta环境注入器接口
 *
 * <AUTHOR>
 */
public interface MetaEnvironmentInjector {

    /**
     * 为容器注入@meta环境
     *
     * @param container 容器实例
     * @param metaEnvironmentName @meta环境名称
     */
    void injectMetaEnvironment(DockerContainer container, String metaEnvironmentName);

    /**
     * 为容器注入默认@meta环境
     *
     * @param container 容器实例
     */
    void injectDefaultMetaEnvironment(DockerContainer container);

    /**
     * 检查@meta环境是否已注入
     *
     * @param container 容器实例
     * @return 是否已注入
     */
    boolean isMetaEnvironmentInjected(DockerContainer container);

    /**
     * 获取容器的@meta环境信息
     *
     * @param container 容器实例
     * @return @meta环境名称
     */
    String getContainerMetaEnvironment(DockerContainer container);

    /**
     * 清理容器的@meta环境
     *
     * @param container 容器实例
     */
    void cleanupMetaEnvironment(DockerContainer container);
}
