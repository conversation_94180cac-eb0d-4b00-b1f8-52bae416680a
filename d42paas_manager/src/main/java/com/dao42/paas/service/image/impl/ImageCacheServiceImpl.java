package com.dao42.paas.service.image.impl;

import com.dao42.paas.enums.ImageCacheStatus;
import com.dao42.paas.model.image.ImageCache;
import com.dao42.paas.repository.image.ImageCacheRepository;
import com.dao42.paas.service.docker.DockerImageService;
import com.dao42.paas.service.image.ImageCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 镜像缓存服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageCacheServiceImpl implements ImageCacheService {

    private final ImageCacheRepository imageCacheRepository;
    private final DockerImageService dockerImageService;

    @Override
    public boolean hasImage(String serverId, String imageTag) {
        try {
            // 首先检查数据库记录
            Optional<ImageCache> cache = imageCacheRepository
                .findByDockerServerIdAndImageTag(Long.parseLong(serverId), imageTag);
            
            if (cache.isPresent() && cache.get().getStatus() == ImageCacheStatus.AVAILABLE) {
                // 验证镜像是否真实存在
                boolean exists = dockerImageService.imageExists(serverId, imageTag);
                if (!exists) {
                    // 镜像不存在，删除缓存记录
                    imageCacheRepository.delete(cache.get());
                    return false;
                }
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("检查镜像缓存失败: serverId={}, imageTag={}", serverId, imageTag, e);
            return false;
        }
    }

    @Override
    public double getImageScore(String serverId, String imageTag) {
        try {
            Optional<ImageCache> cache = imageCacheRepository
                .findByDockerServerIdAndImageTag(Long.parseLong(serverId), imageTag);
            
            if (cache.isPresent()) {
                ImageCache imageCache = cache.get();
                
                // 基于命中次数和最近使用时间计算评分
                double hitScore = Math.log(imageCache.getHitCount() + 1) * 10;
                
                // 最近使用时间评分 (越近评分越高)
                double timeScore = 0;
                if (imageCache.getLastHitAt() != null) {
                    long hoursSinceLastHit = java.time.Duration.between(
                        imageCache.getLastHitAt(), LocalDateTime.now()).toHours();
                    timeScore = Math.max(0, 100 - hoursSinceLastHit);
                }
                
                return hitScore + timeScore;
            }
            
            return 0;
        } catch (Exception e) {
            log.error("计算镜像评分失败: serverId={}, imageTag={}", serverId, imageTag, e);
            return 0;
        }
    }

    @Override
    @Async
    public void preloadImage(String serverId, String imageTag) {
        try {
            // 检查是否已经存在
            if (hasImage(serverId, imageTag)) {
                log.debug("镜像已存在，跳过预拉取: serverId={}, imageTag={}", serverId, imageTag);
                return;
            }
            
            // 创建或更新缓存记录
            ImageCache cache = imageCacheRepository
                .findByDockerServerIdAndImageTag(Long.parseLong(serverId), imageTag)
                .orElse(new ImageCache());
            
            cache.setDockerServerId(Long.parseLong(serverId));
            cache.setImageTag(imageTag);
            cache.setStatus(ImageCacheStatus.PULLING);
            cache.setCachedAt(LocalDateTime.now());
            imageCacheRepository.save(cache);
            
            // 执行镜像拉取
            dockerImageService.pullImage(serverId, imageTag);
            
            // 更新状态为可用
            cache.setStatus(ImageCacheStatus.AVAILABLE);
            imageCacheRepository.save(cache);
            
            log.info("镜像预拉取完成: serverId={}, imageTag={}", serverId, imageTag);
            
        } catch (Exception e) {
            log.error("镜像预拉取失败: serverId={}, imageTag={}", serverId, imageTag, e);
            
            // 更新状态为失败
            imageCacheRepository
                .findByDockerServerIdAndImageTag(Long.parseLong(serverId), imageTag)
                .ifPresent(cache -> {
                    cache.setStatus(ImageCacheStatus.FAILED);
                    imageCacheRepository.save(cache);
                });
        }
    }

    @Override
    public void updateImageCache(String serverId, String imageTag, Long size) {
        try {
            ImageCache cache = imageCacheRepository
                .findByDockerServerIdAndImageTag(Long.parseLong(serverId), imageTag)
                .orElse(new ImageCache());
            
            cache.setDockerServerId(Long.parseLong(serverId));
            cache.setImageTag(imageTag);
            cache.setCacheSize(size);
            cache.setStatus(ImageCacheStatus.AVAILABLE);
            cache.setCachedAt(LocalDateTime.now());
            cache.setUpdatedAt(LocalDateTime.now());
            
            imageCacheRepository.save(cache);
            
            log.debug("更新镜像缓存信息: serverId={}, imageTag={}, size={}", 
                     serverId, imageTag, size);
            
        } catch (Exception e) {
            log.error("更新镜像缓存失败: serverId={}, imageTag={}", serverId, imageTag, e);
        }
    }

    @Override
    public void recordImageHit(String serverId, String imageTag) {
        try {
            Optional<ImageCache> cacheOpt = imageCacheRepository
                .findByDockerServerIdAndImageTag(Long.parseLong(serverId), imageTag);
            
            if (cacheOpt.isPresent()) {
                ImageCache cache = cacheOpt.get();
                cache.setHitCount(cache.getHitCount() + 1);
                cache.setLastHitAt(LocalDateTime.now());
                cache.setUpdatedAt(LocalDateTime.now());
                
                imageCacheRepository.save(cache);
                
                log.debug("记录镜像命中: serverId={}, imageTag={}, hitCount={}", 
                         serverId, imageTag, cache.getHitCount());
            }
            
        } catch (Exception e) {
            log.error("记录镜像命中失败: serverId={}, imageTag={}", serverId, imageTag, e);
        }
    }

    @Override
    public void removeImageCache(String serverId, String imageTag) {
        try {
            Optional<ImageCache> cache = imageCacheRepository
                .findByDockerServerIdAndImageTag(Long.parseLong(serverId), imageTag);
            
            if (cache.isPresent()) {
                imageCacheRepository.delete(cache.get());
                log.info("删除镜像缓存记录: serverId={}, imageTag={}", serverId, imageTag);
            }
            
        } catch (Exception e) {
            log.error("删除镜像缓存失败: serverId={}, imageTag={}", serverId, imageTag, e);
        }
    }
}
