package com.dao42.paas.service.storage;

import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.framework.alert.ExceptionMsgBot;
import com.dao42.paas.model.NfsNode;
import com.dao42.paas.redis.RedisRedissonUtil;
import com.dao42.paas.repository.NfsNodeRepository;
import com.dao42.paas.repository.docker.DockerRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @ClassName: StorageProvider
 * @Description:
 * @author: 逍幽
 * @date: 2025/6/18 18:52
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StorageProvider {


    private final SystemProperties systemProperties;
    private final BtrfsService btrfsServiceBySystemProperties;
    private final ExceptionMsgBot bot;
    private final NfsNodeRepository nfsNodeRepository;
    private final RedisRedissonUtil redisRedissonUtil;
    private final DockerRepository dockerRepository;
    private final ConcurrentHashMap<Long, StorageService> storageServiceMap = new ConcurrentHashMap<>(8);

    public StorageService getAvailableBtrfsService() {

        // 检查 Btrfs 多模式是否启用
        if (!systemProperties.getBtrfs().isMultiMode()) {
            log.info("BtrfsService multi mode disabled, using system properties for BtrfsService creation.");
            return btrfsServiceBySystemProperties;
        }
        // 查询可用的 nfs 节点
        List<NfsNode> availableNodes = nfsNodeRepository.findAllByEnable(true);

        // 构建需要创建的 StorageService 列表
        availableNodes.stream()
            .filter(nfsNode -> !storageServiceMap.containsKey(nfsNode.getId()))  // 过滤出未缓存的节点
            .forEach(nfsNode -> {
                // 直接使用已查询的 NfsNode 对象，避免再次查询数据库
                StorageService storageService = getStorageService(nfsNode);
                storageServiceMap.put(nfsNode.getId(), storageService);
                log.debug("Added StorageService for NfsNode id: {}", nfsNode.getId());
            });

        List<Long> nodeIds = availableNodes.stream()
            .map(NfsNode::getId)
            .collect(Collectors.toList());

        // 查询有负载的 node 节点列表
        List<Object[]> rawResults = dockerRepository.findLoadedNodeWithCount(nodeIds);

        Map<Long, Long> loadedNodeWithCount = new HashMap<>();
        for (Object[] row : rawResults) {
            Long nodeId = ((Number) row[0]).longValue();
            Long count = ((Number) row[1]).longValue();
            loadedNodeWithCount.put(nodeId, count);
        }

        // 添加没有记录的节点，count 设置为 0
        for (Long nodeId : nodeIds) {
            loadedNodeWithCount.putIfAbsent(nodeId, 0L);
        }

        // 按负载排序（升序）
        List<Map.Entry<Long, Long>> sortedNodes = loadedNodeWithCount.entrySet().stream()
            .sorted(Map.Entry.comparingByValue())
            .collect(Collectors.toList());

        // 打印结果（调试用）
        sortedNodes.forEach(entry ->
            log.debug("Node ID: {}, Container Count: {}", entry.getKey(), entry.getValue()));
        Long leastLoadedNodeId = sortedNodes.get(0).getKey();
        if (leastLoadedNodeId != null) {
            return getStorageService(leastLoadedNodeId);
        }
        return btrfsServiceBySystemProperties;
    }

    /**
     * 根据 nfsId 获取 StorageService 实例
     *
     * @param nfsNodeId
     * @return
     */
    public StorageService getStorageService(Long nfsNodeId) {
        // 检查 Btrfs 多模式是否启用
        if (!systemProperties.getBtrfs().isMultiMode()) {
            log.info("BtrfsService multi mode disabled, using system properties for BtrfsService creation.");
            return btrfsServiceBySystemProperties;
        }
        // 检查缓存中是否存在
        if (storageServiceMap.get(nfsNodeId) != null) {
            log.info("BtrfsService found in cache for nfsNodeId: {}", nfsNodeId);
            return storageServiceMap.get(nfsNodeId);
        }
        // 从数据库中根据主键查询
        NfsNode nfsNode = nfsNodeRepository.findById(nfsNodeId).orElseThrow(() -> {
            log.error("NfsNode with id {} not found", nfsNodeId);
            return new RuntimeException("NfsNode not found");
        });

        // 创建 StorageService 实例并更新到缓存
        StorageService storageService = new NfsService(nfsNode, systemProperties, bot, redisRedissonUtil);
        storageServiceMap.put(nfsNodeId, storageService);
        return storageService;
    }

    /**
     * 根据 nfs 节点信息获取 StorageService 实例
     * @param nfsNode
     * @return
     */
    public StorageService getStorageService(NfsNode nfsNode) {
        return new NfsService(nfsNode, systemProperties, bot, redisRedissonUtil);
    }
}
