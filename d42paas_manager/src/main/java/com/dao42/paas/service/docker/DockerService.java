package com.dao42.paas.service.docker;

import com.dao42.paas.aop.log.annotation.Did;
import com.dao42.paas.bean.FileDownloadBean;
import com.dao42.paas.common.ResourcesLimit;
import com.dao42.paas.common.bean.AgentConfigBean;
import com.dao42.paas.common.constants.MsgType;
import com.dao42.paas.common.constants.RedisPrefix;
import com.dao42.paas.common.constants.SystemConsts;
import com.dao42.paas.common.enums.PlaygroundActiveResult;
import com.dao42.paas.common.enums.URLResourceStatus;
import com.dao42.paas.common.message.DockerEnvMQMsg;
import com.dao42.paas.common.message.DockerRunCmdMQMsg;
import com.dao42.paas.common.message.PlaygroundActiveResultMQMsg;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.constants.Constant;
import com.dao42.paas.enums.DockerRunCmdEnum;
import com.dao42.paas.enums.DockerStatus;
import com.dao42.paas.enums.DockerURLResourceTypeEnum;
import com.dao42.paas.enums.PlaygroundBindType;
import com.dao42.paas.exception.docker.DockerCreateException;
import com.dao42.paas.exception.docker.DockerStartException;
import com.dao42.paas.exception.resource.NoDockerServerException;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneFileDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneFileListDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneLimitFileDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneLimitFileListDTO;
import com.dao42.paas.external.sdk.dto.playground.CmdDTO;
import com.dao42.paas.external.sdk.dto.playground.CmdRunIdDTO;
import com.dao42.paas.framework.alert.ExceptionMsgBot;
import com.dao42.paas.framework.alert.ExceptionMsgBotBean;
import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.framework.jpa.model.AbstractAuditModel;
import com.dao42.paas.model.Environment;
import com.dao42.paas.model.EnvironmentVer;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.PlaygroundBindLog;
import com.dao42.paas.model.RunCmdLog;
import com.dao42.paas.model.codezone.CodeZone;
import com.dao42.paas.model.codezone.CodeZoneDiskResource;
import com.dao42.paas.model.codezone.CodeZoneSnapshot;
import com.dao42.paas.model.codezone.CodeZoneURLResource;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.docker.DockerContainerRecoverLog;
import com.dao42.paas.model.docker.DockerDiskResource;
import com.dao42.paas.model.docker.DockerServer;
import com.dao42.paas.model.docker.DockerURLResource;
import com.dao42.paas.model.middleware.MiddlewareConfig;
import com.dao42.paas.model.middleware.MiddlewareInstance;
import com.dao42.paas.model.resource.URLResource;
import com.dao42.paas.rabbit.MQMessageSender;
import com.dao42.paas.repository.PlaygroundBindLogRepository;
import com.dao42.paas.repository.PlaygroundRepository;
import com.dao42.paas.repository.RunCmdLogRepository;
import com.dao42.paas.repository.TenantRepository;
import com.dao42.paas.repository.codezone.CodeZoneDiskResourceRepository;
import com.dao42.paas.repository.codezone.CodeZoneURLResourceRepository;
import com.dao42.paas.repository.docker.DockerContainerRecoverLogRepository;
import com.dao42.paas.repository.docker.DockerDiskResourceRepository;
import com.dao42.paas.repository.docker.DockerRepository;
import com.dao42.paas.repository.docker.DockerURLResourceRepository;
import com.dao42.paas.repository.middleware.MiddlewareInstanceRepository;
import com.dao42.paas.repository.resource.URLResourceRepository;
import com.dao42.paas.service.storage.BtrfsService;
import com.dao42.paas.service.RedisExternalService;
import com.dao42.paas.service.RunCmdCallBackService;
import com.dao42.paas.service.docker.handler.EventsCallbackHandler;
import com.dao42.paas.service.file.FileService;
import com.dao42.paas.service.impl.MemoryDockerServerSelector;
import com.dao42.paas.service.middleware.MiddlewareExternalService;
import com.dao42.paas.service.middleware.MiddlewareInstanceService;
import com.dao42.paas.service.resource.URLResourceService;
import com.dao42.paas.service.storage.StorageProvider;
import com.dao42.paas.service.unitTest.UnitTestService;
import com.dao42.paas.utils.DirUtil;
import com.dao42.paas.utils.FileMsg;
import com.dao42.paas.utils.FileUtil;
import com.dao42.paas.utils.JsonUtil;
import com.github.dockerjava.api.exception.ConflictException;
import com.github.dockerjava.api.exception.NotFoundException;
import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.github.dockerjava.api.exception.NotModifiedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RDeque;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

@Slf4j
@RequiredArgsConstructor
@Service
public class DockerService {

    private final SystemProperties systemProperties;
    private final TenantRepository tenantRepository;
    private final DockerRepository dockerRepository;
    private final MiddlewareInstanceRepository middlewareRepository;
    private final DockerURLResourceRepository dockerURLResourceRepository;
    private final URLResourceRepository urlResourceRepository;
    private final PlaygroundBindLogRepository playgroundBindLogRepository;
    private final PlaygroundRepository playgroundRepository;
    private final MiddlewareInstanceService middlewareInstanceService;
    private final URLResourceService urlResourceService;
    private final DockerContainerRecoverLogRepository dockerContainerRecoverLogRepository;
    private final DockerDiskResourceRepository dockerDiskResourceRepository;
    private final CodeZoneURLResourceRepository codeZoneURLResourceRepository;
    private final CodeZoneDiskResourceRepository codeZoneDiskResourceRepository;
    private final BtrfsService shellService;
    private final DockerResourceService resourceService;
    private final RedisExternalService redisExternalService;
    private final DockerExternalService dockerExternalService;
    private final MiddlewareExternalService middlewareExternalService;
    private final FileService fileService;
    private final MQMessageSender mqMessageSender;
    private final ExceptionMsgBot bot;
    private final RunCmdLogRepository runCmdLogRepository;
    private final MemoryDockerServerSelector dockerServerSelector;
    private final DockerContainerService dockerContainerService;
    private final BtrfsService btrfsService;
    private final RunCmdCallBackService runCmdCallBackService;
    private final Redisson redisson;

    /**
     * 创建编辑CodeZone的容器
     *
     * @param codeZone codeZone
     * @return
     */
    public DockerContainer generateNewForEdit(CodeZone codeZone, Playground playground) {
        return this.generate(playground, codeZone.getRootPath(), codeZone.getMiddlewareConfigList(), "" , codeZone.getImage());
    }

    /**
     * 创建docker容器（副本模式）
     *
     * @param codeZone codeZone
     * @return
     */
    public DockerContainer generateForDuplicate(CodeZone codeZone, Playground playground) {
        // 1.获取CodeZone的相对路径
        String codeZoneDir = codeZone.getRootPath().replace(systemProperties.getCodeZone().getPath(), "");
        // 2.检查or创建上级目录
        String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String parentPathOnNfs = systemProperties.getDocker().getStorage().getNfsPath() + datePath;
        checkDirExistOrCreate(parentPathOnNfs);
        // 3.创建目标目录
        String dockerContainerDir = datePath + File.separator + "@" + UUID.randomUUID() + File.separator;
        // 4.创建btrfs子卷
        String createCmd = systemProperties.getBtrfs().getCreateCmd()
            .replace("{source}", systemProperties.getCodeZone().getPathInBtrfs() + codeZoneDir)
            .replace("{target}", systemProperties.getDocker().getStorage().getFileServerPath() + dockerContainerDir);
        boolean success = shellService.execCommandBtrfs(createCmd);
        if (!success) {
            throw new CustomRuntimeException("创建子卷失败");
        }

        return this.generate(playground, systemProperties.getDocker().getStorage().getNfsPath() + dockerContainerDir,
            codeZone.getMiddlewareConfigList(), "" ,codeZone.getImage());
    }

    /**
     * 创建docker容器（快照模式）
     *
     * @param snapshot codeZone快照
     * @return
     */
    public DockerContainer generateForSnapshot(CodeZoneSnapshot snapshot, Playground playground,
        String defaultOpenFile) {
        playground.setBindType(PlaygroundBindType.CODE_ZONE_SNAPSHOT);
        playground.setBindObject(snapshot);
        RDeque<String> forkPool = redisson.getDeque(RedisPrefix.CODE_ZONE_SNAPSHOT_FORK_POOL + snapshot.getId());
        String dockerContainerDir = forkPool.poll();
        if (dockerContainerDir == null) {
            // 1.检查or创建上级目录
            String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern("/yyyy/MM/dd"));
            String parentPathOnNfs = systemProperties.getDocker().getStorage().getNfsPath() + datePath;
            checkDirExistOrCreate(parentPathOnNfs);
            // 2.创建目标目录
            dockerContainerDir = datePath + File.separator + "@" + UUID.randomUUID() + File.separator;
        }
        // 执行btrfs创建快照命令
        String rootPath=systemProperties.getDocker().getStorage().getNfsPath() + dockerContainerDir;
        if (!FileUtil.exist(rootPath)) {
            createBtrfsSnapshot(
                snapshot.getPath().replace(systemProperties.getCodeZone().getSnapshotPath(), ""),
                rootPath.replace(systemProperties.getDocker().getStorage().getNfsPath(), ""));
        }
        // 3.创建docker数据
        return this.generate(playground, rootPath,
            snapshot.getMiddlewareConfigList(), defaultOpenFile ,null);
    }

    /**
     * 根据指定路径生成snapshot
     *
     * @param snapshotDir        快照路径
     * @param dockerContainerDir dockerContainer的路径
     */
    public void createBtrfsSnapshot(String snapshotDir, String dockerContainerDir) {
        // 4.创建btrfs子卷
        String cmd = systemProperties.getBtrfs().getCreateCmd()
            .replace("{source}", systemProperties.getCodeZone().getSnapshotPathInBtrfs() + snapshotDir)
            .replace("{target}", systemProperties.getDocker().getStorage().getFileServerPath() + dockerContainerDir);
        boolean success = shellService.execCommandBtrfs(cmd);
        if (!success) {
            throw new CustomRuntimeException("创建子卷失败");
        }
    }

    private void checkDirExistOrCreate(String parentPathOnNfs) {
        File parentDir = new File(parentPathOnNfs);
        if (!parentDir.exists()) {
            boolean mkdirResult = parentDir.mkdirs();
            if (!mkdirResult) {
                throw new CustomRuntimeException(String.format("路径创建失败(%s)", parentPathOnNfs));
            }
            log.info("路径({})不存在，已创建", parentPathOnNfs);
        }
    }

    /**
     * 新建docker容器实例（不创建真实容器）
     *
     * @param playground
     * @param rootPath   代码文件存储根路径
     * @param configList 中间件
     * @return DockerContainer实例
     */
    private DockerContainer generate(Playground playground, String rootPath, List<MiddlewareConfig> configList,
        String defaultOpenFile , String image) {
        DockerContainer docker = new DockerContainer();
        docker.setTenantId(playground.getTenantId());
        if ( image != null){
            docker.setImage(image);
        }else {
            docker.setImage(playground.getEnvironmentVer().getEnvironment().getImage());
        }
        log.info("upgrade docker container generate, playgroundId:{}, codeZoneId: {}, docker's environment:{} ", 
        playground.getId(), playground.getCodeZone().getId(), playground.getEnvironmentVer().getId());
        docker.setEnvironmentVer(playground.getEnvironmentVer());
        docker.setNfsNodeId(playground.getCodeZone().getNfsNodeId());
        // 传递Playground资源配置
        docker.setResourcesLimit(playground.getResourcesLimit());
        docker.setRootPath(rootPath);
        docker.setDefaultOpenFile(defaultOpenFile);

        docker = dockerRepository.save(docker);
        // 创建中间件实例
        for (MiddlewareConfig config : configList) {
            middlewareInstanceService.add(docker, config);
        }
        return docker;
    }

    /**
     * 创建Docker容器
     *
     * @param docker dockerContainer对象
     * @param config 启动配置参数
     * @return 修改后的DockerContainer
     * @throws NoDockerServerException
     * @throws DockerCreateException
     */
    public DockerContainer createContainer(@Did DockerContainer docker, AgentConfigBean config)
        throws NoDockerServerException, DockerCreateException {
        // 判断是否已绑定DockerServer，NOT_INIT就需要选择DockerServer
        if (docker.getStatus() == DockerStatus.NOT_INIT) {
            resourceService.chooseAllServer(docker);
        }
        // 创建容器
        try {
            // 生成配置参数，用于容器启动时的env变量
            this.generateConfig(docker, config);
            // 创建容器
            for (MiddlewareInstance instance : docker.getMiddlewares()) {
                long startTime = System.currentTimeMillis();
                instance.setContainerId(middlewareExternalService.create(instance));
                long endTime = System.currentTimeMillis();
                long diff = endTime - startTime;
                log.info("DockerScheduling-Active, 创建中间键;id:{};dockerServiceId:{};parentDockerId:{}, time: {} ms",
                    instance.getId(), instance.getServer().getId(), instance.getParentDocker().getId(), diff);
            }

            long startTime = System.currentTimeMillis();
            docker.setContainerId(dockerExternalService.create(docker, config));
            long endTime = System.currentTimeMillis();
            long diff = endTime - startTime;
            log.info("DockerScheduling-Active, 创建容器;id:{};dockerServiceId:{}, time: {} ms",docker.getId(),docker.getDockerServer().getId(), diff);

            for (MiddlewareInstance instance : docker.getMiddlewares()) {
                middlewareInstanceService.save(instance);
            }
            return dockerContainerService.save(docker);
        } catch (Exception e) {
            log.error("DockerScheduling-Active, DockerService create error, id: {}, e: {}", docker.getId(), e);
            resourceService.recycleAll(docker);
            throw new DockerCreateException(e);
        }
    }

    /**
     * 检查Docker所需资源，如果已绑定Server，但原Server资源不足，就需要重新绑定Server，重新创建容器
     *
     * @param docker docker
     * @return true:不需要重新绑定;false:需要重新绑定
     * @throws NoDockerServerException
     */
    public boolean checkOriginServerResource(DockerContainer docker) throws NoDockerServerException {
        // 1.申请原宿主机资源，如果资源不足，重新分配宿主机，再创建docker
        List<AbstractAuditModel> rebindList = resourceService.checkAllServer(docker);
        return rebindList.size() == 0;
    }

    /**
     * 启动容器（容器启动前会申请资源，容器停止后归还资源）
     *
     * @param docker docker容器
     * @throws DockerStartException 启动失败 * @see
     * @see EventsCallbackHandler#dealDieEvent(com.github.dockerjava.api.model.Event) 容器停止事件处理
     */    //todo ConflictException 主容器启动失败  容器已经被标记为要删除，解决方案1:启动时发现该容器已经被标记删除，重建该容器 2 发现该容器已经被标记删除，则提示用户该容器已经被删除，无法启动。中间件已经启动 报错InternalServerErrorException 捕获不处理
    public void startContainer(@Did DockerContainer docker) throws DockerStartException {
        try {
            /* 启动中间件 */
            log.info("DockerScheduling-Active, startContainer-start, id: {}", docker.getId());
            for (MiddlewareInstance instance : docker.getMiddlewares()) {
                try {
                    middlewareExternalService.start(instance);
                    // 保存启动过程中修改的端口号和用户变量
                    instance = middlewareInstanceService.updatePortAndEnv(instance);
                    instance.setStatus(DockerStatus.START_SUCCESS);
                    log.info("DockerScheduling-Active, 中间件启动成功, id: {}, status: {}", instance.getId(), instance.getStatus());
                } catch (Exception e) {
                    log.error("DockerScheduling-Active, 中间件启动失败, id: %d, e: {}", instance.getId(), e);
                    instance.setStatus(DockerStatus.START_FAIL);
                    throw e;
                }
            }

            /* 启动主容器 */
            try {
                // 等待 docker 返回成功消息，等待消息写入 redis
                redisExternalService.setMQWaitDockerInfo(docker.getId());
                // 启动容器
                dockerExternalService.start(docker);
                // 保存启动过程中修改的端口号
                docker = dockerContainerService.updatePorts(docker);
                docker.setStatus(DockerStatus.START_SUCCESS);
                log.info("DockerScheduling-Active, 主容器启动成功, id: {}", docker.getId());
            } catch (Exception e) {
                log.error("DockerScheduling-Active, 主容器启动失败, id: {}, e: {}", docker.getId(), e);
                // 启动失败。停止接收agent激活消息
                redisExternalService.removeMQWaitDockerInfo(String.valueOf(docker.getId()));
                docker.setStatus(DockerStatus.START_FAIL);
                throw e;
            }
        } catch (NotFoundException e) {
            log.error("DockerScheduling-Active, DockerService start error1, id: {}, e: {}", docker.getId(), e);
            // 容器未找到，需要重新创建，先归还已扣减的资源
            this.killAll(docker);
            // todo：这里是抛出异常，playgroundService捕获后直接调用create->start比较好，还是重试整个激活流程？
            throw e;
        } catch (ConflictException e) { //如果是容器已经是待删除 则重新创建
            log.error("DockerScheduling-Active, DockerService start error2, id: {}, e: {}", docker.getId(), e);
            //需要重新创建，先归还已扣减的资源
            this.killAll(docker);
            throw e;
        } catch (Exception e) {
            log.error("DockerScheduling-Active, DockerService start error3, id: {}, e: {}", docker.getId(), e);
            // 启动过程出现错误，关闭容器并归还资源
            this.killAll(docker);
            throw new DockerStartException(e);
        } finally {
            for (MiddlewareInstance middlewareInstance : docker.getMiddlewares()) {
                middlewareInstanceService.updateStatus(middlewareInstance);
                log.info("DockerScheduling-Active-startContainer-middle: {}, id: {}, status: {}",
                    middlewareInstance.getMiddleType(), middlewareInstance.getId(), middlewareInstance.getStatus());
            }
            dockerContainerService.updateStatus(docker);
            log.info("DockerScheduling-Active-startContainer-end, 主容器启动, id: {}", docker.getId());
        }
    }

    /**
     * 停止容器(停止失败会尝试kill容器。容器停止后归还资源查看DockerEventListener)
     *
     * @param docker 容器
     * @see EventsCallbackHandler#dealDieEvent(com.github.dockerjava.api.model.Event) 容器停止事件处理
     */
    public void stop(@Did DockerContainer docker) {
        log.debug("DockerScheduling-stop, 停止容器:{}", docker.getId());
        // 删除redis中缓存的文件内容
        String keyPattern = RedisPrefix.PREFIX_FILE_CONTENT + docker.getId() + ":*";
        redisExternalService.removeKeys(keyPattern);
        try {
            // 停止中间件
            for (MiddlewareInstance instance : docker.getMiddlewares()) {
                try {
                    middlewareExternalService.stop(instance);
                    instance.setStatus(DockerStatus.STOP_SUCCESS);
                    log.info("DockerScheduling-stop, 中间件停止成功1，containerId:{}", instance.getContainerId());
                } catch (ConflictException exception) {
                    log.info("DockerScheduling-stop, 中间件停止成功2，containerId:{}", instance.getContainerId());
                    instance.setStatus(DockerStatus.STOP_SUCCESS);
                } catch (NotModifiedException e){
                    log.warn("DockerScheduling-stop middleware already stopped: {}" ,instance.getContainerId());
                } catch (Exception e) {
                    log.error("DockerScheduling-stop, 中间件停止失败，containerId:{}", instance.getContainerId());
                    instance.setStatus(DockerStatus.STOP_FAIL);
                    throw e;
                }
            }
            // 停止主容器
            try {
                dockerExternalService.stop(docker);
                docker.setStatus(DockerStatus.STOP_SUCCESS);
                redisExternalService.removeDockerInfo(docker.getId());
            } catch (ConflictException exception) {
                // docker未运行，不需要停止，认为停止成功
                docker.setStatus(DockerStatus.STOP_SUCCESS);
                redisExternalService.removeDockerInfo(docker.getId());
            }  catch (NotModifiedException e){
                log.warn("DockerScheduling-stop docker already stopped: {}", docker.getId());
            }  catch (Exception e) {
                log.error("DockerScheduling-stop, 容器停止失败，id:{},containerId:{}", docker.getId(), docker.getContainerId());
                docker.setStatus(DockerStatus.STOP_FAIL);
                throw e;
            }
        } catch (Exception e) {
            // stop过程出现异常，尝试kill
            log.error(String.format("DockerScheduling-stop, ockerService stop error, id=%s", docker.getId()), e);
            // kill中间件
            for (MiddlewareInstance instance : docker.getMiddlewares()) {
                try {
                    middlewareExternalService.killAsync(instance);
                    instance.setStatus(DockerStatus.STOP_SUCCESS);
                } catch (ConflictException exception) {
                    instance.setStatus(DockerStatus.STOP_SUCCESS);
                } catch (Exception ex) {
                    instance.setStatus(DockerStatus.STOP_FAIL);
                }
            }
            // kill主容器
            try {
                dockerExternalService.killAsync(docker);
                docker.setStatus(DockerStatus.STOP_SUCCESS);
                redisExternalService.removeDockerInfo(docker.getId());

            } catch (ConflictException exception) {
                docker.setStatus(DockerStatus.STOP_SUCCESS);
                redisExternalService.removeDockerInfo(docker.getId());
            } catch (Exception ex) {
                docker.setStatus(DockerStatus.STOP_FAIL);
            }
        } finally {
            log.info("DockerScheduling-stop, stop，containerId:{} , status : {}", docker.getId(), docker.getStatus());
            docker.getMiddlewares().forEach(middlewareInstanceService::updateStatus);
            dockerContainerService.updateStatus(docker);

        }
    }

    /**
     * 删除容器并回收资源
     *
     * @param docker 容器
     */
    public void remove(@Did DockerContainer docker) {
        log.debug("docker-remove删除容器并回收资源:{}", docker.getId());
//        if (docker.getStatus() == DockerStatus.DELETE_SUCCESS) {
//            return;
//        }

//        log.debug("docker-remove删除容器并回收资源1:{}", docker.getId());
        // 如果文件只属于容器，删除容器的同时删除文件
        // 临时容器可以删，答题数据保留 42 天
        PlaygroundBindLog playgroundBindLog = this.playgroundBindLogRepository.findFirstByDockerContainerIdOrderByIdDesc(
            docker.getId());
        if (PlaygroundBindType.CODE_ZONE_DUPLICATE.equals(playgroundBindLog.getPlayground().getBindType())) {
            String cmd = systemProperties.getBtrfs().getRemoveCmd().replace("{target}", docker.getRootPath()
                .replace(systemProperties.getDocker().getStorage().getNfsPath(),
                    systemProperties.getDocker().getStorage().getFileServerPath()));
            shellService.execCommandBtrfs(cmd);
        }
        /* 删除容器分为两种情况，都无需手动回收资源
           1.容器已停止，之前停止动作已触发资源回收，删除时无需再处理
           2.正在运行的容器，被强制停止，容器触发die事件就会被DockerEventListener回收资源
        */
        try {
            for (MiddlewareInstance instance : docker.getMiddlewares()) {
                try {
                    middlewareExternalService.remove(instance);
                    instance.setStatus(DockerStatus.DELETE_SUCCESS);
                } catch (NotFoundException e) {
                    // 找不到认为删除成功
                    instance.setStatus(DockerStatus.DELETE_SUCCESS);
                } catch (Exception e) {
                    log.error("中间件删除失败", e);
                    instance.setStatus(DockerStatus.DELETE_FAIL);
                }
            }
            try {
                dockerExternalService.remove(docker);
                docker.setStatus(DockerStatus.DELETE_SUCCESS);
            } catch (NotFoundException e) {
                // 找不到认为删除成功
                docker.setStatus(DockerStatus.DELETE_SUCCESS);
            } catch (Exception e) {
                log.error("容器删除失败", e);
                docker.setStatus(DockerStatus.DELETE_FAIL);
            }
            urlResourceService.recycle(docker, true);
        } catch (Exception e) {
            log.error(String.format("DockerService remove error, id=%s", docker.getId()), e);
        } finally {
            docker.getMiddlewares().forEach(middlewareInstanceService::updateStatus);
            dockerContainerService.updateStatus(docker);
        }
    }

    /**
     * 异步停止容器
     *
     * @param docker docker
     */
    @Async
    public void stopAsync(@Did DockerContainer docker) {
        this.stop(docker);
    }

    /**
     * 构建初始资源
     *
     * @param codeZone        codeZone
     * @param dockerContainer dockerContainer
     */
    public void bindResourcesToDocker(CodeZone codeZone, DockerContainer dockerContainer) {
        // 保存URL资源
        this.saveURLResourceForCodeZone(codeZone, dockerContainer);
        // 保存磁盘资源
        this.saveDiskResourceForCodeZone(codeZone, dockerContainer);
    }

    private void saveURLResourceForCodeZone(CodeZone codeZone, DockerContainer dockerContainer) {
        CodeZoneURLResource codeZoneURLResource = codeZoneURLResourceRepository.findByCodeZoneId(codeZone.getId());
        if (codeZoneURLResource != null) {
            DockerURLResource dockerURLResource = new DockerURLResource();
            dockerURLResource.setDockerContainerId(dockerContainer.getId());
            dockerURLResource.setResourceId(codeZoneURLResource.getUrlResourceId());
            dockerURLResource.setDockerURLResourceType(DockerURLResourceTypeEnum.SERVICE_URL_RESOURCE);
            dockerURLResourceRepository.save(dockerURLResource);

            Optional<URLResource> urlResourceOptional = urlResourceRepository.findById(
                dockerURLResource.getResourceId());
            if (urlResourceOptional.isPresent()) {
                URLResource urlResource = urlResourceOptional.get();
                urlResource.setStatus(URLResourceStatus.IDLE);
                urlResourceRepository.save(urlResource);
            }
        }
    }

    private void saveDiskResourceForCodeZone(CodeZone codeZone, DockerContainer dockerContainer) {
        // 磁盘资源
        List<CodeZoneDiskResource> codeZoneDiskResourceList = codeZoneDiskResourceRepository.findByCodeZoneId(
            codeZone.getId());
        for (CodeZoneDiskResource codeZoneDiskResource : codeZoneDiskResourceList) {
            DockerDiskResource dockerDiskResource = new DockerDiskResource();
            dockerDiskResource.setDockerContainerId(dockerContainer.getId());
            dockerDiskResource.setResourceId(codeZoneDiskResource.getDiskResourceId());
            dockerDiskResource.setMountPath(codeZoneDiskResource.getMountPath());
            dockerDiskResourceRepository.save(dockerDiskResource);
        }
    }

    /**
     * 生成docker启动所需的配置参数
     *
     * @param docker docker容器
     * @param config 配置参数
     */
    private void generateConfig(DockerContainer docker, AgentConfigBean config) {
        Playground playground = playgroundRepository.findByDockerContainerId(docker.getId());
        if (playground == null) {
            throw new CustomRuntimeException("Playground information not found, cannot generate configuration");
        }
        EnvironmentVer environmentVer = playground.getCodeZone().getEnvironmentVer();
        Environment environment = environmentVer.getEnvironment();
        log.info("generateConfig, playgroundId:{}, dockerId:{}, environmentVerId:{}", playground.getId(), docker.getId(), environmentVer.getId());

        CodeZone cz = playground.getCodeZone();
        config.setDockerCpuCount((long) cz.getResourcesLimit().getCPU());
        config.setDockerMemory(cz.getResourcesLimit().getRAM());
        config.setDockerId(docker.getId());
        config.setPaasLanguagePackage(environment.getLanguagePackage());
        config.setShellCmd(environment.getShellCmd());
        String shellCmd = environment.getShellCmd();
        if (shellCmd.startsWith(Constant.PLAYGROUND_SHELL_TYPE_BASH)) {
            config.setShellCmdType(Constant.PLAYGROUND_SHELL_TYPE_BASH);
        } else if (shellCmd.startsWith(Constant.PLAYGROUND_SHELL_TYPE_NIX_SHELL)) {
            config.setShellCmdType(Constant.PLAYGROUND_SHELL_TYPE_NIX_SHELL);
        }

        config.setPaasLanguageBashCmd(environmentVer.getBashCmd());
        config.setFileTreeIgnore(Constant.IGNORE_FILE_NAME + environment.getFileTreeIgnore());

        // 语言
        config.setLanguage(environment.getLanguage().getName());
        config.setLanguageVersion(environmentVer.getLanguageVersion());
        if (environment.getLanguage().isConsoleSupported()) {
            config.setConsoleStartCmd(environment.getLanguage().getConsoleStartCmd());
        }
        if (environment.getLanguage().isLspSupported()) {
            config.setLspStartCmd(environment.getLanguage().getLspStartCmd());
            config.setLspLanguageId(environment.getLanguage().getLspLanguageId());
            config.setLspLanguageIds(environment.getLanguage().getLspLanguageIds());
            config.setLspStartCmds(environment.getLanguage().getLspStartCmds());
            config.setLspPort(SystemConsts.LSP_PORT);
        }
        if (environment.getLanguage().isDebugSupported()) {
            config.setDebugStartCmd(environment.getLanguage().getDebugStartCmd());
        }

        // 框架
        config.setFramework(environment.getFramework());
        config.setFrameworkVersion(environmentVer.getFrameworkVersion());

        config.setServicePort(environment.getPort());
        config.setPaasProjectWebPort(SystemConsts.PROJECT_WEB_PORT);
        config.setPaasAgentServerPort(SystemConsts.AGENT_SERVER_PORT);

        if (docker.getTenantId() != null) {
            tenantRepository.findById(docker.getTenantId()).ifPresent(tenant -> {
                // 配置文件文件名
                config.setConfigFileName(tenant.getConfigFileName());
                Optional.ofNullable(tenant.getTenantConfig()).ifPresent(tenantConfig -> {
                    // 是否开启资源监控
                    config.setResourceMonitoring(tenantConfig.getResourceMonitoring());
                });
            });
        }
        // 归还url资源，确定每次使用的是最新
        urlResourceService.recycle(docker, true);
        // 服务url & LSP url & SSH url & agent server url
        config.setSshURL(urlResourceService.getSshUrlResource(docker,true).getUrl());
        config.setUrl(urlResourceService.getServiceUrlResource(docker, true).getUrl());
        if (environmentVer.isLspSupported()) {
            config.setLspURL(urlResourceService.getLspUrlResource(docker, true).getUrl());
        }

        config.setAgentServerURL(urlResourceService.getAgentServerUrlResource(docker, true).getUrl());
    }

    public void download(DockerContainer dockerContainer, HttpServletRequest request, HttpServletResponse response) {
        String filePath = dockerContainer.getRootPath();
        List<String> ignoreList = new ArrayList<>();
        String[] strArray = null;
        if (dockerContainer.getEnvironmentVer().getEnvironment().getFileTreeIgnore() != null) {
            strArray = dockerContainer.getEnvironmentVer().getEnvironment().getFileTreeIgnore().split(";");
        }
        if (strArray != null) {
            ignoreList = Arrays.asList(strArray);
        }

        List<FileDownloadBean> fileDownloadBeans = new ArrayList<>();
        fileDownloadBeans.add(new FileDownloadBean(dockerContainer.getEnvironmentVer().getEnvironment().getName(),
            DirUtil.join(filePath, Constant.CODE_ZONE_SOURCE_PATH), ignoreList));
        this.fileService.download(fileDownloadBeans, systemProperties.getCodeZone().getDownload(), request, response);
    }

    public List<CodeZoneFileDTO> downloadFilesForJson(DockerContainer dockerContainer,
        CodeZoneFileListDTO codeZoneFileListDTO) {
        String filePath = dockerContainer.getRootPath();

        codeZoneFileListDTO.getFileList().forEach(item -> {
            String content = FileUtil.getContent(
                DirUtil.join(filePath, Constant.CODE_ZONE_SOURCE_PATH, item.getFilePath()));
            if (Constant.READ_FILE_SIZE_LIMIT_ERROR_MSG.equals(content)) {
                bot.send(new ExceptionMsgBotBean(
                    content + ":" + DirUtil.join(filePath, Constant.CODE_ZONE_SOURCE_PATH, item.getFilePath())));
            }
            item.setFileContent(content);
        });
        return codeZoneFileListDTO.getFileList();
    }

    /**
     * 下载变更的代码文件，并且按照JSON内容格式返回
     * @param dockerContainer
     * @param codeZoneLimitFileListDTO
     * @return
     */
    public List<CodeZoneLimitFileDTO> downloadChangedFilesForJson(DockerContainer dockerContainer,
        CodeZoneLimitFileListDTO codeZoneLimitFileListDTO) {
        String filePath = dockerContainer.getRootPath();
        codeZoneLimitFileListDTO.getFileList().forEach(item -> {
            item.setFileContent(null);
            item.setErrMsg(null);
            if (!StringUtils.hasText(item.getFilePath())){
                item.setErrMsg(Constant.FILE_CHECK_RESULT.FILE_IS_EMPTY_MSG);
                item.setErrCode(Constant.FILE_CHECK_RESULT.FILE_IS_EMPTY_CODE);
                return;
            }
            FileMsg fileMsg = FileUtil.getFileMsg(
                DirUtil.join(filePath, Constant.CODE_ZONE_SOURCE_PATH, item.getFilePath()), Constant.FILE_SIZE_LIMIT);
            item.setFileContent(fileMsg.getContent());
            item.setErrMsg(fileMsg.getMsg());
            item.setErrCode(fileMsg.getCode());
        });
        return codeZoneLimitFileListDTO.getFileList();
    }

    /**
     * 更新 docker 容器实例
     *
     * @return DockerContainer实例
     */
    @Transactional
    public DockerContainer update(Playground playground) {
        log.info("DockerScheduling-docker-service-update,  playgroundId: {}", playground.getId());
        DockerContainer docker = playground.getDockerContainer();

        DockerContainerRecoverLog recoverLog = new DockerContainerRecoverLog();
        recoverLog.setDockerContainer(docker);
        recoverLog.setContainerId(docker.getContainerId());
        recoverLog.setImage(docker.getImage());
        dockerContainerRecoverLogRepository.save(recoverLog);

        if (playground.getBindType().equals(PlaygroundBindType.CODE_ZONE)
            && org.apache.commons.lang3.StringUtils.isNotBlank(playground.getCodeZone().getImage())) {
            docker.setImage(playground.getCodeZone().getImage());
        } else {
            docker.setImage(playground.getEnvironment().getImage());
        }
        docker.setContainerId(null);
        docker.setStatus(DockerStatus.NOT_INIT);
        // 删掉旧的中间件实例（关联关系暂时无需处理）
        middlewareRepository.deleteAll(docker.getMiddlewares());
        docker.setMiddlewares(new ArrayList<>());

        // 传递Playground资源配置
        docker.setResourcesLimit(playground.getResourcesLimit());
        docker = dockerRepository.save(docker);

        List<MiddlewareConfig> configList = null;
        switch (playground.getBindType()) {
            case CODE_ZONE, CODE_ZONE_DUPLICATE -> configList = playground.getCodeZone().getMiddlewareConfigList();
            case CODE_ZONE_SNAPSHOT -> configList = playground.getCodeZoneSnapshot().getMiddlewareConfigList();
        }

        log.info("DockerScheduling-docker-service-update, playgroundId: {}, dockerId: {}, configList: {}",
            playground.getId(), docker.getId(), configList);
        if (configList != null) {
            // 创建中间件的实例
            for (MiddlewareConfig config : configList) {
                log.info("DockerScheduling-docker-service-update, playgroundId: {}, dockerId: {}, config: {}",
                    playground.getId(), docker.getId(), config);
                middlewareInstanceService.add(docker, config);
            }
        }

        docker = dockerContainerService.getById(docker.getId());

        return docker;
    }

    /**
     * 更新 docker 容器 资源信息
     *
     * @return DockerContainer实例
     */
    public boolean updateResource(DockerContainer docker, ResourcesLimit resourcesLimit, Long mem) {
        boolean requestResource = true;
        // 更新 redis dockerServer 内存
        if (mem < 0) {
            // 申请的资源比当前的小 回收资源
            dockerServerSelector.recycle(docker, mem * -1);
        } else {
            // 申请的资源比当前的多 申请资源
            // TODO:申请资源不足 更换 dockerServer
            requestResource = dockerServerSelector.request(String.valueOf(docker.getDockerServer().getId()), mem, 0);
        }
        docker.setResourcesLimit(resourcesLimit);
        dockerRepository.save(docker);
        return requestResource;
    }

    /**
     * 更新 docker 容器 资源信息
     *
     * @return DockerContainer实例
     */
    public void rollbackResource(DockerContainer docker, Long mem) {
        // 回滚资源
        if (mem > 0) {
            dockerServerSelector.recycle(docker, mem);
        } else {
            dockerServerSelector.request(String.valueOf(docker.getDockerServer().getId()), mem * -1, 0);
        }
    }

    public CmdRunIdDTO runCmd(Playground playground, CmdDTO cmdDTO) {
        String runId = UUID.randomUUID().toString().replace("-", "");
        DockerRunCmdMQMsg msg = new DockerRunCmdMQMsg();
        msg.setCmd(cmdDTO.getCmd());
        msg.setRunId(runId);
        mqMessageSender.sendToDocker(MsgType.RUN_CMD, playground, msg);
        RunCmdLog runCmdLog = new RunCmdLog();
        runCmdLog.setCmd(cmdDTO.getCmd());
        runCmdLog.setDockerContainer(playground.getDockerContainer());
        runCmdLog.setEndOfRun(false);
        runCmdLog.setRunId(runId);
        this.runCmdLogRepository.save(runCmdLog);
        CmdRunIdDTO cmdRunIdDTO = new CmdRunIdDTO();
        cmdRunIdDTO.setRunId(runId);
        return cmdRunIdDTO;
    }

    public void stopRunCmd(Playground playground, String runId) {
        DockerRunCmdMQMsg msg = new DockerRunCmdMQMsg();
        msg.setRunId(runId);
        mqMessageSender.sendToDocker(MsgType.STOP_RUN_CMD, playground, msg);
        RunCmdLog runCmdLog = this.runCmdLogRepository.findByRunId(runId);
        if (runCmdLog != null) {
            runCmdLog.setEndOfRun(true);
            runCmdLogRepository.save(runCmdLog);
        }
    }

    /**
     * 强制关闭容器/中间件，并回收资源
     *
     * @param docker 主容器（包含中间件）
     */
    private void killAll(DockerContainer docker) {
        // kill已启动的容器，docker events监听会回收资源，其他未启动的容器，手动归还资源
        for (MiddlewareInstance middleware : docker.getMiddlewares()) {
            if (middleware.getStatus() == DockerStatus.START_SUCCESS) {
                middlewareExternalService.kill(middleware);
            } else {
                resourceService.recycleSingle(middleware);
            }
        }
        if (docker.getStatus() == DockerStatus.START_SUCCESS) {
            dockerExternalService.kill(docker);
        } else {
            resourceService.recycleSingle(docker);
        }
    }

    /**
     * 发送playground的环境变量给主容器（agent）
     *
     * @param playground playground
     */
    public void sendPlaygroundEnv(Playground playground) {
        // 给docker 发送 环境变量信息
        DockerEnvMQMsg dockerEnvMQMsg = new DockerEnvMQMsg();
        Map<String, String> env = new HashMap<>();
        playground.getDockerContainer().getMiddlewares().forEach(item -> {
            Map<String, Object> userEnvMap = JsonUtil.jsonToMap(item.getUserEnv());
            if (userEnvMap != null) {
                userEnvMap.forEach((key, value) -> env.put(key, String.valueOf(value)));
            }
        });
        if (playground.getBindType() == PlaygroundBindType.CODE_ZONE) {
            env.putAll(playground.getCodeZone().getEnv());
        }
        env.putAll(playground.getEnv());
        dockerEnvMQMsg.setValue(env);
        mqMessageSender.sendToDocker(MsgType.ENV, playground, dockerEnvMQMsg);
    }

    /**
     * 发送激活成功消息
     *
     * @param playground 激活的playground
     */
    public void sendActiveSuccessResult(Playground playground) {
        PlaygroundActiveResultMQMsg resultMQMsg = new PlaygroundActiveResultMQMsg();
        // 返回激活结果
        resultMQMsg.setSuccess(true);
        DockerContainer container = playground.getDockerContainer();

        String agentServerInternalAddr = container.getDockerServer().getIp() + ":"
            + container.getAgentServerPort();

        resultMQMsg.setReason(PlaygroundActiveResult.SUCCESS);
        resultMQMsg.setAgentServerInternalAddr(agentServerInternalAddr);

        mqMessageSender.sendToIdeServer(MsgType.ACTIVE, playground, resultMQMsg);
    }

    /**
     * 发送激活失败消息
     *
     * @param dockerId dockerid
     */
    public void sendActiveFailResult(Long dockerId) {
    	log.debug("sendActiveFailResult-激活失败：{}",dockerId);
        Playground playground = this.playgroundRepository.findByDockerContainerId(dockerId);
        PlaygroundBindLog playgroundBindLog = playgroundBindLogRepository.findFirstByDockerContainerIdOrderByIdDesc(
            dockerId);
        // 切题速度快 dockerInfo 还没有发出就停了，后续以激活不需要发送失败
        if (playground == null) {
            log.debug("sendActiveFailResult-激活失败1：{}",dockerId);
            return;
        }
        DockerContainer docker = dockerContainerService.getById(playgroundBindLog.getDockerContainerId());
        if (docker.getStatus() != DockerStatus.START_FAIL) {
            docker.setStatus(DockerStatus.START_FAIL);
            dockerRepository.save(docker);
        }

        PlaygroundActiveResultMQMsg resultMQMsg = new PlaygroundActiveResultMQMsg();
        // 返回激活结果
        resultMQMsg.setReason(PlaygroundActiveResult.DOCKER_INFO_LOST);
        resultMQMsg.setError("dockerInfo.lost");

        ExceptionMsgBotBean botBean = new ExceptionMsgBotBean();
        botBean.setText("docker.active.fail:" + docker.getId());
        bot.send(botBean);
        mqMessageSender.sendToIdeServer(MsgType.ACTIVE, playgroundBindLog.getPlayground(), resultMQMsg);
    }

    /**
     * 停止指定DockerServer所有容器
     *
     * @param server DockerServer
     */
    public void stopAllByServer(DockerServer server) {
        List<DockerContainer> dockerContainers = dockerRepository.findAllByDockerServerAndStatus(server,
            DockerStatus.START_SUCCESS);
        dockerContainers.forEach(this::stop);
    }

    public void backgroundRunUnittest(String cmd, Playground playground, String runId,
        UnitTestService unitTestService) {
        // TODO: 启动中间件
        try {
            //            if (playground.getDockerContainer().getDockerServer() == null ||
            //                    DockerServerStatus.INACTIVE ==
            // playground.getDockerContainer().getDockerServer().getStatus()) {
            //
            // playground.getDockerContainer().setDockerServer(getFirstDockerServer());
            //            }
            dockerExternalService.execRunCmd(cmd, playground.getDockerContainer(), runId, DockerRunCmdEnum.UNITTEST_CMD,
                runCmdCallBackService);
        } catch (NoDockerServerException e) {
            throw new CustomRuntimeException("no.dockerServer", "资源不足稍后重试");
        }
    }

    //    private DockerServer getFirstDockerServer() throws NoDockerServerException {
    //        Set<String> serverIdSet =
    // this.redisExternalService.getDockerServer(RedisPrefix.DOCKER_SERVER_MEMORY_LEFT, 0, 1);
    //        if (serverIdSet.size() == 0) {
    //            throw new NoDockerServerException("资源不足稍后重试");
    //        }
    //        List<String> list = new ArrayList(serverIdSet);
    //        return dockerServerRepository
    //                .findById(Long.valueOf(list.get(0)))
    //                .orElseThrow(() -> new NoDockerServerException("DockerServer不存在"));
    //    }

    public CmdRunIdDTO backgroundRunCmd(Playground playground, CmdDTO cmdDTO) {
        String runId = UUID.randomUUID().toString().replace("-", "");
        DockerRunCmdMQMsg msg = new DockerRunCmdMQMsg();
        msg.setCmd(cmdDTO.getCmd());
        msg.setRunId(runId);
        mqMessageSender.sendToDocker(MsgType.RUN_CMD, playground, msg);
        RunCmdLog runCmdLog = new RunCmdLog();
        runCmdLog.setCmd(cmdDTO.getCmd());
        runCmdLog.setDockerContainer(playground.getDockerContainer());
        runCmdLog.setEndOfRun(false);
        runCmdLog.setRunId(runId);
        this.runCmdLogRepository.save(runCmdLog);
        CmdRunIdDTO cmdRunIdDTO = new CmdRunIdDTO();
        cmdRunIdDTO.setRunId(runId);
        if (playground.getEnvironment().getName().toLowerCase().contains("mysql")) {
            cmdDTO.setCmd(Constant.MYSQL_HEALTH_CHECK + cmdDTO.getCmd());
        }
        try {
            dockerExternalService.execRunCmd(cmdDTO.getCmd(), playground.getDockerContainer(), runId,
                DockerRunCmdEnum.RUN_CMD, runCmdCallBackService);
        } catch (NoDockerServerException e) {
            throw new CustomRuntimeException("no.dockerServer", "资源不足稍后重试");
        }
        return cmdRunIdDTO;
    }

    public void delete(DockerContainer dockerContainer) {
    	log.debug("删除 dockerContainer btrf 数据;{}",dockerContainer.getId());
        // 删除数据
        dockerContainer.setDeleted(true);
        dockerRepository.save(dockerContainer);
        // 删除文件
        String cmd = systemProperties.getBtrfs().getRemoveCmd().replace("{target}", dockerContainer.getRootPath()
            .replace(systemProperties.getCodeZone().getPath(), systemProperties.getCodeZone().getPathInBtrfs())
            .replace(systemProperties.getDocker().getStorage().getNfsPath(),
                systemProperties.getDocker().getStorage().getFileServerPath()));
        btrfsService.execCommandBtrfs(cmd);
    }
}
