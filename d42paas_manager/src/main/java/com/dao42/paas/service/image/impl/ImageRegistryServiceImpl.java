package com.dao42.paas.service.image.impl;

import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.service.image.ImageRegistryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * 镜像仓库服务实现 - ECR
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageRegistryServiceImpl implements ImageRegistryService {

    private final SystemProperties systemProperties;

    @Override
    public void push(String imageTag) {
        try {
            // 执行docker push命令
            ProcessBuilder pb = new ProcessBuilder("docker", "push", imageTag);
            Process process = pb.start();
            
            // 读取输出
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.debug("Docker push output: {}", line);
                }
            }
            
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                // 读取错误输出
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                    StringBuilder errorOutput = new StringBuilder();
                    String line;
                    while ((line = errorReader.readLine()) != null) {
                        errorOutput.append(line).append("\n");
                    }
                    throw new RuntimeException("Docker push失败: " + errorOutput.toString());
                }
            }
            
            log.info("镜像推送成功: {}", imageTag);
        } catch (IOException | InterruptedException e) {
            log.error("镜像推送失败: {}", imageTag, e);
            throw new RuntimeException("镜像推送失败", e);
        }
    }

    @Override
    public void pull(String imageTag) {
        try {
            // 执行docker pull命令
            ProcessBuilder pb = new ProcessBuilder("docker", "pull", imageTag);
            Process process = pb.start();
            
            // 读取输出
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.debug("Docker pull output: {}", line);
                }
            }
            
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                // 读取错误输出
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                    StringBuilder errorOutput = new StringBuilder();
                    String line;
                    while ((line = errorReader.readLine()) != null) {
                        errorOutput.append(line).append("\n");
                    }
                    throw new RuntimeException("Docker pull失败: " + errorOutput.toString());
                }
            }
            
            log.info("镜像拉取成功: {}", imageTag);
        } catch (IOException | InterruptedException e) {
            log.error("镜像拉取失败: {}", imageTag, e);
            throw new RuntimeException("镜像拉取失败", e);
        }
    }

    @Override
    public void deleteImage(String imageTag) {
        try {
            // 对于ECR，需要使用AWS CLI删除镜像
            // aws ecr batch-delete-image --repository-name clacky/docker --image-ids imageTag=xxx
            String[] parts = imageTag.split("/");
            if (parts.length >= 2) {
                String repositoryName = parts[1] + "/" + parts[2].split(":")[0];
                String tag = parts[2].split(":")[1];
                
                ProcessBuilder pb = new ProcessBuilder(
                    "aws", "ecr", "batch-delete-image",
                    "--repository-name", repositoryName,
                    "--image-ids", "imageTag=" + tag
                );
                
                Process process = pb.start();
                int exitCode = process.waitFor();
                
                if (exitCode != 0) {
                    try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                        StringBuilder errorOutput = new StringBuilder();
                        String line;
                        while ((line = errorReader.readLine()) != null) {
                            errorOutput.append(line).append("\n");
                        }
                        throw new RuntimeException("ECR镜像删除失败: " + errorOutput.toString());
                    }
                }
                
                log.info("ECR镜像删除成功: {}", imageTag);
            }
        } catch (IOException | InterruptedException e) {
            log.error("ECR镜像删除失败: {}", imageTag, e);
            throw new RuntimeException("ECR镜像删除失败", e);
        }
    }

    @Override
    public boolean exists(String imageTag) {
        try {
            // 使用docker manifest inspect检查镜像是否存在
            ProcessBuilder pb = new ProcessBuilder("docker", "manifest", "inspect", imageTag);
            Process process = pb.start();
            
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (IOException | InterruptedException e) {
            log.debug("检查镜像存在性失败: {}", imageTag, e);
            return false;
        }
    }

    @Override
    public String getImageDigest(String imageTag) {
        try {
            // 使用docker inspect获取镜像摘要
            ProcessBuilder pb = new ProcessBuilder(
                "docker", "inspect", "--format={{.RepoDigests}}", imageTag
            );
            Process process = pb.start();
            
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String output = reader.readLine();
                if (output != null && !output.isEmpty()) {
                    // 解析输出获取digest
                    // 格式通常是 [registry/repo@sha256:digest]
                    if (output.contains("@")) {
                        return output.substring(output.indexOf("@") + 1, output.lastIndexOf("]"));
                    }
                }
            }
            
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new RuntimeException("获取镜像摘要失败");
            }
            
            return null;
        } catch (IOException | InterruptedException e) {
            log.error("获取镜像摘要失败: {}", imageTag, e);
            throw new RuntimeException("获取镜像摘要失败", e);
        }
    }
}
