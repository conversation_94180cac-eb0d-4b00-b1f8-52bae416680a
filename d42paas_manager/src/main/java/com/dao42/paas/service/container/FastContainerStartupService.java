package com.dao42.paas.service.container;

import com.dao42.paas.dto.container.ContainerStartupResult;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.docker.DockerServer;

/**
 * 快速容器启动服务接口
 *
 * <AUTHOR>
 */
public interface FastContainerStartupService {

    /**
     * 快速启动容器
     *
     * @param dockerContainer 容器配置
     * @return 启动结果
     */
    ContainerStartupResult fastStartContainer(DockerContainer dockerContainer);

    /**
     * 预热容器启动环境
     *
     * @param dockerContainer 容器配置
     */
    void preheatStartupEnvironment(DockerContainer dockerContainer);

    /**
     * 选择最优镜像
     *
     * @param dockerContainer 容器配置
     * @param targetServer 目标服务器
     * @return 最优镜像标签
     */
    String selectOptimalImage(DockerContainer dockerContainer, DockerServer targetServer);

    /**
     * 并行准备启动资源
     *
     * @param dockerContainer 容器配置
     * @param targetServer 目标服务器
     * @param imageTag 镜像标签
     */
    void parallelPrepareResources(DockerContainer dockerContainer, DockerServer targetServer, String imageTag);

    /**
     * 快速拉取镜像
     *
     * @param serverId 服务器ID
     * @param imageTag 镜像标签
     * @return 是否成功
     */
    boolean fastPullImage(String serverId, String imageTag);

    /**
     * 估算容器启动时间
     *
     * @param dockerContainer 容器配置
     * @param targetServer 目标服务器
     * @return 预估启动时间 (毫秒)
     */
    long estimateStartupTime(DockerContainer dockerContainer, DockerServer targetServer);
}
