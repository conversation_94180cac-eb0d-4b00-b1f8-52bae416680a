package com.dao42.paas.service.codezone;

import com.dao42.paas.bean.CurrentUserBean;
import com.dao42.paas.bean.FileDownloadBean;
import com.dao42.paas.common.ResourcesLimit;
import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.constants.Constant;
import com.dao42.paas.enums.SnapshotPublicationEnum;
import com.dao42.paas.external.sdk.dto.ForkCodeZoneDTO;
import com.dao42.paas.external.sdk.dto.codeZoneSnapshot.CodeZoneSnapshotCopyReturnDTO;
import com.dao42.paas.external.sdk.dto.codeZoneSnapshot.CodeZoneSnapshotForkPoolDto;
import com.dao42.paas.external.sdk.dto.codeZoneSnapshot.CodeZoneSnapshotForkPoolDto.Item;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneLimitFileDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneLimitFileListDTO;
import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.model.PlaygroundBindLog;
import com.dao42.paas.model.codezone.CodeZone;
import com.dao42.paas.model.codezone.CodeZoneSnapshot;
import com.dao42.paas.model.codezone.CodeZoneSnapshotForkJob;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.middleware.MiddlewareConfig;
import com.dao42.paas.repository.PlaygroundBindLogRepository;
import com.dao42.paas.repository.codezone.CodeZoneSnapshotForkJobRepository;
import com.dao42.paas.repository.codezone.CodeZoneSnapshotRepository;
import com.dao42.paas.service.storage.BtrfsService;
import com.dao42.paas.service.file.FileService;
import com.dao42.paas.service.impl.CodeZoneServiceImpl;
import com.dao42.paas.service.middleware.MiddlewareFactory;
import com.dao42.paas.service.permission.PermissionService;
import com.dao42.paas.utils.DirUtil;
import com.dao42.paas.utils.FileMsg;
import com.dao42.paas.utils.FileUtil;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

@RequiredArgsConstructor
@Slf4j
@Service
public class CodeZoneSnapshotService {

    private final CodeZoneSnapshotRepository codeZoneSnapshotRepository;
    private final SystemProperties systemProperties;
    private final CodeZoneService codeZoneService;
    private final CodeZoneForkService codeZoneForkService;
    private final GitService gitService;
    private final PlaygroundBindLogRepository playgroundBindLogRepository;
    private final MiddlewareFactory middlewareFactory;
    private final PermissionService permissionService;
    private final FileService fileService;
    private final BtrfsService btrfsService;
    private final CodeZoneSnapshotForkJobRepository codeZoneSnapshotForkJobRepository;

    @Transactional
    public CodeZoneSnapshot create(CodeZone codeZone, String path, String commitId,
        SnapshotPublicationEnum snapshotPublicationEnum, CurrentUserBean tenant,
        List<MiddlewareConfig> middlewareConfigList) {
        // 生成新子卷父级目录 格式：/{年份}/{租户Id}/{月-日}/
        String folderName = CodeZoneServiceImpl.getParentFolderName(codeZone.getTenantId());
        // 父目录的路径
        String folderPath = systemProperties.getCodeZone().getSnapshotPath() + folderName;
        // btrfs上父目录的路径
        String folderPathOnBtrfs = systemProperties.getCodeZone().getSnapshotPathInBtrfs() + folderName;
        // fork
        String nfsSnapshotPath = codeZoneForkService.fork(path, commitId, folderPath, folderPathOnBtrfs,
            snapshotPublicationEnum, codeZone);

        // 构建Snapshot
        final CodeZoneSnapshot codeZoneSnapshot = new CodeZoneSnapshot();
        codeZoneSnapshot.setCodeZone(codeZone);
        codeZoneSnapshot.setCommitId("1");

        if (codeZone.getUnitTestFramework() != null) {
            codeZoneSnapshot.setUnitTestFrameworkName(codeZone.getUnitTestFramework().getName());
        }
        if (middlewareConfigList == null) {
            middlewareConfigList = codeZone.getMiddlewareConfigList();
        }
        codeZoneSnapshot.setPath(nfsSnapshotPath);
        for (MiddlewareConfig config : middlewareConfigList) {
            MiddlewareConfig newConfig = middlewareFactory.forkConfig(codeZoneSnapshot.getPath(), config);
            newConfig.setExternalSwitch(config.getExternalSwitch());
            codeZoneSnapshot.getMiddlewareConfigList().add(newConfig);
        }
        // 复制codeZone的资源配置
        codeZoneSnapshot.setResourcesLimit(codeZone.getResourcesLimit());
        // 发布后删掉 debug 数据
        FileUtil.deleteFile(
            DirUtil.join(codeZoneSnapshot.getPath(), Constant.CODE_ZONE_SOURCE_PATH) + Constant.DEBUG_TAG_FILE);
        return this.codeZoneSnapshotRepository.save(codeZoneSnapshot);
    }

    public CodeZoneSnapshot get(Long codeZoneSnapshotId) {
        CodeZoneSnapshot codeZoneSnapshot = codeZoneSnapshotRepository.findById(codeZoneSnapshotId).orElseThrow(
            () -> new CustomRuntimeException(String.format("CodeZoneSnapshot(ID:%s)不存在", codeZoneSnapshotId)));
        permissionService.checkPermission(codeZoneSnapshot.getCodeZone().getTenantId());
        return codeZoneSnapshot;
    }

    public CodeZoneSnapshot createByDocker(DockerContainer dockerContainer, CurrentUserBean currentUserBean) {
        PlaygroundBindLog playgroundBindLog =
            playgroundBindLogRepository.findFirstByDockerContainerIdOrderByIdDesc(dockerContainer.getId());
        final CodeZoneSnapshot codeZoneSnapshotOld = this.get(playgroundBindLog.getBindObjectId());
        return create(codeZoneSnapshotOld.getCodeZone(), dockerContainer.getRootPath(), null,
            SnapshotPublicationEnum.DOCKER, currentUserBean, codeZoneSnapshotOld.getMiddlewareConfigList());
    }

    public void download(List<Long> ids, HttpServletRequest request, HttpServletResponse response) {
        List<String> ignoreList = new ArrayList<>();
        String[] strArray = null;
        List<CodeZoneSnapshot> codeZoneSnapshotList = (List<CodeZoneSnapshot>) this.codeZoneSnapshotRepository.findAllById(
            ids);
        if (codeZoneSnapshotList.size() == 0) {
            return;
        }
        List<FileDownloadBean> fileDownloadBeans = new ArrayList<>();
        for (CodeZoneSnapshot codeZoneSnapshot : codeZoneSnapshotList) {
            if (codeZoneSnapshot.getCodeZone().getEnvironmentVer().getEnvironment().getFileTreeIgnore() != null) {
                strArray = codeZoneSnapshot.getCodeZone().getEnvironmentVer().getEnvironment().getFileTreeIgnore()
                    .split(";");
            }
            if (strArray != null) {
                ignoreList = Arrays.asList(strArray);
            }
            fileDownloadBeans.add(
                new FileDownloadBean(codeZoneSnapshot.getCodeZone().getEnvironmentVer().getEnvironment().getName(),
                    DirUtil.join(codeZoneSnapshot.getPath(), Constant.CODE_ZONE_SOURCE_PATH), ignoreList));
        }

        this.fileService.download(fileDownloadBeans,
            systemProperties.getCodeZone().getDownload(),
            request,
            response);
    }

    public List<CodeZoneSnapshotCopyReturnDTO> copy(List<Long> idList, CurrentUserBean tenant) {
        List<CodeZoneSnapshotCopyReturnDTO> list = new ArrayList<>();
        for (Long id : idList) {
            CodeZoneSnapshotCopyReturnDTO item = new CodeZoneSnapshotCopyReturnDTO();
            CodeZoneSnapshot codeZoneSnapshot = this.get(id);
            ForkCodeZoneDTO dto = new ForkCodeZoneDTO();
            dto.setWithMiddlewareData(true);
            dto.setPurpose(null);
            CodeZone codeZoneNew = this.codeZoneService.fork(codeZoneSnapshot.getCodeZone(), tenant.getUserId(), dto);
            CodeZoneSnapshot codeZoneSnapshotNew = this.create(codeZoneNew, codeZoneSnapshot.getPath(), "",
                SnapshotPublicationEnum.CODE_ZONE_SNAPSHOT, tenant, null);
            item.setCodeZoneId(String.valueOf(codeZoneNew.getId()));
            item.setCodeZoneSnapshotId(String.valueOf(codeZoneSnapshotNew.getId()));
            item.setCommitId(codeZoneSnapshotNew.getCommitId());
            item.setOriginalCodeZoneSnapshotId(String.valueOf(id));
            list.add(item);
        }
        return list;
    }

    public void updateResources(CodeZoneSnapshot codeZoneSnapshot, ResourcesLimit resourcesLimit) {
        codeZoneSnapshot.setResourcesLimit(resourcesLimit);
        codeZoneSnapshotRepository.save(codeZoneSnapshot);
    }

    public void delete(CodeZoneSnapshot codeZoneSnapshot) {
        // 删除数据
        codeZoneSnapshot.setDeleted(true);
        this.codeZoneSnapshotRepository.save(codeZoneSnapshot);
        // 删除文件
        String cmd = systemProperties.getBtrfs().getRemoveCmd()
            .replace("{target}", codeZoneSnapshot.getPath()
                .replace(systemProperties.getCodeZone().getPath(), systemProperties.getCodeZone().getPathInBtrfs())
                .replace(systemProperties.getDocker().getStorage().getNfsPath(),
                    systemProperties.getDocker().getStorage().getFileServerPath()));
        btrfsService.execCommandBtrfs(cmd);
    }

    public void forkPool(CodeZoneSnapshotForkPoolDto codeZoneSnapshotForkPoolDto) {
        List<Item> items = codeZoneSnapshotForkPoolDto.getItems();
        if (items.isEmpty()) {
            return;
        }
        LocalDateTime triggerTime = codeZoneSnapshotForkPoolDto.getTriggerTime();
        List<CodeZoneSnapshotForkJob> codeZoneSnapshotForkJobs = items.stream().map(item -> {
            CodeZoneSnapshotForkJob codeZoneSnapshotForkJob = new CodeZoneSnapshotForkJob();
            codeZoneSnapshotForkJob.setCodeZoneSnapshotId(item.getId());
            codeZoneSnapshotForkJob.setNumber(item.getNumber());
            codeZoneSnapshotForkJob.setTriggerTime(triggerTime);
            return codeZoneSnapshotForkJob;
        }).toList();
        codeZoneSnapshotForkJobRepository.saveAll(codeZoneSnapshotForkJobs);
    }

    public List<CodeZoneLimitFileDTO> downloadOriginalFilesForJson(CodeZone codeZone, CodeZoneLimitFileListDTO codeZoneLimitFileListDTO) {
        String filePath = codeZone.getRootPath();
        codeZoneLimitFileListDTO.getFileList().forEach(item -> {
            item.setFileContent(null);
            item.setErrMsg(null);
            if (!StringUtils.hasText(item.getFilePath())){
                item.setErrMsg(Constant.FILE_CHECK_RESULT.FILE_IS_EMPTY_MSG);
                item.setErrCode(Constant.FILE_CHECK_RESULT.FILE_IS_EMPTY_CODE);
                return;
            }
            FileMsg fileMsg = FileUtil.getFileMsg(
                DirUtil.join(filePath, Constant.CODE_ZONE_SOURCE_PATH, item.getFilePath()), Constant.FILE_SIZE_LIMIT);
            item.setFileContent(fileMsg.getContent());
            item.setErrMsg(fileMsg.getMsg());
            item.setErrCode(fileMsg.getCode());
        });
        return codeZoneLimitFileListDTO.getFileList();
    }
}