package com.dao42.paas.service.codezone;


import com.dao42.paas.bean.GitBean;
import com.dao42.paas.common.constants.RedisPrefix;
import com.dao42.paas.constants.Constant;
import com.dao42.paas.external.sdk.dto.git.*;
import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.model.user.TenantUser;
import com.dao42.paas.utils.DateTimeUtil;
import com.dao42.paas.utils.FileUtil;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jgit.api.FetchCommand;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.ResetCommand;
import org.eclipse.jgit.api.ResetCommand.ResetType;
import org.eclipse.jgit.api.Status;
import org.eclipse.jgit.diff.DiffEntry;
import org.eclipse.jgit.diff.DiffEntry.ChangeType;
import org.eclipse.jgit.diff.RenameDetector;
import org.eclipse.jgit.internal.storage.file.FileRepository;
import org.eclipse.jgit.lib.ObjectId;
import org.eclipse.jgit.lib.ObjectLoader;
import org.eclipse.jgit.lib.ObjectReader;
import org.eclipse.jgit.lib.Ref;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.lib.StoredConfig;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.revwalk.RevWalk;
import org.eclipse.jgit.storage.file.FileRepositoryBuilder;
import org.eclipse.jgit.transport.CredentialsProvider;
import org.eclipse.jgit.transport.RefSpec;
import org.eclipse.jgit.transport.URIish;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.eclipse.jgit.treewalk.CanonicalTreeParser;
import org.eclipse.jgit.treewalk.TreeWalk;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GitExternalService {

    public boolean gitInit(String localRepoPath) {
        boolean initFlag = true;
        try (Repository newlyCreatedRepo = FileRepositoryBuilder.create(new File(localRepoPath + "/.git"))) {
            newlyCreatedRepo.create();
        } catch (Exception e) {
            log.error("Git init fail,{}", e.getMessage());
            initFlag = false;
        }
        return initFlag;
    }

    public boolean add(String addFilePath, String localRepoPath) {
        long startTime = System.currentTimeMillis();
        log.debug("Git-Command-add, localRepoPath: {}", localRepoPath);
        if (StringUtils.isEmpty(addFilePath)) {
            addFilePath = ".";
        }
        // commit之前检查是否有空文件夹没提交

        boolean addFileFlag = true;
        try (Git git = Git.open(new File(localRepoPath + "/.git"))) {
            //add file to git
            git.add().addFilepattern(addFilePath).call();
            git.remoteSetUrl().setRemoteUri(new URIish("")).call();
        } catch (Exception e) {
            long diff = System.currentTimeMillis() - startTime;
            log.info("Git-Command-add, Git fail: {}, cost_time: {} ms, e: {}", localRepoPath, diff, e);
            addFileFlag = false;
        }
        return addFileFlag;
    }

    public String commit(String msg, String localRepoPath, TenantUser tenantUser) {
        long startTime = System.currentTimeMillis();
        log.debug("Git-Command-commit, localRepoPath: {}", localRepoPath);
        try (Git git = new Git(new FileRepository(localRepoPath + "/.git"))) {
            //全部提交
            RevCommit revCommit = git.commit()
                .setAll(true)
                .setMessage(msg)
                .setCommitter(tenantUser.getName(),
                    tenantUser.getEmail() == null ? tenantUser.getName() : tenantUser.getEmail())
                .call();
            return revCommit.getName();
        } catch (Exception e) {
            long diff = System.currentTimeMillis() - startTime;
            log.info("Git-Command-commit, Git commit fail: {}, cost_time: {} ms, e: {}", localRepoPath, diff, e);
            throw new CustomRuntimeException("Git commit fail", e.getMessage());
        }
    }

    private void checkEmptyFolder(String localRepoPath) {
        Status status = null;
        try (Git git = new Git(new FileRepository(localRepoPath + "/.git"))) {
            status = git.status().call();
            Set<String> pathList = status.getUntrackedFolders();
            // 检查文件夹下是否有文件，如果没有则新建 .gitkeep
            for (String path : pathList) {
                FileUtil.gitCheckFile(localRepoPath + "/" + path);
            }
        } catch (Exception e) {
            log.error("fail", e.getMessage());
        }
    }

    public String addAndCommit(GitBean gitBean) {
        long startTime = System.currentTimeMillis();
        log.debug("Git-Command-addAndCommit, localRepoPath: {}", gitBean.getLocalRepoPath());

        if (gitBean.getAddFilePath() == null) {
            gitBean.setAddFilePath(".");
        }

        // commit之前检查是否有空文件夹没提交

        try (Git git = new Git(new FileRepository(gitBean.getLocalRepoPath() + "/.git"))) {
            git.add().addFilepattern(gitBean.getAddFilePath()).call();
            //全部提交
            RevCommit revCommit = git.commit().setAll(true).setMessage(gitBean.getMsg()).call();
            return revCommit.getName();
        } catch (Exception e) {
            long diff = System.currentTimeMillis() - startTime;
            log.info("Git-Command-addAndCommit, Git fail: {}, cost_time: {} ms, e: {}", gitBean.getLocalRepoPath(),
                diff, e);
            throw new CustomRuntimeException("Git commit fail", e.getMessage());
        }
    }

    public List<GitStatusDTO> status(String localRepoPath) {
        long startTime = System.currentTimeMillis();
        log.debug("Git-Command-status, localRepoPath: {}", localRepoPath);

        Status status;
        try (Git git = new Git(new FileRepository(localRepoPath + "/.git"))) {
            status = git.status().call();
        } catch (Exception e) {
            long diff = System.currentTimeMillis() - startTime;
            log.info("Git-Command-status, Git fail: {}, cost_time: {} ms, e: {}", localRepoPath, diff, e);
            throw new CustomRuntimeException("Git status fail", e.getMessage());
        }
        return this.gitStatusDTOSFormat(status);
    }

    // 扩展 status 方法，返回当前分支的追踪分支（git rev-parse --abbrev-ref main@{upstream}）
    public HashMap<String, Object> statusRemoteBranch(String localRepoPath) {
        long startTime = System.currentTimeMillis();
        log.debug("Git-Command-statusRemoteBranch, localRepoPath: {}", localRepoPath);

        Status status;
        Boolean hasLocalCommit = false;
        try (Repository repository = new FileRepositoryBuilder()
            .setGitDir(new File(localRepoPath, ".git"))
            .build();
            Git git = new Git(repository)) {

            // 获取 status
            status = git.status().call();

            // 获取当前分支
            String currentBranch = repository.getBranch();

            // 获取是否有未 push 的 commit
            hasLocalCommit = hasLocalCommits(localRepoPath, currentBranch);
        } catch (Exception e) {
            long diff = System.currentTimeMillis() - startTime;
            log.info("Git-Command-status, Git fail: {}, cost_time: {} ms, e: {}", localRepoPath, diff, e);
            throw new CustomRuntimeException("Git status fail", e.getMessage());
        }

        return this.statusRemoteBranchDTOSFormat(status, hasLocalCommit);
    }

    public static Boolean hasLocalCommits(String repoPath, String branchName) {
        try (Repository repository = new FileRepositoryBuilder()
            .setGitDir(new File(repoPath, ".git"))
            .build();
            Git git = new Git(repository)) {

            Ref localBranch = repository.findRef(branchName);
            Ref remoteBranch = repository.findRef("refs/remotes/origin/" + branchName);

            if (localBranch == null) {
                return false;
            }

            if (remoteBranch == null) {
                // 远程分支不存在，返回需要推送
                return true;
            }

            ObjectId localCommitId = localBranch.getObjectId();
            ObjectId remoteCommitId = remoteBranch.getObjectId();

            try (RevWalk revWalk = new RevWalk(repository)) {
                RevCommit localCommit = revWalk.parseCommit(localCommitId);
                RevCommit remoteCommit = revWalk.parseCommit(remoteCommitId);

                return !localCommit.equals(remoteCommit);
            }
        } catch (Exception e) {
            log.error("Error checking local commits", e);
            return false;
        }
    }

    private HashMap<String, Object> statusRemoteBranchDTOSFormat(Status status, Boolean hasUnpushedCommit) {
        List<GitStatusDTO> gitStatusDTOS = gitStatusDTOSFormat(status);

        HashMap<String, Object> res = new HashMap<>();
        res.put("gitStatus", gitStatusDTOS);
        res.put("hasUnpushedCommit", hasUnpushedCommit);
        return res;
    }

    private List<GitStatusDTO> gitStatusDTOSFormat(Status status) {
        List<GitStatusDTO> gitStatusDTOS = new ArrayList<>();
        status.getUntracked().forEach(item -> {
            GitStatusDTO gitStatusDTO = new GitStatusDTO();
            gitStatusDTO.setStatus(ChangeType.ADD.toString());
            gitStatusDTO.setAdded(false);
            gitStatusDTO.setPath(item);
            gitStatusDTOS.add(gitStatusDTO);
        });
        status.getMissing().forEach(item -> {
            GitStatusDTO gitStatusDTO = new GitStatusDTO();
            gitStatusDTO.setStatus(ChangeType.DELETE.toString());
            gitStatusDTO.setAdded(false);
            gitStatusDTO.setPath(item);
            gitStatusDTOS.add(gitStatusDTO);
        });
        status.getModified().forEach(item -> {
            GitStatusDTO gitStatusDTO = new GitStatusDTO();
            gitStatusDTO.setStatus(ChangeType.MODIFY.toString());
            gitStatusDTO.setAdded(false);
            gitStatusDTO.setPath(item);
            gitStatusDTOS.add(gitStatusDTO);
        });
        status.getAdded().forEach(item -> {
            GitStatusDTO gitStatusDTO = new GitStatusDTO();
            gitStatusDTO.setStatus(ChangeType.ADD.toString());
            gitStatusDTO.setAdded(true);
            gitStatusDTO.setPath(item);
            gitStatusDTOS.add(gitStatusDTO);
        });
        status.getChanged().forEach(item -> {
            GitStatusDTO gitStatusDTO = new GitStatusDTO();
            gitStatusDTO.setStatus(ChangeType.MODIFY.toString());
            gitStatusDTO.setAdded(true);
            gitStatusDTO.setPath(item);
            gitStatusDTOS.add(gitStatusDTO);
        });
        status.getRemoved().forEach(item -> {
            GitStatusDTO gitStatusDTO = new GitStatusDTO();
            gitStatusDTO.setStatus(ChangeType.DELETE.toString());
            gitStatusDTO.setAdded(true);
            gitStatusDTO.setPath(item);
            gitStatusDTOS.add(gitStatusDTO);
        });
        status.getConflicting().forEach(item -> {
            GitStatusDTO gitStatusDTO = new GitStatusDTO();
            gitStatusDTO.setStatus("CONFLICTING");
            gitStatusDTO.setAdded(true);
            gitStatusDTO.setPath(item);
            gitStatusDTOS.add(gitStatusDTO);
        });
        return gitStatusDTOS;
    }

    public List<GitCommitDTO> log(String localRepoPath, String commitId, Integer pageCount) {
        if (StringUtils.isEmpty(commitId)) {
            commitId = this.getLastCommitId(localRepoPath);
        }
        if (pageCount == null) {
            pageCount = 100;
        }
        Git git = null;
        Iterable<RevCommit> logs = null;
        try {
            git = new Git(new FileRepository(localRepoPath + "/.git"));
            ObjectId objectId = git.getRepository().resolve(commitId + "^{commit}");
            if (objectId == null) {
                return new ArrayList<>();
            }
            logs = git.log().add(objectId).setMaxCount(pageCount).call();
        } catch (Exception e) {
            throw new CustomRuntimeException("Git log fail", e.getMessage());
        } finally {
            if (git != null) {
                git.close();
            }
        }
        List<GitCommitDTO> gitCommitDTOS = new ArrayList<>();
        for (RevCommit rev : logs) {
            final GitCommitDTO gitCommitDTO = getConventionalCommitMessage(rev);
            gitCommitDTOS.add(gitCommitDTO);
        }
        return gitCommitDTOS;
    }

    public String getLastCommitId(String localRepoPath) {
        long startTime = System.currentTimeMillis();
        log.debug("Git-Command-getLastCommitId, localRepoPath: {}", localRepoPath);

        RevCommit latestCommit;
        try (Git git = new Git(new FileRepository(localRepoPath + "/.git"))) {
            latestCommit = git.log().setMaxCount(1).call().iterator().next();
        } catch (Exception e) {
            long diff = System.currentTimeMillis() - startTime;
            log.info("Git-Command-getLastCommitId, Git fail: {}, cost_time: {} ms, e: {}", localRepoPath, diff, e);
            throw new CustomRuntimeException("Git get last commit", e.getMessage());
        }
        return latestCommit.getName();
    }

    private static GitCommitDTO getConventionalCommitMessage(RevCommit commit) {
        // Prepare the pieces
        final String justTheAuthorNoTime = commit.getAuthorIdent().getName();
        final Instant commitInstant = Instant.ofEpochSecond(commit.getCommitTime());
        final ZoneId zoneId = commit.getAuthorIdent().getTimeZone().toZoneId();
        final ZonedDateTime authorDateTime = ZonedDateTime.ofInstant(commitInstant, zoneId);
        final String gitDateTimeFormatString = "EEE MMM dd HH:mm:ss yyyy Z";
        final String formattedDate = authorDateTime.format(DateTimeFormatter.ofPattern(gitDateTimeFormatString));
        final Date date = DateTimeUtil.stringToDate(formattedDate, gitDateTimeFormatString);
        final GitCommitDTO gitCommitDTO = new GitCommitDTO();
        gitCommitDTO.setCommitId(commit.getName());
        gitCommitDTO.setAuthor(justTheAuthorNoTime);
        gitCommitDTO.setDate(date.getTime());
        gitCommitDTO.setMessage(commit.getFullMessage());
        return gitCommitDTO;
    }

    public void revert(String... strings) {
        long startTime = System.currentTimeMillis();
        log.debug("Git-Command-revert, strings");
        String localRepoPath = strings[0];
        String commitId = strings[1];
        try (Git git = new Git(new FileRepository(localRepoPath + "/.git"))) {
            if (StringUtils.isNotBlank(commitId)) {
                RevWalk walk = new RevWalk(git.getRepository());
                RevCommit revCommit = walk.parseCommit(ObjectId.fromString(commitId));
                RevCommit revertCommit = git.revert().include(revCommit).call();
                if (revertCommit == null) {
                    throw new CustomRuntimeException("Git revert fail",
                        "git conflicts need to be resolved manually and committed again");
                }
            }
        } catch (Exception e) {
            long diff = System.currentTimeMillis() - startTime;
            log.info("Git-Command-revert, Git fail: {}, cost_time: {} ms, e: {}", localRepoPath, diff, e);
            throw new CustomRuntimeException("Git revert fail", e.getMessage());
        }
    }

    public void reset(String localRepoPath, String commitId) {
        long startTime = System.currentTimeMillis();
        log.debug("Git-Command-reset, strings");
        if (StringUtils.isEmpty(commitId)) {
            commitId = this.getLastCommitId(localRepoPath);
        }
        try (Git git = new Git(new FileRepository(localRepoPath + File.separator + ".git"))) {
            git.reset().setMode(ResetType.HARD).setRef(commitId).call();
        } catch (Exception e) {
            long diff = System.currentTimeMillis() - startTime;
            log.info("Git-Command-reset, Git fail: {}, cost_time: {} ms, e: {}", localRepoPath, diff, e);
            throw new CustomRuntimeException("Git reset fail", e.getMessage());
        }
    }

    public List<GitBranchDTO> branch(String localRepoPath) {
        Git git = null;
        List<GitBranchDTO> gitBranchDTOList = new ArrayList<>();
        try {
            git = new Git(new FileRepository(localRepoPath + "/.git"));
            List<Ref> call = git.branchList().call();
            for (Ref ref : call) {
                final GitBranchDTO gitBranchDTO = new GitBranchDTO();
                gitBranchDTO.setName(ref.getName());
                gitBranchDTO.setObjectId(ref.getObjectId().getName());
                if (gitBranchDTO.getName().equals(git.getRepository().getFullBranch())) {
                    gitBranchDTO.setCurrentBranch(true);
                }
                gitBranchDTOList.add(gitBranchDTO);
            }
        } catch (Exception e) {
            throw new CustomRuntimeException("Git branch fail", e.getMessage());
        } finally {
            if (git != null) {
                git.close();
            }
        }
        return gitBranchDTOList;
    }

    public GitBranchDTO getCurrentBranch(String localRepoPath) {
        try (Git git = new Git(new FileRepository(localRepoPath + "/.git"))) {
            String branchName = git.getRepository().getBranch();
            Ref ref = git.getRepository().findRef(branchName);
            if (ref == null) {
                return null;
            }

            final GitBranchDTO gitBranchDTO = new GitBranchDTO();
            gitBranchDTO.setName(ref.getName());
            gitBranchDTO.setObjectId(ref.getObjectId().getName());
            return gitBranchDTO;
        } catch (Exception e) {
            throw new CustomRuntimeException("getCurrentBranch exception", e.getMessage());
        }
    }

    public void createBranch(String localRepoPath, GitCreateBranchDTO gitBranchDTO) {
        try (Git git = new Git(new FileRepository(localRepoPath + "/.git"))) {
            git.branchCreate().setName(gitBranchDTO.getName()).setStartPoint(gitBranchDTO.getCommitId()).call();
        } catch (Exception e) {
            throw new CustomRuntimeException("Git create branch fail", e.getMessage());
        }
    }

    public List<GitDiffFileDTO> show(String rootPath, String commitId) {
        List<GitDiffFileDTO> gitDiffFileDTOS = new ArrayList<>();
        try (Git git = new Git(new FileRepository(rootPath + "/.git"))) {
            Repository repository = git.getRepository();

            ObjectId oldHead = repository.resolve(commitId + "^^{tree}");
            ObjectId head = repository.resolve(commitId + "^{tree}");

            // prepare the two iterators to compute the diff between
            try (ObjectReader reader = repository.newObjectReader()) {
                CanonicalTreeParser oldTreeIter = new CanonicalTreeParser();
                if (oldHead != null) {
                    oldTreeIter.reset(reader, oldHead);
                }
                CanonicalTreeParser newTreeIter = new CanonicalTreeParser();
                newTreeIter.reset(reader, head);

                // finally get the list of changed files
                List<DiffEntry> diffs = git.diff()
                    .setNewTree(newTreeIter)
                    .setOldTree(oldTreeIter)
                    .call();
                RenameDetector rd = new RenameDetector(git.getRepository());
                rd.addAll(diffs);
                diffs = rd.compute();
                for (DiffEntry entry : diffs) {
                    GitDiffFileDTO gitDiffFileDTO = new GitDiffFileDTO();
                    if (ChangeType.DELETE.equals(entry.getChangeType())) {
                        gitDiffFileDTO.setPath(entry.getOldPath());
                    } else {
                        gitDiffFileDTO.setPath(entry.getNewPath());
                    }
                    gitDiffFileDTO.setStatus(entry.getChangeType().toString());
                    gitDiffFileDTOS.add(gitDiffFileDTO);
                }
            }
        } catch (Exception e) {
            throw new CustomRuntimeException("Git log fail", e.getMessage());
        }
        return gitDiffFileDTOS;
    }

    public String showFile(String rootPath, String commitId, String path) {
        try (Git git = new Git(new FileRepository(rootPath + "/.git"))) {
            RevWalk revWalk = new RevWalk(git.getRepository());
            ObjectId objectId = git.getRepository().resolve(commitId);
            RevCommit revCommit = revWalk.parseCommit(objectId);
            return getContent(revCommit, path, git);
        } catch (Exception e) {
            throw new CustomRuntimeException("Git show file fail", e.getMessage());
        }
    }

    private String getContent(RevCommit commit, String path, Git git) throws IOException {
        try (TreeWalk treeWalk = TreeWalk.forPath(git.getRepository(), path, commit.getTree())) {
            if (treeWalk == null) {
                throw new CustomRuntimeException("file.not.exist", "file.not.exist");
            }
            ObjectId blobId = treeWalk.getObjectId(0);
            try (ObjectReader objectReader = git.getRepository().newObjectReader()) {
                ObjectLoader objectLoader = objectReader.open(blobId);
                byte[] bytes = objectLoader.getBytes();
                return new String(bytes, StandardCharsets.UTF_8);
            }
        }
    }

    public void fetch(String path, GitFetchDTO gitFetchDTO) {
        try (Git git = new Git(new FileRepository(path + ".git"))) {
            FetchCommand fetchCommand = git.fetch().setTimeout(10);
            if (gitFetchDTO.getToken() != null && gitFetchDTO.getUsername() != null) {
                String username = gitFetchDTO.getUsername();
                String token = gitFetchDTO.getToken();
                CredentialsProvider credentialsProvider = new UsernamePasswordCredentialsProvider(username, token);
                fetchCommand.setCredentialsProvider(credentialsProvider);
            }
            if (gitFetchDTO.getRemoteBranch() != null && gitFetchDTO.getRemoteName() != null
                && gitFetchDTO.getLocalBranch() != null) {
                RefSpec refSpec = new RefSpec("refs/heads/" + gitFetchDTO.getRemoteBranch() + ":refs/remotes/origin/"
                    + gitFetchDTO.getLocalBranch());
                fetchCommand.setRemote(gitFetchDTO.getRemoteName()).setRefSpecs(refSpec).call();
            } else {
                fetchCommand.call();
            }
        } catch (Exception e) {
            throw new CustomRuntimeException("Git fetch fail", e.getMessage());
        }
    }

    public void pull(String path, GitPullDTO gitPullDTO) {
        try (Git git = new Git(new FileRepository(path + ".git"))) {
            FetchCommand fetchCommand = git.fetch().setTimeout(10);
            if (gitPullDTO.getToken() != null && gitPullDTO.getUsername() != null) {
                String username = gitPullDTO.getUsername();
                String token = gitPullDTO.getToken();
                CredentialsProvider credentialsProvider = new UsernamePasswordCredentialsProvider(username, token);
                fetchCommand.setCredentialsProvider(credentialsProvider);
            }
            if (gitPullDTO.getRemoteBranch() != null && gitPullDTO.getRemoteName() != null
                && gitPullDTO.getLocalBranch() != null) {
                RefSpec refSpec = new RefSpec("refs/heads/" + gitPullDTO.getRemoteBranch() + ":refs/remotes/origin/"
                    + gitPullDTO.getLocalBranch());
                fetchCommand.setRemote(gitPullDTO.getRemoteName()).setRefSpecs(refSpec).call();
            } else {
                fetchCommand.call();
            }
            Ref remoteRef = git.getRepository().findRef("refs/remotes/origin/" + gitPullDTO.getLocalBranch());
            if (remoteRef != null) {
                git.reset().setMode(ResetType.HARD).setRef(remoteRef.getName()).call();
            } else {
                log.warn("远程分支 refs/remotes/origin/" + gitPullDTO.getLocalBranch() + " 未找到");
                throw new CustomRuntimeException(
                    "remote branch  refs/remotes/origin/" + gitPullDTO.getLocalBranch() + " not found");
            }
//            git.pull().setProgressMonitor(new TextProgressMonitor(new PrintWriter(System.out))).call();
//            git.pull().setProgressMonitor(new SimpleProgressMonitor()).call();
        } catch (Exception e) {
            throw new CustomRuntimeException("Git pull fail", e.getMessage());
        }
    }

    public void setRemoteUrl(String path, String remoteName, String remoteUrl) {
        try (Git git = new Git(new FileRepository(path + ".git"))) {
            log.info("setRemoteUrl path: {}, remoteName: {}, remoteUrl: {}", path, remoteName, remoteUrl);
            git.remoteSetUrl().setRemoteName(remoteName).setRemoteUri(new URIish(remoteUrl)).call();
        } catch (Exception e) {
            throw new CustomRuntimeException("Git setRemoteUrl fail", e.getMessage());
        }
    }

    public void addRemoteUrl(String path, String remoteName, String remoteUrl) {
        try (Git git = new Git(new FileRepository(path + "/.git"))) {
            log.info("addRemoteUrl path: {}, remoteName: {}, remoteUrl: {}", path, remoteName, remoteUrl);
            log.info("Git repository path: {}", git.getRepository().getDirectory().getAbsolutePath());

            // 检查远程仓库是否已存在
            StoredConfig config = git.getRepository().getConfig();
            Set<String> remotes = config.getSubsections("remote");
            if (remotes.contains(remoteName)) {
                log.warn("Remote '{}' already exists, updating URL instead", remoteName);
                git.remoteSetUrl().setRemoteName(remoteName).setRemoteUri(new URIish(remoteUrl)).call();
            } else {
                git.remoteAdd().setName(remoteName).setUri(new URIish(remoteUrl)).call();
            }

            // 验证远程仓库是否添加成功
            config = git.getRepository().getConfig();
            String actualUrl = config.getString("remote", remoteName, "url");
            log.info("Remote '{}' URL after operation: {}", remoteName, actualUrl);

            if (!remoteUrl.equals(actualUrl)) {
                throw new CustomRuntimeException("Git addRemoteUrl fail",
                    String.format("Failed to set remote URL. Expected: %s, Actual: %s", remoteUrl, actualUrl));
            }
        } catch (Exception e) {
            log.error("Git addRemoteUrl failed with error: {}", e.getMessage(), e);
            throw new CustomRuntimeException("Git addRemoteUrl fail", e.getMessage());
        }
    }


    public void createAndCheckBranch(String localRepoPath, GitCreateAndCheckBranchDTO gitCreateAndCheckBranchDTO) {
        try {
            // 检查 index.lock 文件
            checkAndWaitForIndexLock(localRepoPath);

            try (Git git = new Git(new FileRepository(localRepoPath + "/.git"))) {
                long beginTime = System.currentTimeMillis();
                Ref localRemoteRef = git.getRepository().findRef(gitCreateAndCheckBranchDTO.getRemoteBranch());
                if (localRemoteRef == null) {
                    git.checkout().setForced(true).setCreateBranch(true).setStartPoint(
                            gitCreateAndCheckBranchDTO.getRemoteName() + "/" + gitCreateAndCheckBranchDTO.getRemoteBranch())
                        .setName(gitCreateAndCheckBranchDTO.getRemoteBranch()).call();
                } else {
                    git.checkout().setForced(true).setStartPoint(
                            gitCreateAndCheckBranchDTO.getRemoteName() + "/" + gitCreateAndCheckBranchDTO.getRemoteBranch())
                        .setName(gitCreateAndCheckBranchDTO.getRemoteBranch()).call();
                }
                Ref newBranchRef = git.getRepository().findRef(gitCreateAndCheckBranchDTO.getNewBranch());
                if (newBranchRef == null) {
                    log.debug("newBranchRef already created");
                    git.branchCreate().setName(gitCreateAndCheckBranchDTO.getNewBranch()).call();
                }
                git.checkout().setForced(true).setName(gitCreateAndCheckBranchDTO.getNewBranch()).call();
                log.info("createAndCheckBranch, localRepoPath: {}, timeCost: {}", localRepoPath,
                    System.currentTimeMillis() - beginTime);
            }
        } catch (CustomRuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new CustomRuntimeException("Git create and check branch fail", e.getMessage());
        }
    }

    /**
     * 检查并 index.lock 文件
     *
     * @param repoPath 仓库路径
     */
    private void checkAndWaitForIndexLock(String repoPath) throws InterruptedException {
        String indexLockPath = repoPath + "/.git/index.lock";
        log.info("checkout checkAndWaitForIndexLock indexLockPath: {}", indexLockPath);

        File indexLockFile = new File(indexLockPath);
        if (!indexLockFile.exists()) {
            log.info("checkout index.lock file not exists");
            return;
        }

        log.info("checkout index.lock file still exists");
        throw new CustomRuntimeException("checkout index.lock file is exists",
            "The git checkout operation is still in progress. Please try again later.");
    }

    public void checkFilesSize(String localRepoPath) {

        try (Git git = new Git(new FileRepository(localRepoPath + "/.git"))) {
            Status status = git.status().call();
            Set<String> untrackedFiles = status.getUntracked();
            log.info("checkFilesSize,path:{}", localRepoPath);
            long totalSize = 0;
            for (String file : untrackedFiles) {
                Path filePath = Paths.get(localRepoPath + file);
                if (Files.exists(filePath)) { // 增加存在性检查
                    totalSize += Files.size(filePath);
                    if (totalSize > Constant.FILE_SIZE_LIMIT_100M) {
                        log.warn("checkFilesSize warn: Untracked files exceed the maximum limit of 100M");
                        throw new CustomRuntimeException("checkFilesSize warn",
                            "Untracked files exceed the maximum limit of 100M");
                    }
                } else {
                    log.warn("file not exists: " + file);
                }
            }
        } catch (Exception e) {
            throw new CustomRuntimeException("checkFilesSize", e.getMessage());
        }
    }

    public void createDefaultBranch(String localRepoPath, String branchName) {
        try (Git git = new Git(new FileRepository(localRepoPath + "/.git"))) {
            // 创建 .gitignore 文件
            File gitignore = new File(localRepoPath, ".gitignore");
            Files.write(gitignore.toPath(), ".DS_Store\n".getBytes(StandardCharsets.UTF_8));

            // 添加 .gitignore 并创建初始提交
            git.add().addFilepattern(".").call();
            git.commit()
                .setMessage("Initial commit with .gitignore")
                .call();

            // 获取当前默认分支
            String currentBranch = git.getRepository().getBranch();
            if (branchName.equals(currentBranch)) {
                log.info("Default branch {} already exists, skipping creation", branchName);
                return;
            }

            // 创建并设置默认分支
            git.branchCreate()
                .setName(branchName)
                .call();

            git.getRepository().updateRef("HEAD", true)
                .link("refs/heads/" + branchName);
        } catch (Exception e) {
            throw new CustomRuntimeException("Failed to create default branch", e.getMessage());
        }
    }
}