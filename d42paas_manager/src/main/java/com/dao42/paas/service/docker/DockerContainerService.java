package com.dao42.paas.service.docker;

import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.repository.docker.DockerRepository;
import com.dao42.paas.service.permission.PermissionService;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import com.vladmihalcea.concurrent.Retry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.StaleObjectStateException;
import org.springframework.beans.BeanUtils;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * DockerContainer业务方法
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DockerContainerService {

    private final DockerRepository dockerRepository;
    private final PermissionService permissionService;

    /**
     * 查找
     *
     * @param dockerId ID
     * @return
     */
    public DockerContainer getById(Long dockerId) {
        DockerContainer dockerContainer = this.dockerRepository
            .findById(dockerId)
            .orElseThrow(() -> new CustomRuntimeException("docker.not.exist", "docker 不存在"));
        permissionService.checkPermission(dockerContainer.getTenantId());
        return dockerContainer;
    }

    /**
     * 查找
     *
     * @param dockerId ID
     * @return
     */
    public List<DockerContainer> getByIds(List<Long> dockerId) {
        Iterable<DockerContainer> dockerContainer = this.dockerRepository
            .findAllById(dockerId);

        if (dockerContainer != null) {
            List<DockerContainer> collect = StreamSupport.stream(dockerContainer.spliterator(), false)
                .collect(Collectors.toList());
            for (DockerContainer container : collect) {
                permissionService.checkPermission(container.getTenantId());
                return collect;
            }
        }
        return null;
    }

    /**
     * 查找
     *
     * @param containerId CONTAINER_ID
     * @return
     */
    public DockerContainer getByContainerId(String containerId) {
        return dockerRepository
            .findByContainerId(containerId)
            .orElseThrow(() -> new CustomRuntimeException("docker.not.exist", "docker 不存在"));
    }

    /**
     * 保存
     *
     * @param docker docker
     * @return 保存后的docker
     */
    @Retry(times = 3, on = {StaleObjectStateException.class})
    public DockerContainer save(DockerContainer docker) {

        if (docker.getId() != null) {
            Optional<DockerContainer> optional = dockerRepository.findById(docker.getId());
            DockerContainer oldDocker = optional.get();
            docker.setVersion(oldDocker.getVersion());
        }

            return dockerRepository.save(docker);

    }

    /**
     * 带有重试机制的更新端口方法
     *
     * @param docker docker
     * @return
     */
    @Retry(times = 10, on = {ObjectOptimisticLockingFailureException.class})
    public DockerContainer updatePorts(DockerContainer docker) {
        DockerContainer newest = dockerRepository.findById(docker.getId())
            .orElseThrow(() -> new CustomRuntimeException("docker.not.exist", "docker 不存在"));
        newest.setHostPort(docker.getHostPort());
        newest.setLspPort(docker.getLspPort());
        newest.setSshPort(docker.getSshPort());
        newest.setProjectWebPort(docker.getProjectWebPort());
        newest.setAgentServerPort(docker.getAgentServerPort());
        newest.setDockerAddress(docker.getDockerAddress());
        return dockerRepository.save(newest);
    }

    /**
     * 更新状态
     *
     * @param docker docker
     * @return
     */
    public void updateStatus(DockerContainer docker) {
        dockerRepository.updateStatusById(docker.getStatus(), docker.getId());
    }

}
