package com.dao42.paas.external.sdk.controller.image;

import com.dao42.paas.dto.image.CacheStatistics;
import com.dao42.paas.dto.image.ImageCacheResponse;
import com.dao42.paas.framework.web.response.ApiResponse;
import com.dao42.paas.model.image.ImageCache;
import com.dao42.paas.service.image.HotImageCacheManager;
import com.dao42.paas.service.image.ImageCacheService;
import com.dao42.paas.repository.image.ImageCacheRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 镜像缓存管理API控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/sdk/images/cache")
@RequiredArgsConstructor
@Tag(name = "镜像缓存管理", description = "镜像缓存查询、清理、统计等API")
public class ImageCacheController {

    private final ImageCacheService imageCacheService;
    private final HotImageCacheManager hotImageCacheManager;
    private final ImageCacheRepository imageCacheRepository;

    @Operation(summary = "获取缓存统计信息", description = "获取镜像缓存的统计信息")
    @GetMapping("/statistics")
    public ApiResponse<CacheStatistics> getCacheStatistics() {
        try {
            CacheStatistics statistics = hotImageCacheManager.getCacheStatistics();
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            return ApiResponse.error("获取缓存统计信息失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取热点镜像列表", description = "获取当前的热点镜像列表")
    @GetMapping("/hot-images")
    public ApiResponse<List<ImageCacheResponse>> getHotImages() {
        try {
            List<ImageCache> hotImages = hotImageCacheManager.identifyHotImages();
            List<ImageCacheResponse> responses = hotImages.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
            return ApiResponse.success(responses);
        } catch (Exception e) {
            log.error("获取热点镜像列表失败", e);
            return ApiResponse.error("获取热点镜像列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "预拉取镜像", description = "将指定镜像预拉取到所有服务器")
    @PostMapping("/preload")
    public ApiResponse<Void> preloadImage(
            @Parameter(description = "镜像标签") @RequestParam String imageTag) {
        try {
            hotImageCacheManager.preloadHotImageToAllServers(imageTag);
            return ApiResponse.success();
        } catch (Exception e) {
            log.error("预拉取镜像失败: imageTag={}", imageTag, e);
            return ApiResponse.error("预拉取镜像失败: " + e.getMessage());
        }
    }

    @Operation(summary = "清理过期缓存", description = "清理过期的镜像缓存")
    @PostMapping("/cleanup/expired")
    public ApiResponse<Void> cleanupExpiredCaches() {
        try {
            hotImageCacheManager.cleanupExpiredCaches();
            return ApiResponse.success();
        } catch (Exception e) {
            log.error("清理过期缓存失败", e);
            return ApiResponse.error("清理过期缓存失败: " + e.getMessage());
        }
    }

    @Operation(summary = "清理低使用率缓存", description = "清理低使用率的镜像缓存")
    @PostMapping("/cleanup/low-usage")
    public ApiResponse<Void> cleanupLowUsageCaches() {
        try {
            hotImageCacheManager.cleanupLowUsageCaches();
            return ApiResponse.success();
        } catch (Exception e) {
            log.error("清理低使用率缓存失败", e);
            return ApiResponse.error("清理低使用率缓存失败: " + e.getMessage());
        }
    }

    @Operation(summary = "优化存储成本", description = "执行存储成本优化")
    @PostMapping("/optimize")
    public ApiResponse<Void> optimizeStorageCost() {
        try {
            hotImageCacheManager.optimizeStorageCost();
            return ApiResponse.success();
        } catch (Exception e) {
            log.error("优化存储成本失败", e);
            return ApiResponse.error("优化存储成本失败: " + e.getMessage());
        }
    }

    @Operation(summary = "强制清理镜像缓存", description = "强制清理指定服务器上的镜像缓存")
    @DeleteMapping("/force-cleanup")
    public ApiResponse<Void> forceCleanupImageCache(
            @Parameter(description = "服务器ID") @RequestParam String serverId,
            @Parameter(description = "镜像标签") @RequestParam String imageTag) {
        try {
            hotImageCacheManager.forceCleanupImageCache(serverId, imageTag);
            return ApiResponse.success();
        } catch (Exception e) {
            log.error("强制清理镜像缓存失败: serverId={}, imageTag={}", serverId, imageTag, e);
            return ApiResponse.error("强制清理镜像缓存失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查镜像缓存", description = "检查指定服务器是否有指定镜像")
    @GetMapping("/check")
    public ApiResponse<Boolean> checkImageCache(
            @Parameter(description = "服务器ID") @RequestParam String serverId,
            @Parameter(description = "镜像标签") @RequestParam String imageTag) {
        try {
            boolean hasImage = imageCacheService.hasImage(serverId, imageTag);
            return ApiResponse.success(hasImage);
        } catch (Exception e) {
            log.error("检查镜像缓存失败: serverId={}, imageTag={}", serverId, imageTag, e);
            return ApiResponse.error("检查镜像缓存失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取镜像评分", description = "获取指定镜像在指定服务器上的评分")
    @GetMapping("/score")
    public ApiResponse<Double> getImageScore(
            @Parameter(description = "服务器ID") @RequestParam String serverId,
            @Parameter(description = "镜像标签") @RequestParam String imageTag) {
        try {
            double score = imageCacheService.getImageScore(serverId, imageTag);
            return ApiResponse.success(score);
        } catch (Exception e) {
            log.error("获取镜像评分失败: serverId={}, imageTag={}", serverId, imageTag, e);
            return ApiResponse.error("获取镜像评分失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取服务器镜像缓存列表", description = "获取指定服务器的镜像缓存列表")
    @GetMapping("/server/{serverId}")
    public ApiResponse<List<ImageCacheResponse>> getServerImageCaches(
            @Parameter(description = "服务器ID") @PathVariable Long serverId) {
        try {
            List<ImageCache> caches = imageCacheRepository.findByDockerServerId(serverId);
            List<ImageCacheResponse> responses = caches.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
            return ApiResponse.success(responses);
        } catch (Exception e) {
            log.error("获取服务器镜像缓存列表失败: serverId={}", serverId, e);
            return ApiResponse.error("获取服务器镜像缓存列表失败: " + e.getMessage());
        }
    }

    /**
     * 转换为响应DTO
     */
    private ImageCacheResponse convertToResponse(ImageCache imageCache) {
        ImageCacheResponse response = new ImageCacheResponse();
        response.setId(imageCache.getId());
        response.setDockerServerId(imageCache.getDockerServerId());
        response.setImageTag(imageCache.getImageTag());
        response.setImageDigest(imageCache.getImageDigest());
        response.setCacheSize(imageCache.getCacheSize());
        response.setStatus(imageCache.getStatus().name());
        response.setHitCount(imageCache.getHitCount());
        response.setLastHitAt(imageCache.getLastHitAt());
        response.setCachedAt(imageCache.getCachedAt());
        response.setUpdatedAt(imageCache.getUpdatedAt());
        return response;
    }
}
