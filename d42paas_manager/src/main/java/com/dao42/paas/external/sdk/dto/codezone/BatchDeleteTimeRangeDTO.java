package com.dao42.paas.external.sdk.dto.codezone;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * BatchDeleteTimeRangeDTO - DTO for batch deleting CodeZones based on time range and expiration days
 */
@Getter
@Setter
@ApiModel(description = "批量删除CodeZone的时间范围和过期时间参数")
public class BatchDeleteTimeRangeDTO {

    @ApiModelProperty(value = "开始时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2023-01-01 00:00:00")
    private String startTime;

    @ApiModelProperty(value = "结束时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2023-12-31 23:59:59")
    private String endTime;

    @ApiModelProperty(value = "过期天数，超过该天数的CodeZone将被删除，不指定时使用系统默认值", example = "30")
    private Integer expireDays;

    @ApiModelProperty(value = "要删除的CodeZone ID列表")
    private List<Long> ids;
}