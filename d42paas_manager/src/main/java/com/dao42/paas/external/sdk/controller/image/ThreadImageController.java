package com.dao42.paas.external.sdk.controller.image;

import com.dao42.paas.dto.image.CacheStatistics;
import com.dao42.paas.dto.image.ThreadImageBuildRequest;
import com.dao42.paas.dto.image.ThreadImageResponse;
import com.dao42.paas.framework.web.response.ApiResponse;
import com.dao42.paas.model.image.ThreadImage;
import com.dao42.paas.service.image.ThreadImageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Thread镜像管理API控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/sdk/images/threads")
@RequiredArgsConstructor
@Tag(name = "Thread镜像管理", description = "Thread镜像构建、推送、查询等API")
public class ThreadImageController {

    private final ThreadImageService threadImageService;

    @Operation(summary = "手动构建Thread镜像", description = "为指定容器手动构建Thread镜像")
    @PostMapping("/build")
    public ApiResponse<ThreadImageResponse> buildThreadImage(
            @Valid @RequestBody ThreadImageBuildRequest request) {
        try {
            ThreadImage threadImage = threadImageService.buildThreadImage(request);
            ThreadImageResponse response = convertToResponse(threadImage);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("构建Thread镜像失败", e);
            return ApiResponse.error("构建Thread镜像失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询Thread镜像列表", description = "分页查询用户的Thread镜像列表")
    @GetMapping
    public ApiResponse<Page<ThreadImageResponse>> getThreadImages(
            @Parameter(description = "项目ID") @RequestParam(required = false) Long projectId,
            @Parameter(description = "Issue ID") @RequestParam(required = false) Long issueId,
            @Parameter(description = "容器类型") @RequestParam(required = false) String containerType,
            @Parameter(description = "状态") @RequestParam(required = false) String status,
            Pageable pageable) {
        try {
            Page<ThreadImage> threadImages = threadImageService.getThreadImages(
                    projectId, issueId, containerType, status, pageable);
            Page<ThreadImageResponse> responses = threadImages.map(this::convertToResponse);
            return ApiResponse.success(responses);
        } catch (Exception e) {
            log.error("查询Thread镜像列表失败", e);
            return ApiResponse.error("查询Thread镜像列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取Thread镜像详情", description = "根据ID获取Thread镜像详细信息")
    @GetMapping("/{id}")
    public ApiResponse<ThreadImageResponse> getThreadImage(
            @Parameter(description = "镜像ID") @PathVariable Long id) {
        try {
            ThreadImage threadImage = threadImageService.getThreadImage(id);
            ThreadImageResponse response = convertToResponse(threadImage);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取Thread镜像详情失败: id={}", id, e);
            return ApiResponse.error("获取Thread镜像详情失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除Thread镜像", description = "删除指定的Thread镜像")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteThreadImage(
            @Parameter(description = "镜像ID") @PathVariable Long id) {
        try {
            threadImageService.deleteThreadImage(id);
            return ApiResponse.success();
        } catch (Exception e) {
            log.error("删除Thread镜像失败: id={}", id, e);
            return ApiResponse.error("删除Thread镜像失败: " + e.getMessage());
        }
    }

    @Operation(summary = "推送Thread镜像", description = "将Thread镜像推送到镜像仓库")
    @PostMapping("/{id}/push")
    public ApiResponse<Void> pushThreadImage(
            @Parameter(description = "镜像ID") @PathVariable Long id) {
        try {
            threadImageService.pushThreadImage(id);
            return ApiResponse.success();
        } catch (Exception e) {
            log.error("推送Thread镜像失败: id={}", id, e);
            return ApiResponse.error("推送Thread镜像失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取最新Thread镜像", description = "获取项目或Issue的最新Thread镜像")
    @GetMapping("/latest")
    public ApiResponse<ThreadImageResponse> getLatestThreadImage(
            @Parameter(description = "项目ID") @RequestParam Long projectId,
            @Parameter(description = "Issue ID") @RequestParam(required = false) Long issueId) {
        try {
            ThreadImage threadImage = threadImageService.getLatestThreadImage(projectId, issueId);
            if (threadImage == null) {
                return ApiResponse.error("未找到最新的Thread镜像");
            }
            ThreadImageResponse response = convertToResponse(threadImage);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取最新Thread镜像失败: projectId={}, issueId={}", projectId, issueId, e);
            return ApiResponse.error("获取最新Thread镜像失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取镜像构建日志", description = "获取Thread镜像的构建日志")
    @GetMapping("/{id}/build-log")
    public ApiResponse<String> getBuildLog(
            @Parameter(description = "镜像ID") @PathVariable Long id) {
        try {
            String buildLog = threadImageService.getBuildLog(id);
            return ApiResponse.success(buildLog);
        } catch (Exception e) {
            log.error("获取构建日志失败: id={}", id, e);
            return ApiResponse.error("获取构建日志失败: " + e.getMessage());
        }
    }

    @Operation(summary = "重新构建Thread镜像", description = "重新构建失败的Thread镜像")
    @PostMapping("/{id}/rebuild")
    public ApiResponse<ThreadImageResponse> rebuildThreadImage(
            @Parameter(description = "镜像ID") @PathVariable Long id) {
        try {
            ThreadImage threadImage = threadImageService.rebuildThreadImage(id);
            ThreadImageResponse response = convertToResponse(threadImage);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("重新构建Thread镜像失败: id={}", id, e);
            return ApiResponse.error("重新构建Thread镜像失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取用户镜像统计", description = "获取当前用户的镜像使用统计")
    @GetMapping("/statistics")
    public ApiResponse<CacheStatistics> getImageStatistics() {
        try {
            CacheStatistics statistics = threadImageService.getUserImageStatistics();
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取用户镜像统计失败", e);
            return ApiResponse.error("获取用户镜像统计失败: " + e.getMessage());
        }
    }

    /**
     * 转换为响应DTO
     */
    private ThreadImageResponse convertToResponse(ThreadImage threadImage) {
        ThreadImageResponse response = new ThreadImageResponse();
        response.setId(threadImage.getId());
        response.setProjectId(threadImage.getProjectId());
        response.setIssueId(threadImage.getIssueId());
        response.setContainerType(threadImage.getContainerType().name());
        response.setImageTag(threadImage.getImageTag());
        response.setImageDigest(threadImage.getImageDigest());
        response.setImageSize(threadImage.getImageSize());
        response.setParentImageTag(threadImage.getParentImageTag());
        response.setUserId(threadImage.getUserId());
        response.setTenantId(threadImage.getTenantId());
        response.setStatus(threadImage.getStatus().name());
        response.setUsageCount(threadImage.getUsageCount());
        response.setLastUsedAt(threadImage.getLastUsedAt());
        response.setCreatedAt(threadImage.getCreatedAt());
        response.setUpdatedAt(threadImage.getUpdatedAt());
        return response;
    }
}
