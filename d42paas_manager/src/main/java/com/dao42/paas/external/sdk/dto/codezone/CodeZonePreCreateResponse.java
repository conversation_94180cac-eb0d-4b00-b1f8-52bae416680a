package com.dao42.paas.external.sdk.dto.codezone;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CodeZone pre-create response DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CodeZonePreCreateResponse {

    /**
     * CodeZone ID
     */
    @ApiModelProperty(value = "CodeZone ID")
    @JsonProperty("codezone_id")
    private String codeZoneId;

    /**
     * Playground ID
     */
    @ApiModelProperty(value = "Playground ID")
    @JsonProperty("playground_id")
    private String playgroundId;
}