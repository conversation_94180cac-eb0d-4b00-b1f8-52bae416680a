package com.dao42.paas.external.sdk.controller.codezone;

import com.dao42.paas.bean.CurrentUserBean;
import com.dao42.paas.common.enums.PlaygroundStatus;
import com.dao42.paas.enums.PlaygroundBindType;
import com.dao42.paas.external.sdk.convertor.CodeZoneConvertor;
import com.dao42.paas.external.sdk.dto.ForkCodeZoneDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneCreateDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneFileEditDto;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneGithubImportByUrlDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneGithubImportDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneImagesDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneImportDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZonePreCreateRequest;
import com.dao42.paas.external.sdk.dto.codezone.CodeZonePreCreateResponse;
import com.dao42.paas.external.sdk.dto.codezone.CodeZonePushDTO;
import com.dao42.paas.external.sdk.dto.git.GitSetRemoteUrlDTO;
import com.dao42.paas.framework.currentUser.CurrentUser;
import com.dao42.paas.framework.dto.result.CloneAsyncResultDTO;
import com.dao42.paas.framework.dto.result.ListResultDTO;
import com.dao42.paas.framework.dto.result.ResultDTO;
import com.dao42.paas.framework.dto.result.ResultIdDTO;
import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.codezone.CodeZone;
import com.dao42.paas.model.tenant.Tenant;
import com.dao42.paas.repository.PlaygroundRepository;
import com.dao42.paas.repository.codezone.CodeZoneRepository;
import com.dao42.paas.service.RedisExternalService;
import com.dao42.paas.service.TenantService;
import com.dao42.paas.service.codezone.CodeZoneService;
import com.dao42.paas.service.codezone.GitService;
import com.dao42.paas.service.playground.PlaygroundService;
import com.dao42.paas.utils.JsonUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;
import com.dao42.paas.external.sdk.dto.codezone.BatchDeleteTimeRangeDTO;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/sdk/codeZones")
@Slf4j
@Api(tags = "codeZone")
@ApiSort(value = 3)
public class CodeZoneController {

    private static final String LOCK_REDIS_CREATE_PLAYGROUND = "codeZone:createPlayground:";

    private final RedisExternalService redisExternalService;

    private final TenantService tenantService;

    private final CodeZoneRepository codeZoneRepository;

    private final CodeZoneConvertor codeZoneConvertor;

    private final CodeZoneService codeZoneService;

    private final PlaygroundRepository playgroundRepository;

    private final PlaygroundService playgroundService;

    private final GitService gitService;

    @GetMapping
    @ApiOperation(value = "获取codeZone列表")
    @ApiOperationSupport(order = 1)
    public ListResultDTO<CodeZoneDTO> getList(@CurrentUser @ApiIgnore CurrentUserBean tenant) {
        List<CodeZone> codeZones = this.codeZoneRepository.findAllByTenantId(tenant.getTenantId());
        return this.codeZoneConvertor.toResultDTO(codeZones);
    }

    @PostMapping
    @ApiOperation(value = "创建codeZone")
    @ApiOperationSupport(order = 2)
    public ResultDTO<ResultIdDTO> create(@Validated @RequestBody CodeZoneCreateDTO codeZoneDTO,
        @CurrentUser @ApiIgnore CurrentUserBean tenant) {
        CodeZone codeZone = this.codeZoneService.create(tenant, codeZoneDTO.getEnvironmentVerId(),
            codeZoneDTO.getUnitTestFrameworkId(), codeZoneDTO.getPurpose());
        ResultIdDTO resultIdDTO = new ResultIdDTO();
        resultIdDTO.setId(String.valueOf(codeZone.getId()));
        return ResultDTO.success(resultIdDTO);
    }

    @PostMapping("/preCreate")
    @ApiOperation(value = "预创建CodeZone，包括创建Playground和初始化Git仓库")
    @ApiOperationSupport(order = 2)
    @Transactional
    public ResultDTO<CodeZonePreCreateResponse> preCreate(
        @Validated @RequestBody CodeZonePreCreateRequest request,
        @CurrentUser @ApiIgnore CurrentUserBean tenant) {

        CodeZone codeZone = this.codeZoneService.create(tenant, request.getEnvironmentVerId(),
            request.getUnitTestFrameworkId(), request.getPurpose());

        String redisLockKey = LOCK_REDIS_CREATE_PLAYGROUND + codeZone.getId();
        if (!redisExternalService.getLock(redisLockKey, 3)) {
            log.error("failed to get redisLockKey : {}", redisLockKey);
            throw new CustomRuntimeException("Request too fast, please try later !",
                " Request too fast, please try later !");
        }
        Playground playground = playgroundService.create(tenant.getTenantId(), PlaygroundBindType.CODE_ZONE, null);
        playgroundService.bindCodeZone(playground, codeZone);

        CodeZonePreCreateResponse response = this.codeZoneService.preCreate(codeZone, playground);
        return ResultDTO.success(response);
    }


    @PostMapping("/github")
    @ApiOperation(value = "从github导入创建codeZone")
    @ApiOperationSupport(order = 2)
    public ResultDTO<ResultIdDTO> createFromGithub(
        @Validated @RequestBody CodeZoneGithubImportDTO codeZoneGithubImportDTO,
        @CurrentUser @ApiIgnore CurrentUserBean tenant) {
        String codeZoneId = this.codeZoneService.createFromGithub(tenant, codeZoneGithubImportDTO);
        ResultIdDTO resultIdDTO = new ResultIdDTO();
        resultIdDTO.setId(codeZoneId);
        return ResultDTO.success(resultIdDTO);
    }

    @PostMapping("/github/async")
    @ApiOperation(value = "从github导入创建codeZone")
    @ApiOperationSupport(order = 2)
    public ResultDTO<CloneAsyncResultDTO> createFromGithubAsync(
        @Validated @RequestBody CodeZoneGithubImportDTO codeZoneGithubImportDTO,
        @CurrentUser @ApiIgnore CurrentUserBean tenant) {
        Map<String, String> resultMap = this.codeZoneService.createFromGithubAsync(tenant, codeZoneGithubImportDTO);
        CloneAsyncResultDTO resultIdDTO = CloneAsyncResultDTO.assignment(resultMap.get("codeZoneId"),
            resultMap.get("taskId"));
        return ResultDTO.success(resultIdDTO);
    }

    @PostMapping("/asyncClone")
    @ApiOperation(value = "从 git 导入创建codeZone")
    @ApiOperationSupport(order = 2)
    public ResultDTO<CloneAsyncResultDTO> cloneAsync(
        @Validated @RequestBody CodeZoneGithubImportByUrlDTO codeZoneGithubImportDTO,
        @CurrentUser @ApiIgnore CurrentUserBean tenant) {
        Map<String, String> resultMap = this.codeZoneService.asyncClone(tenant, codeZoneGithubImportDTO);
        CloneAsyncResultDTO resultIdDTO = CloneAsyncResultDTO.assignment(resultMap.get("codeZoneId"),
            resultMap.get("taskId"));
        return ResultDTO.success(resultIdDTO);
    }

    @PostMapping("/{id}/push")
    @ApiOperation(value = "push")
    @ApiOperationSupport(order = 2)
    public ResultDTO<ResultIdDTO> push(@PathVariable Long id, @RequestBody CodeZonePushDTO codeZonePushDTO,
        @CurrentUser @ApiIgnore CurrentUserBean tenant) {
        CodeZone codeZone = this.codeZoneService.getCodeZone(id);
        String commitId = this.codeZoneService.push(codeZonePushDTO, codeZone.getRootPath());
        ResultIdDTO resultIdDTO = new ResultIdDTO();
        resultIdDTO.setId(commitId);
        return ResultDTO.success(resultIdDTO);
    }

    @PostMapping("/fork")
    @ApiOperation(value = "forkCodeZone")
    @ApiOperationSupport(order = 2)
    public ResultDTO<CodeZoneDTO> fork(@Validated @RequestBody ForkCodeZoneDTO dto,
        @CurrentUser @ApiIgnore CurrentUserBean tenant) {
        log.info("forkCodeZone, codeZoneId: {}, withMiddlewareData: {}", dto.getCodeZoneId(),
            dto.isWithMiddlewareData());
        CodeZone codeZoneOld = this.codeZoneService.getCodeZone(Long.valueOf(dto.getCodeZoneId()));
        CodeZone codeZone = this.codeZoneService.fork(codeZoneOld, tenant.getUserId(), dto);
        return this.codeZoneConvertor.toResultDTO(codeZone);
    }

    @PutMapping(value = "/{id}")
    @ApiOperation(value = "编辑codeZone")
    @ApiOperationSupport(order = 3)
    public ResultDTO<CodeZoneDTO> update(@PathVariable Long id, @Validated @RequestBody CodeZoneCreateDTO codeZoneDTO) {
        CodeZone codeZone = this.codeZoneService.getCodeZone(id);
        codeZone = this.codeZoneService.update(codeZone, codeZoneDTO.getEnvironmentVerId(),
            codeZoneDTO.getUnitTestFrameworkId(), codeZoneDTO.getCpu(), codeZoneDTO.getMemory(),
            codeZoneDTO.getPurpose());
        return this.codeZoneConvertor.toResultDTO(codeZone);
    }

    @PutMapping(value = "/{id}/upgrade")
    @ApiOperation(value = "升级codeZone")
    @ApiOperationSupport(order = 3)
    // @Transactional
    public ResultDTO<CodeZoneDTO> upgrade(@PathVariable Long id, @Validated @RequestBody CodeZoneCreateDTO codeZoneDTO)
        throws Exception {
        if ((codeZoneDTO.getEnvironmentVerId() == null || codeZoneDTO.getEnvironmentVerId().isEmpty())
            && (codeZoneDTO.getEnvironmentVerCode() == null || codeZoneDTO.getEnvironmentVerCode().isEmpty())) {
            throw new Exception("EnvironmentId or EnvironmentCode is empty");
        }

        CodeZone codeZone = this.codeZoneService.getCodeZone(id);
        codeZone = this.codeZoneService.upgrade(codeZone, codeZoneDTO);
        playgroundService.notifyPlaygroundInfoByCodeZone(codeZone);
        if (codeZoneDTO.getBranchName() != null && !codeZoneDTO.getBranchName().isEmpty()) {
            this.gitService.createDefaultBranch(codeZone.getRootPath(), codeZoneDTO.getBranchName());
        }
        return this.codeZoneConvertor.toResultDTO(codeZone);
    }


    /**
     * 给指定CodeZone设置环境变量
     *
     * @param codeZoneId  codeZone ID
     * @param envMap      环境变量map
     * @param currentUser 当前用户
     * @return 修改后的文件内容预览
     */
    @PutMapping(value = "/{codeZoneId}/config/environments")
    @ApiOperation(value = "设置环境变量,写入config配置文件中")
    @ApiOperationSupport(order = 3)
    public ResultDTO<String> setEnvs(@PathVariable Long codeZoneId,
        @Validated @RequestBody HashMap<String, String> envMap, @CurrentUser @ApiIgnore CurrentUserBean currentUser) {
        Tenant tenant = tenantService.get(currentUser.getTenantId());
        CodeZone codeZone = codeZoneService.getCodeZone(codeZoneId);
        return ResultDTO.success(codeZoneService.setEnvs(tenant, codeZone, envMap));
    }

    @PutMapping(value = "/{codeZoneId}/environments")
    @ApiOperation(value = "设置环境变量,绑定codeZone")
    @ApiOperationSupport(order = 3)
    public ResultDTO<Void> setEnvs(@PathVariable Long codeZoneId,
        @Validated @RequestBody HashMap<String, String> envMap) {
        CodeZone codeZone = codeZoneService.getCodeZone(codeZoneId);
        codeZoneService.setCodeZoneEnvs(codeZone, envMap);
        return ResultDTO.success();
    }

    @GetMapping(value = "/{codeZoneId}/environments")
    @ApiOperation(value = "获取codeZone绑定的环境变量")
    @ApiOperationSupport(order = 3)
    public ResultDTO<Map<String, String>> getEnvs(@PathVariable Long codeZoneId) {
        CodeZone codeZone = codeZoneService.getCodeZone(codeZoneId);
        return ResultDTO.success(codeZone.getEnv());
    }

    @GetMapping(value = "/{id}/playground")
    @ApiOperation(value = "获取编辑CodeZone的Playground")
    @ApiOperationSupport(order = 4)
    public ResultDTO<ResultIdDTO> getPlayground(@CurrentUser @ApiIgnore CurrentUserBean tenant, @PathVariable Long id,
        @RequestParam(required = false) String env) {
        CodeZone codeZone = this.codeZoneService.getCodeZone(id);
        Playground playground = playgroundRepository.findByBindTypeAndBindObject(PlaygroundBindType.CODE_ZONE, id);
        HashMap<String, String> envMap = JsonUtil.jsonToHashMap(env);
        String redisLockKey = LOCK_REDIS_CREATE_PLAYGROUND + id;
        if (playground == null && !redisExternalService.getLock(redisLockKey, 3)) {
            log.error("failed to get redisLockKey : {}", redisLockKey);
            throw new CustomRuntimeException("Request too fast, please try later !",
                " Request too fast, please try later !");
        }
        if (playground != null) {
            if (envMap != null) {
                playground.setEnv(envMap);
            }
            if (PlaygroundStatus.EMPTY != playground.getStatus()) {
                // 存在已绑定好codeZone的playground时，直接返回
                playgroundRepository.save(playground);
                return ResultDTO.success(ResultIdDTO.assignment(playground.getId()));
            }
        } else {
            playground = playgroundService.create(tenant.getTenantId(), PlaygroundBindType.CODE_ZONE, envMap);
        }
        playgroundService.bindCodeZone(playground, codeZone);
        return ResultDTO.success(ResultIdDTO.assignment(playground.getId()));
    }

    @GetMapping(value = "/{id}/duplicate/playground")
    @ApiOperation(value = "获取编辑CodeZone的Playground(副本模式)", notes = "副本模式代码不持久")
    @ApiOperationSupport(order = 5)
    public ResultDTO<ResultIdDTO> getDuplicatePlayground(@CurrentUser @ApiIgnore CurrentUserBean tenant,
        @PathVariable Long id, @RequestParam(required = false) String env) {
        CodeZone codeZone = this.codeZoneService.getCodeZone(id);
        Playground playground = playgroundService.create(tenant.getTenantId(), PlaygroundBindType.CODE_ZONE_DUPLICATE,
            JsonUtil.jsonToHashMap(env));
        playgroundService.bindDuplicate(playground, codeZone);
        return ResultDTO.success(ResultIdDTO.assignment(playground.getId()));
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入 CodeZone")
    @ApiOperationSupport(order = 6)
    public ResultDTO<Void> importCodeZone(@Validated @RequestBody CodeZoneImportDTO dto,
        @CurrentUser @ApiIgnore CurrentUserBean tenant) {
        this.codeZoneService.importCodeZone(dto, tenant);
        return ResultDTO.success();
    }

    @PostMapping("/{id}/importFiles")
    @ApiOperation(value = "导入文件", notes = "单文件最大20M,压缩包最大100M")
    @ApiOperationSupport(order = 7)
    public ResultDTO<Void> importFiles(@PathVariable Long id, @RequestParam MultipartFile file,
        @CurrentUser @ApiIgnore CurrentUserBean tenant) {
        CodeZone codeZone = this.codeZoneService.getCodeZone(id);
        this.codeZoneService.importCodeZoneFile(file, codeZone);
        return ResultDTO.success();
    }

    @GetMapping("/{id}/downloadAllFiles")
    @ApiOperation(value = "下载代码文件", notes = "不包含 node_modules 等依赖文件")
    @ApiOperationSupport(order = 8)
    public void download(@PathVariable final Long id, final HttpServletRequest request,
        final HttpServletResponse response) {
        CodeZone codeZone = this.codeZoneService.getCodeZone(id);
        this.codeZoneService.download(codeZone, request, response);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除codeZone")
    @ApiOperationSupport(order = 9)
    public ResultDTO<Void> delete(@PathVariable final Long id) {
        CodeZone codeZone = this.codeZoneService.getCodeZone(id);
        Playground playground = this.playgroundRepository.findByBindTypeAndBindObject(PlaygroundBindType.CODE_ZONE,
            codeZone.getId());
        if (playground != null) {
            this.playgroundService.pause(playground, "codezone");
        }
        this.codeZoneService.delete(codeZone);
        return ResultDTO.success();
    }

    @DeleteMapping("/batch/purposeFiltered")
    @ApiOperation(value = "批量删除指定purpose的codeZones")
    @ApiOperationSupport(order = 10)
    public ResultDTO<Map<String, List<Long>>> batchDeleteByPurpose(
        @RequestBody BatchDeleteTimeRangeDTO dto,
        @RequestParam(defaultValue = "2") String purpose) {

        Integer expireDays = dto.getExpireDays();
        List<Long> ids = dto.getIds();

        // Call the service method to perform the deletion
        Map<String, List<Long>> result = this.codeZoneService.deleteByIdsWithPurpose(expireDays, ids, purpose, true);

        // Log the operation result
        log.info(
            "Batch delete request for CodeZones with purpose '{}' (Rate limit enabled). IDs provided: {}. Successfully deleted: {}. Skipped: {}.",
            purpose, ids, result.get("deletedIds"), result.get("skippedIds"));

        for (Long deletedId : result.get("deletedIds")) {
            Playground playground = this.playgroundRepository.findByBindTypeAndBindObject(PlaygroundBindType.CODE_ZONE,
                deletedId);
            if (playground != null) {
                this.playgroundService.pause(playground, "codezone");
            }
        }

        return ResultDTO.success(result);
    }

    @DeleteMapping("/batch/timeRangeFiltered")
    @ApiOperation(value = "根据时间范围,idlist和过期时间批量删除CodeZone")
    @ApiOperationSupport(order = 11)
    public ResultDTO<Map<String, List<Long>>> batchDeleteByTimeRange(
        @RequestBody BatchDeleteTimeRangeDTO dto,
        @RequestParam(defaultValue = "2") String purpose) {

        String startTime = dto.getStartTime();
        String endTime = dto.getEndTime();
        Integer expireDays = dto.getExpireDays();
        List<Long> ids = dto.getIds();

        Map<String, List<Long>> result = this.codeZoneService.batchDeleteByTimeRange(startTime, endTime, expireDays,
            ids, purpose, true);

        log.info(
            "Batch delete by time range request (Rate limit enabled). StartTime: '{}', EndTime: '{}', ExpireDays: {}, Purpose: '{}', IDs provided: {}. Successfully deleted: {}. Skipped: {}.",
            startTime, endTime, expireDays, purpose, ids, result.get("deletedIds"), result.get("skippedIds"));

        for (Long deletedId : result.get("deletedIds")) {
            Playground playground = this.playgroundRepository.findByBindTypeAndBindObject(PlaygroundBindType.CODE_ZONE,
                deletedId);
            if (playground != null) {
                this.playgroundService.pause(playground, "codezone_batch_delete_time_range");
            }
        }

        return ResultDTO.success(result);
    }

    @DeleteMapping("/deleteAll/timeRangeFiltered")
    @ApiOperation(value = "根据时间范围和过期时间批量删除CodeZone")
    @ApiOperationSupport(order = 12)
    public ResultDTO<Map<String, List<Long>>> batchDeleteAllByTimeRange(
        @RequestBody BatchDeleteTimeRangeDTO dto,
        @RequestParam(defaultValue = "2") String purpose) {

        String startTime = dto.getStartTime();
        String endTime = dto.getEndTime();
        Integer expireDays = dto.getExpireDays();

        Map<String, List<Long>> result = this.codeZoneService.batchDeleteAllByTimeRange(startTime, endTime, expireDays,
            purpose, true);

        log.info(
            "Batch delete by time range request (Rate limit enabled). StartTime: '{}', EndTime: '{}', ExpireDays: {}, Purpose: '{}'. Successfully deleted: {}. Skipped: {}.",
            startTime, endTime, expireDays, purpose, result.get("deletedIds"), result.get("skippedIds"));

        for (Long deletedId : result.get("deletedIds")) {
            Playground playground = this.playgroundRepository.findByBindTypeAndBindObject(PlaygroundBindType.CODE_ZONE,
                deletedId);
            if (playground != null) {
                this.playgroundService.pause(playground, "codezone_batch_delete_time_range");
            }
        }

        return ResultDTO.success(result);
    }

    @PutMapping("/{id}/file")
    @ApiOperation(value = "编辑文件")
    @ApiOperationSupport(order = 7)
    public ResultDTO<Void> fileEdit(@PathVariable Long id, @CurrentUser @ApiIgnore CurrentUserBean tenant,
        @RequestBody @Validated CodeZoneFileEditDto codeZoneFileEditDto) {
        this.codeZoneService.fileEdit(id, tenant, codeZoneFileEditDto);
        return ResultDTO.success();
    }

    @GetMapping("/github/{taskId}")
    public ResultDTO<Map<String, String>> getGithubImportProgress(@PathVariable String taskId) {
        Map<String, String> progress = codeZoneService.getGithubImportProgress(taskId);
        return ResultDTO.success(progress);
    }

    @PostMapping("/{id}/setImage")
    @ApiOperation(value = "设置codeZone的镜像")
    @ApiOperationSupport(order = 10)
    public ResultDTO<Void> setImage(@PathVariable Long id, @Validated @RequestBody CodeZoneImagesDTO dto) {
        CodeZone codeZone = this.codeZoneService.getCodeZone(id);
        this.codeZoneService.setImage(dto.getImageCode(), codeZone);
        return ResultDTO.success();
    }

    @PostMapping(value = "/{id}/upgradeGitRemoteAddress")
    @ApiOperation(value = "Update Git remote repository URL")
    @ApiOperationSupport(order = 13)
    public ResultDTO<Void> upgradeGitRemoteAddress(
        @PathVariable Long id,
        @Validated @RequestBody GitSetRemoteUrlDTO gitSetRemoteUrlDTO,
        @CurrentUser @ApiIgnore CurrentUserBean currentUserBean) {

        CodeZone codeZone = this.codeZoneService.getCodeZone(id);
        log.info("Add Git remote repository URL: {}, name: {}, codeZoneID: {}, ", gitSetRemoteUrlDTO.getRemoteUrl(),
            gitSetRemoteUrlDTO.getRemoteName(), codeZone.getId());
        this.gitService.addRemoteUrl(codeZone.getRootPath(), gitSetRemoteUrlDTO, currentUserBean);
        this.codeZoneService.upgradeSSHFile(currentUserBean, codeZone, gitSetRemoteUrlDTO);
        return ResultDTO.success();
    }
}