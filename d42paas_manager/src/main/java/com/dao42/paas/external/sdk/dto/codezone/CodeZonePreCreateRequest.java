package com.dao42.paas.external.sdk.dto.codezone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * CodeZone pre-create request DTO
 */
@Data
public class CodeZonePreCreateRequest {

    /**
     * 环境版本编码
     */
    @ApiModelProperty("环境版本编码")
    private String environmentVerId;


    /**
     * 测试框架Id
     */
    @ApiModelProperty("测试框架Id")
    private String unitTestFrameworkId;

    /**
     * 使用目的
     */
    @ApiModelProperty(value = "mark it as template with 1, draft with 2. of course, you can describe it with detail info")
    private String purpose;

}