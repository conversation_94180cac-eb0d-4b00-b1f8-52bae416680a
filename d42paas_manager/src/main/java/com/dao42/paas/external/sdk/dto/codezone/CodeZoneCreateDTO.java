package com.dao42.paas.external.sdk.dto.codezone;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * CodeZoneDTO
 * <AUTHOR>
 */
@Getter
@Setter
public class CodeZoneCreateDTO {

//    @ApiModelProperty(value = "环境版本Id",required = true)
//    @NotBlank
    @ApiModelProperty("环境编码")
    private String environmentVerId;

    @ApiModelProperty("环境自定义编号")
    private String environmentVerCode;

    @ApiModelProperty("测试框架Id")
    private String unitTestFrameworkId;

    @ApiModelProperty(value = "mark it as template with 1,draft with 2 . of course ,you can describe it with detail info")
    private String purpose;

    @ApiModelProperty(value = "cpu")
    private int cpu;

    @ApiModelProperty(value = "memory")
    private int memory;

    @ApiModelProperty(value = "branch name")
    private String branchName;
}
