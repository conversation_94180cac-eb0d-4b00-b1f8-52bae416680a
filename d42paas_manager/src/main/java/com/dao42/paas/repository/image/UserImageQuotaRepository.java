package com.dao42.paas.repository.image;

import com.dao42.paas.enums.UserTier;
import com.dao42.paas.model.image.UserImageQuota;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户镜像配额Repository
 *
 * <AUTHOR>
 */
@Repository
public interface UserImageQuotaRepository extends JpaRepository<UserImageQuota, Long> {

    /**
     * 根据用户ID查找配额
     */
    Optional<UserImageQuota> findByUserId(Long userId);

    /**
     * 根据租户ID查找配额列表
     */
    List<UserImageQuota> findByTenantId(Long tenantId);

    /**
     * 根据用户级别查找配额列表
     */
    List<UserImageQuota> findByUserTier(UserTier userTier);

    /**
     * 查找超出镜像数量限制的用户
     */
    @Query("SELECT uiq FROM UserImageQuota uiq WHERE uiq.maxImages != -1 AND uiq.currentImages >= uiq.maxImages")
    List<UserImageQuota> findUsersExceedingImageLimit();

    /**
     * 查找超出存储空间限制的用户
     */
    @Query("SELECT uiq FROM UserImageQuota uiq WHERE uiq.maxStorageGb != -1 AND uiq.currentStorageGb >= uiq.maxStorageGb")
    List<UserImageQuota> findUsersExceedingStorageLimit();

    /**
     * 查找需要重置配额的用户
     */
    @Query("SELECT uiq FROM UserImageQuota uiq WHERE uiq.quotaResetAt IS NOT NULL AND uiq.quotaResetAt <= :now")
    List<UserImageQuota> findUsersNeedingQuotaReset(@Param("now") LocalDateTime now);

    /**
     * 统计指定级别的用户数量
     */
    int countByUserTier(UserTier userTier);

    /**
     * 查找配额使用率最高的用户
     */
    @Query("SELECT uiq FROM UserImageQuota uiq WHERE uiq.maxImages > 0 ORDER BY (uiq.currentImages * 1.0 / uiq.maxImages) DESC")
    List<UserImageQuota> findUsersWithHighestImageUsageRate();

    /**
     * 查找存储使用率最高的用户
     */
    @Query("SELECT uiq FROM UserImageQuota uiq WHERE uiq.maxStorageGb > 0 ORDER BY (uiq.currentStorageGb / uiq.maxStorageGb) DESC")
    List<UserImageQuota> findUsersWithHighestStorageUsageRate();
}
