package com.dao42.paas.repository.codezone;

import com.dao42.paas.model.codezone.CodeZone;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;

import java.util.Date;
import java.util.List;

public interface CodeZoneRepository extends CrudRepository<CodeZone, Long> , JpaSpecificationExecutor<CodeZone> {


    List<CodeZone> findAllByTenantId(Long tenantId);

    long countByCreatedDateBetween(Date start, Date end);

    long countByCreatedDateBefore(Date end);

    List<CodeZone> findAllByLastModifiedDateBetweenAndPurpose(Date startDate, Date endDate, String purpose);

}