package com.dao42.paas.repository.meta;

import com.dao42.paas.enums.MetaEnvironmentStatus;
import com.dao42.paas.model.meta.MetaEnvironment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * @meta环境Repository
 *
 * <AUTHOR>
 */
@Repository
public interface MetaEnvironmentRepository extends JpaRepository<MetaEnvironment, Long> {

    /**
     * 查找默认环境
     */
    Optional<MetaEnvironment> findByIsDefaultTrue();

    /**
     * 根据名称查找环境
     */
    Optional<MetaEnvironment> findByName(String name);

    /**
     * 根据名称和版本查找环境
     */
    Optional<MetaEnvironment> findByNameAndVersion(String name, String version);

    /**
     * 查找指定状态的环境
     */
    List<MetaEnvironment> findByStatus(MetaEnvironmentStatus status);

    /**
     * 查找可用的环境
     */
    List<MetaEnvironment> findByStatusOrderByCreatedAtDesc(MetaEnvironmentStatus status);

    /**
     * 查找需要更新的环境
     */
    @Query("SELECT me FROM MetaEnvironment me WHERE me.lastSyncedAt < :cutoffTime OR me.lastSyncedAt IS NULL")
    List<MetaEnvironment> findEnvironmentsNeedingUpdate(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找最新版本的环境
     */
    @Query("SELECT me FROM MetaEnvironment me WHERE me.name = :name ORDER BY me.version DESC")
    List<MetaEnvironment> findLatestVersionsByName(@Param("name") String name);

    /**
     * 统计指定状态的环境数量
     */
    int countByStatus(MetaEnvironmentStatus status);
}
