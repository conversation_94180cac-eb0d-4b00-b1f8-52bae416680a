package com.dao42.paas.repository.image;

import com.dao42.paas.enums.ContainerType;
import com.dao42.paas.model.image.ThreadImage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Thread镜像Repository
 *
 * <AUTHOR>
 */
@Repository
public interface ThreadImageRepository extends JpaRepository<ThreadImage, Long> {

    /**
     * 查找最新的Issue Thread镜像
     */
    @Query("SELECT t.imageTag FROM ThreadImage t WHERE t.projectId = :projectId AND t.issueId = :issueId " +
           "AND t.containerType = 'ISSUE_THREAD' AND t.status = 'AVAILABLE' " +
           "ORDER BY t.createdAt DESC")
    Optional<String> findLatestIssueThreadImage(@Param("projectId") Long projectId, @Param("issueId") Long issueId);

    /**
     * 查找最新的Root Thread镜像
     */
    @Query("SELECT t.imageTag FROM ThreadImage t WHERE t.projectId = :projectId " +
           "AND t.containerType = 'ROOT_THREAD' AND t.status = 'AVAILABLE' " +
           "ORDER BY t.createdAt DESC")
    Optional<String> findLatestRootThreadImage(@Param("projectId") Long projectId);

    /**
     * 查找不活跃的镜像
     */
    @Query("SELECT t FROM ThreadImage t WHERE t.lastUsedAt < :cutoffTime OR t.lastUsedAt IS NULL")
    List<ThreadImage> findInactiveImages(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 按用户ID查找镜像，按创建时间倒序
     */
    List<ThreadImage> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 统计用户的镜像数量
     */
    int countByUserId(Long userId);

    /**
     * 查找特定项目和Issue的镜像
     */
    Optional<ThreadImage> findByProjectIdAndIssueIdAndContainerType(
            Long projectId, Long issueId, ContainerType containerType);

    /**
     * 查找特定项目的Root Thread镜像
     */
    Optional<ThreadImage> findByProjectIdAndContainerType(Long projectId, ContainerType containerType);

    /**
     * 查找用户在特定时间范围内的镜像
     */
    @Query("SELECT t FROM ThreadImage t WHERE t.userId = :userId " +
           "AND t.createdAt BETWEEN :startTime AND :endTime")
    List<ThreadImage> findByUserIdAndCreatedAtBetween(
            @Param("userId") Long userId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 查找特定状态的镜像
     */
    List<ThreadImage> findByStatus(com.dao42.paas.enums.ThreadImageStatus status);

    /**
     * 更新镜像使用统计
     */
    @Query("UPDATE ThreadImage t SET t.usageCount = t.usageCount + 1, t.lastUsedAt = :usedAt " +
           "WHERE t.id = :id")
    void updateUsageStats(@Param("id") Long id, @Param("usedAt") LocalDateTime usedAt);
}
