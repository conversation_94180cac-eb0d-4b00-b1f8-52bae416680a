package com.dao42.paas.repository.image;

import com.dao42.paas.enums.ImageCacheStatus;
import com.dao42.paas.model.image.ImageCache;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 镜像缓存Repository
 *
 * <AUTHOR>
 */
@Repository
public interface ImageCacheRepository extends JpaRepository<ImageCache, Long> {

    /**
     * 根据服务器ID和镜像标签查找缓存
     */
    Optional<ImageCache> findByDockerServerIdAndImageTag(Long dockerServerId, String imageTag);

    /**
     * 查找指定服务器上的所有镜像缓存
     */
    List<ImageCache> findByDockerServerId(Long dockerServerId);

    /**
     * 查找指定状态的镜像缓存
     */
    List<ImageCache> findByStatus(ImageCacheStatus status);

    /**
     * 查找指定镜像标签的所有缓存
     */
    List<ImageCache> findByImageTag(String imageTag);

    /**
     * 查找最近命中的镜像缓存
     */
    @Query("SELECT ic FROM ImageCache ic WHERE ic.lastHitAt > :since ORDER BY ic.lastHitAt DESC")
    List<ImageCache> findRecentlyHitImages(@Param("since") LocalDateTime since);

    /**
     * 查找热点镜像 (按命中次数排序)
     */
    @Query("SELECT ic FROM ImageCache ic WHERE ic.hitCount > :threshold ORDER BY ic.hitCount DESC")
    List<ImageCache> findHotImages(@Param("threshold") int threshold);

    /**
     * 查找过期的镜像缓存
     */
    @Query("SELECT ic FROM ImageCache ic WHERE ic.lastHitAt < :cutoffTime OR ic.lastHitAt IS NULL")
    List<ImageCache> findExpiredCaches(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计服务器上的镜像缓存数量
     */
    int countByDockerServerId(Long dockerServerId);

    /**
     * 统计服务器上指定状态的镜像缓存数量
     */
    int countByDockerServerIdAndStatus(Long dockerServerId, ImageCacheStatus status);

    /**
     * 删除指定服务器上的所有镜像缓存
     */
    void deleteByDockerServerId(Long dockerServerId);
}
