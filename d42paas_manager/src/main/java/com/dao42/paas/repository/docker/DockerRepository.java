package com.dao42.paas.repository.docker;

import com.dao42.paas.enums.DockerStatus;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.docker.DockerServer;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.transaction.annotation.Transactional;

public interface DockerRepository
    extends CrudRepository<DockerContainer, Long>, JpaSpecificationExecutor<DockerContainer> {

    /**
     * 根据容器的Container_id查询
     *
     * @param containerId docker的containerId
     * @return
     */
    Optional<DockerContainer> findByContainerId(String containerId);

    long countByCreatedDateBetween(Date start, Date end);

    long countByCreatedDateBefore(Date end);

    List<DockerContainer> findAllByStatusAndContainerIdIsNotNull(DockerStatus status);

    List<DockerContainer> findAllByStatus(DockerStatus status);

    List<DockerContainer> findAllByDockerServerAndStatus(DockerServer dockerServer, DockerStatus status);

    /**
     * 查找DockerContainer
     *
     * @param dockerServer server
     * @param statuses     状态数组
     * @return DockerContainer集合
     */
    List<DockerContainer> findAllByDockerServerAndStatusIn(DockerServer dockerServer, DockerStatus[] statuses);

    @Transactional
    @Modifying
    @Query("update DockerContainer p set p.status = :status where p.id = :id")
    int updateStatusById(
            @NonNull @Param("status") DockerStatus status, @NonNull @Param("id") Long id);


    @Query(value = """
        SELECT
            dc.nfsNodeId as nfsNodeId,
            COUNT(dc.id) as dockerCount
        FROM DockerContainer dc
        WHERE dc.status = 'START_SUCCESS'
        AND dc.nfsNodeId IN :nodeIds
        AND dc.deleted = FALSE
        GROUP BY dc.nfsNodeId
        ORDER BY COUNT(dc.id) ASC
        """)
    List<Object[]> findLoadedNodeWithCount(@Param("nodeIds") List<Long> nodeIds);


}
