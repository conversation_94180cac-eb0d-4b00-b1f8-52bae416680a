package com.dao42.paas.repository;

import com.dao42.paas.model.NfsNode;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface NfsNodeRepository extends CrudRepository<NfsNode, Long> {

    // 方法1: 查询可用的NFS节点
    List<NfsNode> findAllByEnable(boolean enable);

    Optional<NfsNode> findById(Long id);

}
