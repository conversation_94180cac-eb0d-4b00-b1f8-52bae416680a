package com.dao42.paas.model.image;

import com.dao42.paas.enums.UserTier;
import com.dao42.paas.framework.jpa.model.AbstractAuditModel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * 用户镜像配额实体
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "user_image_quota")
@SQLDelete(sql = "UPDATE user_image_quota SET deleted = true WHERE id = ?")
@Where(clause = "deleted=false")
public class UserImageQuota extends AbstractAuditModel {

    /**
     * 用户ID
     */
    @NotNull
    @Column(name = "user_id", unique = true)
    private Long userId;

    /**
     * 租户ID
     */
    @NotNull
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 用户级别
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "user_tier")
    private UserTier userTier;

    /**
     * 最大镜像数量
     */
    @NotNull
    @Column(name = "max_images")
    private Integer maxImages;

    /**
     * 最大存储空间 (GB)
     */
    @NotNull
    @Column(name = "max_storage_gb")
    private Integer maxStorageGb;

    /**
     * 当前镜像数量
     */
    @Column(name = "current_images")
    private Integer currentImages = 0;

    /**
     * 当前存储空间 (GB)
     */
    @Column(name = "current_storage_gb")
    private BigDecimal currentStorageGb = BigDecimal.ZERO;

    /**
     * 配额重置时间
     */
    @Column(name = "quota_reset_at")
    private LocalDateTime quotaResetAt;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * 检查是否超出镜像数量限制
     */
    public boolean isImageCountExceeded() {
        if (maxImages == -1) { // 无限制
            return false;
        }
        return currentImages >= maxImages;
    }

    /**
     * 检查是否超出存储空间限制
     */
    public boolean isStorageExceeded() {
        if (maxStorageGb == -1) { // 无限制
            return false;
        }
        return currentStorageGb.compareTo(BigDecimal.valueOf(maxStorageGb)) >= 0;
    }

    /**
     * 增加镜像使用量
     */
    public void addImageUsage(long sizeBytes) {
        currentImages++;
        BigDecimal sizeGb = BigDecimal.valueOf(sizeBytes).divide(BigDecimal.valueOf(1024 * 1024 * 1024), 2, RoundingMode.HALF_UP);
        currentStorageGb = currentStorageGb.add(sizeGb);
    }

    /**
     * 减少镜像使用量
     */
    public void removeImageUsage(long sizeBytes) {
        currentImages = Math.max(0, currentImages - 1);
        BigDecimal sizeGb = BigDecimal.valueOf(sizeBytes).divide(BigDecimal.valueOf(1024 * 1024 * 1024), 2, RoundingMode.HALF_UP);
        currentStorageGb = currentStorageGb.subtract(sizeGb);
        if (currentStorageGb.compareTo(BigDecimal.ZERO) < 0) {
            currentStorageGb = BigDecimal.ZERO;
        }
    }
}
