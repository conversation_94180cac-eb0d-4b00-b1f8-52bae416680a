package com.dao42.paas.model;

import com.dao42.paas.framework.jpa.model.AbstractAuditModel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.validation.constraints.NotBlank;

/**
 * NfsNode (Btrfs Server)
 */

@Entity
@Data
public class NfsNode {

    @Id
    private Long id;

    @NotBlank
    private String host;

    @NotBlank
    private String mountPath;
    /**
     * 关闭开关，则表示不会在该节点上分配 元codeZone（根据 @meta 快照创建 codeZone） 创建请求
     */
    @NotBlank
    private boolean enable;

}
