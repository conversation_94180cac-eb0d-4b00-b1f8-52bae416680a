package com.dao42.paas.model.image;

import com.dao42.paas.enums.ContainerType;
import com.dao42.paas.enums.ThreadImageStatus;
import com.dao42.paas.framework.jpa.model.AbstractAuditModel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Thread镜像实体
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "thread_image")
@SQLDelete(sql = "UPDATE thread_image SET deleted = true WHERE id = ?")
@Where(clause = "deleted=false")
public class ThreadImage extends AbstractAuditModel {

    /**
     * 项目ID
     */
    @NotNull
    @Column(name = "project_id")
    private Long projectId;

    /**
     * Issue ID (可为空，Root Thread没有Issue ID)
     */
    @Column(name = "issue_id")
    private Long issueId;

    /**
     * 容器类型
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "container_type")
    private ContainerType containerType;

    /**
     * 镜像标签
     */
    @NotBlank
    @Column(name = "image_tag", length = 255)
    private String imageTag;

    /**
     * 镜像摘要
     */
    @Column(name = "image_digest", length = 255)
    private String imageDigest;

    /**
     * 镜像大小 (字节)
     */
    @Column(name = "image_size")
    private Long imageSize;

    /**
     * 父镜像标签
     */
    @Column(name = "parent_image_tag", length = 255)
    private String parentImageTag;

    /**
     * 用户ID
     */
    @NotNull
    @Column(name = "user_id")
    private Long userId;

    /**
     * 租户ID
     */
    @NotNull
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private ThreadImageStatus status;

    /**
     * 构建日志
     */
    @Lob
    @Column(name = "build_log")
    private String buildLog;

    /**
     * 使用次数
     */
    @Column(name = "usage_count")
    private Integer usageCount = 0;

    /**
     * 最后使用时间
     */
    @Column(name = "last_used_at")
    private LocalDateTime lastUsedAt;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
