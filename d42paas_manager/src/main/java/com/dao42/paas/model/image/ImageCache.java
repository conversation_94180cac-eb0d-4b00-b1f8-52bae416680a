package com.dao42.paas.model.image;

import com.dao42.paas.enums.ImageCacheStatus;
import com.dao42.paas.framework.jpa.model.AbstractAuditModel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 镜像缓存实体
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "image_cache")
@SQLDelete(sql = "UPDATE image_cache SET deleted = true WHERE id = ?")
@Where(clause = "deleted=false")
public class ImageCache extends AbstractAuditModel {

    /**
     * Docker服务器ID
     */
    @NotNull
    @Column(name = "docker_server_id")
    private Long dockerServerId;

    /**
     * 镜像标签
     */
    @NotBlank
    @Column(name = "image_tag", length = 255)
    private String imageTag;

    /**
     * 镜像摘要
     */
    @Column(name = "image_digest", length = 255)
    private String imageDigest;

    /**
     * 缓存大小 (字节)
     */
    @Column(name = "cache_size")
    private Long cacheSize;

    /**
     * 缓存状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private ImageCacheStatus status;

    /**
     * 命中次数
     */
    @Column(name = "hit_count")
    private Integer hitCount = 0;

    /**
     * 最后命中时间
     */
    @Column(name = "last_hit_at")
    private LocalDateTime lastHitAt;

    /**
     * 缓存时间
     */
    @Column(name = "cached_at")
    private LocalDateTime cachedAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        cachedAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
