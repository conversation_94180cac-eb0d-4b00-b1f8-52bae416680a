package com.dao42.paas.model.codezone;


import com.dao42.paas.common.LspSupportInterface;
import com.dao42.paas.common.ResourcesLimit;
import com.dao42.paas.framework.jpa.model.AbstractAuditModel;
import com.dao42.paas.model.Environment;
import com.dao42.paas.model.EnvironmentVer;
import com.dao42.paas.model.Language;
import com.dao42.paas.model.middleware.MiddlewareConfig;
import com.dao42.paas.model.unittest.UnitTestFramework;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

/**
 * 代码空间
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
@SQLDelete(sql = "UPDATE code_zone SET deleted = true WHERE id = ?")
@Where(clause = "deleted=false")
public class CodeZone extends AbstractAuditModel implements LspSupportInterface {

    /**
     * 代码文件存储根路径
     */
    @NotBlank
    private String rootPath;

    /**
     * 基础镜像
     */
    @NotNull
    @ManyToOne
    private EnvironmentVer environmentVer;

    /**
     * 租户
     */
    private Long tenantId;

    /**
     * 租户用户id
     */
    private String userId;

    /**
     * 测试用例框架
     */
    @ManyToOne
    private UnitTestFramework unitTestFramework;

    /**
     * codeZone包含的中间件
     */
    @OneToMany(cascade = CascadeType.ALL)
    private List<MiddlewareConfig> middlewareConfigList = new ArrayList<>();

    /**
     * 资源限制
     */
    @JsonIgnore
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private ResourcesLimit resourcesLimit;

    @JsonIgnore
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private Map<String, String> env;

    private String purpose;


    private String image;

    private Long nfsNodeId;

    public Map<String, String> getEnv() {
        return env != null ? env : new HashMap<>();
    }

    public ResourcesLimit getResourcesLimit() {
        return resourcesLimit != null ? resourcesLimit : environmentVer.getEnvironment().getResourcesLimit();
    }

    /**
     * 是否支持LSP
     *
     * @return true，支持；false，不支持
     */
    @Override
    @JsonIgnore
    public boolean isLspSupported() {
        if (environmentVer == null) {
            return false;
        }
        Environment env = environmentVer.getEnvironment();
        if (env == null) {
            return false;
        }
        Language language = env.getLanguage();
        if (language == null) {
            return false;
        }
        return language.isLspSupported();
    }

}
