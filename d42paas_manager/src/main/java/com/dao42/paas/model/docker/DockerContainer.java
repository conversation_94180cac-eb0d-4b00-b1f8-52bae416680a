package com.dao42.paas.model.docker;

import com.dao42.paas.common.ResourcesLimit;
import com.dao42.paas.enums.DockerStatus;
import com.dao42.paas.framework.jpa.model.AbstractAuditModel;
import com.dao42.paas.model.Environment;
import com.dao42.paas.model.EnvironmentVer;
import com.dao42.paas.model.Language;
import com.dao42.paas.model.middleware.MiddlewareInstance;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
@Entity
@SQLDelete(sql = "UPDATE docker_container SET deleted = true WHERE id = ?")
@Where(clause = "deleted=false")
public class DockerContainer extends AbstractAuditModel {

    /**
     * 构建docker的基础镜像
     */
    @NotNull
    @ManyToOne
    private EnvironmentVer environmentVer;

    /**
     * 所属dockerServer
     */
    @ManyToOne
    @NotFound(action = NotFoundAction.IGNORE)
    private DockerServer dockerServer;

    /**
     * 下属的中间件列表
     */
    @OneToMany
    private List<MiddlewareInstance> middlewares = new ArrayList<>();

    /**
     * 构建容器的镜像
     */
    @NotBlank
    @Length(max = 1000)
    @Column(length = 1000)
    private String image;

    /**
     * 容器中代码在宿主机的路径
     */
    @NotBlank
    private String rootPath;

    /**
     * 默认打开文件
     */
    private String defaultOpenFile;

    /**
     * dockerId docker未实例化为空
     */
    private String containerId;

    /** 容器状态 */
    @NotNull
    @Enumerated(EnumType.STRING)
    private DockerStatus status = DockerStatus.NOT_INIT;

    /**
     * 容器服务的节点IP/DNS
     */
    private String dockerAddress;

    /**
     * 容器服务端口绑定的宿主机端口
     */
    private int hostPort;

    /**
     * 容器lsp服务绑定的宿主机端口
     */
    private int lspPort;

    /**
     * 容器 ssh 服务绑定的宿主机端口
     */
    private Integer sshPort;

    /**
     * 容器 web 代理端口号
     */
    private Integer projectWebPort;

 
    /**
     * 用户容器唯一代理端口
     */
    private Integer agentServerPort;

    /**
     * 环境变量
     */
    private String envMap;

    /**
     * 所属租户
     */
    private Long tenantId;

    /**
     * 是否为批量创建
     */
    private boolean preCreate = false;

    private Long nfsNodeId;

    @JsonIgnore
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private ResourcesLimit resourcesLimit;

    public ResourcesLimit getResourcesLimit() {
        return resourcesLimit != null ? resourcesLimit : environmentVer.getEnvironment().getResourcesLimit();
    }

    /**
     * 是否支持LSP
     *
     * @return true，支持；false，不支持
     */
    @JsonIgnore
    public boolean isLspSupported() {
        EnvironmentVer envVer = environmentVer;
        if (envVer == null) {
            return false;
        }
        Environment env = envVer.getEnvironment();
        if (env == null) {
            return false;
        }
        Language language = env.getLanguage();
        if (language == null) {
            return false;
        }
        return language.isLspSupported();
    }
}
