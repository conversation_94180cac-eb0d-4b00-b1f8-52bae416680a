package com.dao42.paas.model;


import com.dao42.paas.framework.jpa.model.AbstractAuditModel;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

@Getter
@Setter
@Entity
@SQLDelete(sql = "UPDATE language SET deleted = true WHERE id = ?")
@Where(clause = "deleted=false")
public class Language extends AbstractAuditModel {

    /**
     * 名称
     */
    @NotBlank
    private String name;

    /**
     * 是否支持LSP
     */
    private boolean lspSupported;

    /**
     * LSP LanguageId 官方定义地址： https://microsoft.github.io/language-server-protocol/specification#textDocumentItem
     */
    private String lspLanguageId;

    /**
     * LSP启动命令
     */
    @Column(length = 1024)
    private String lspStartCmd;

    /**
     * 多LSP语言ID列表，以逗号分隔
     */
    @Column(name = "lsp_language_ids", length = 1024)
    private String lspLanguageIds;

    /**
     * 多LSP启动命令列表
     */
    @Column(name = "lsp_start_cmds", length = 1024)
    private String lspStartCmds;

    /**
     * Console 启动命令
     */
    @Column(length = 1024)
    private String consoleStartCmd;

    /**
     * 是否支持 Console
     */
    private boolean consoleSupported;

    /**
     * Console 启动命令
     */
    @Column(length = 1024)
    private String debugStartCmd;

    /**
     * 是否支持 Console
     */
    private boolean debugSupported;

    /**
     * 是否支持 实时刷新
     */
    private boolean realTimeRefresh;

}