package com.dao42.paas.model.meta;

import com.dao42.paas.enums.MetaEnvironmentStatus;
import com.dao42.paas.framework.jpa.model.AbstractAuditModel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * @meta环境实体
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "meta_environment")
@SQLDelete(sql = "UPDATE meta_environment SET deleted = true WHERE id = ?")
@Where(clause = "deleted=false")
public class MetaEnvironment extends AbstractAuditModel {

    /**
     * 环境名称
     */
    @NotBlank
    @Column(name = "name", length = 100)
    private String name;

    /**
     * 环境版本
     */
    @NotBlank
    @Column(name = "version", length = 50)
    private String environmentVersion;

    /**
     * S3存储路径
     */
    @NotBlank
    @Column(name = "s3_path", length = 500)
    private String s3Path;

    /**
     * 本地缓存路径
     */
    @Column(name = "local_cache_path", length = 500)
    private String localCachePath;

    /**
     * 环境大小 (字节)
     */
    @Column(name = "size_bytes")
    private Long sizeBytes;

    /**
     * 文件数量
     */
    @Column(name = "file_count")
    private Integer fileCount;

    /**
     * 状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private MetaEnvironmentStatus status;

    /**
     * 描述
     */
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 是否为默认环境
     */
    @Column(name = "is_default")
    private Boolean isDefault = false;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 最后同步时间
     */
    @Column(name = "last_synced_at")
    private LocalDateTime lastSyncedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
