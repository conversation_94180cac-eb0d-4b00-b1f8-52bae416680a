package com.dao42.paas.health;

import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.repository.image.ImageCacheRepository;
import com.dao42.paas.repository.image.ThreadImageRepository;
import com.dao42.paas.service.image.ImageRegistryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 镜像服务健康检查
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ImageServiceHealthIndicator implements HealthIndicator {

    private final ThreadImageRepository threadImageRepository;
    private final ImageCacheRepository imageCacheRepository;
    private final ImageRegistryService imageRegistryService;
    private final SystemProperties systemProperties;

    @Override
    public Health health() {
        try {
            Health.Builder builder = Health.up();
            
            // 检查数据库连接
            checkDatabaseHealth(builder);
            
            // 检查镜像仓库连接
            checkRegistryHealth(builder);
            
            // 检查本地缓存目录
            checkLocalCacheHealth(builder);
            
            // 检查@meta环境
            checkMetaEnvironmentHealth(builder);
            
            return builder.build();
            
        } catch (Exception e) {
            log.error("镜像服务健康检查失败", e);
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }

    /**
     * 检查数据库健康状态
     */
    private void checkDatabaseHealth(Health.Builder builder) {
        try {
            long threadImageCount = threadImageRepository.count();
            long imageCacheCount = imageCacheRepository.count();
            
            builder.withDetail("database", "UP")
                   .withDetail("threadImageCount", threadImageCount)
                   .withDetail("imageCacheCount", imageCacheCount);
                   
        } catch (Exception e) {
            log.error("数据库健康检查失败", e);
            builder.withDetail("database", "DOWN")
                   .withDetail("databaseError", e.getMessage());
        }
    }

    /**
     * 检查镜像仓库健康状态
     */
    private void checkRegistryHealth(Health.Builder builder) {
        try {
            // 尝试检查一个已知存在的镜像
            String testImage = systemProperties.getImage().getRegistry().getUrl() + 
                              "/clacky/docker:latest";
            
            boolean registryHealthy = imageRegistryService.exists(testImage);
            
            if (registryHealthy) {
                builder.withDetail("imageRegistry", "UP")
                       .withDetail("registryUrl", systemProperties.getImage().getRegistry().getUrl());
            } else {
                builder.withDetail("imageRegistry", "DOWN")
                       .withDetail("registryError", "无法连接到镜像仓库");
            }
            
        } catch (Exception e) {
            log.error("镜像仓库健康检查失败", e);
            builder.withDetail("imageRegistry", "DOWN")
                   .withDetail("registryError", e.getMessage());
        }
    }

    /**
     * 检查本地缓存健康状态
     */
    private void checkLocalCacheHealth(Health.Builder builder) {
        try {
            String localCachePath = systemProperties.getImage().getMeta().getLocalCachePath();
            
            if (localCachePath != null && Files.exists(Paths.get(localCachePath))) {
                builder.withDetail("localCache", "UP")
                       .withDetail("localCachePath", localCachePath);
            } else {
                builder.withDetail("localCache", "DOWN")
                       .withDetail("localCacheError", "本地缓存目录不存在: " + localCachePath);
            }
            
        } catch (Exception e) {
            log.error("本地缓存健康检查失败", e);
            builder.withDetail("localCache", "DOWN")
                   .withDetail("localCacheError", e.getMessage());
        }
    }

    /**
     * 检查@meta环境健康状态
     */
    private void checkMetaEnvironmentHealth(Health.Builder builder) {
        try {
            // 检查S3连接 (简化版本)
            String s3Bucket = systemProperties.getImage().getMeta().getS3Bucket();
            
            if (s3Bucket != null && !s3Bucket.isEmpty()) {
                builder.withDetail("metaEnvironment", "UP")
                       .withDetail("s3Bucket", s3Bucket);
            } else {
                builder.withDetail("metaEnvironment", "DOWN")
                       .withDetail("metaError", "S3存储桶配置缺失");
            }
            
        } catch (Exception e) {
            log.error("@meta环境健康检查失败", e);
            builder.withDetail("metaEnvironment", "DOWN")
                   .withDetail("metaError", e.getMessage());
        }
    }
}
