package com.dao42.paas.health;

import com.dao42.paas.config.properties.SystemProperties;
import com.dao42.paas.framework.web.response.ApiResponse;
import com.dao42.paas.repository.image.ImageCacheRepository;
import com.dao42.paas.repository.image.ThreadImageRepository;
import com.dao42.paas.service.image.ImageRegistryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 镜像服务健康检查
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/actuator/health/image-service")
@RequiredArgsConstructor
public class ImageServiceHealthIndicator {

    private final ThreadImageRepository threadImageRepository;
    private final ImageCacheRepository imageCacheRepository;
    private final ImageRegistryService imageRegistryService;
    private final SystemProperties systemProperties;

    @GetMapping
    public ApiResponse<Map<String, Object>> health() {
        try {
            Map<String, Object> healthInfo = new HashMap<>();

            // 检查数据库连接
            checkDatabaseHealth(healthInfo);

            // 检查镜像仓库连接
            checkRegistryHealth(healthInfo);

            // 检查本地缓存目录
            checkLocalCacheHealth(healthInfo);

            // 检查@meta环境
            checkMetaEnvironmentHealth(healthInfo);

            healthInfo.put("status", "UP");
            return ApiResponse.success(healthInfo);

        } catch (Exception e) {
            log.error("镜像服务健康检查失败", e);
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("status", "DOWN");
            errorInfo.put("error", e.getMessage());
            return ApiResponse.error(500, "镜像服务健康检查失败", errorInfo);
        }
    }

    /**
     * 检查数据库健康状态
     */
    private void checkDatabaseHealth(Map<String, Object> healthInfo) {
        try {
            long threadImageCount = threadImageRepository.count();
            long imageCacheCount = imageCacheRepository.count();

            healthInfo.put("database", "UP");
            healthInfo.put("threadImageCount", threadImageCount);
            healthInfo.put("imageCacheCount", imageCacheCount);

        } catch (Exception e) {
            log.error("数据库健康检查失败", e);
            healthInfo.put("database", "DOWN");
            healthInfo.put("databaseError", e.getMessage());
        }
    }

    /**
     * 检查镜像仓库健康状态
     */
    private void checkRegistryHealth(Map<String, Object> healthInfo) {
        try {
            // 尝试检查一个已知存在的镜像
            String testImage = systemProperties.getImage().getRegistry().getUrl() +
                              "/clacky/docker:latest";

            boolean registryHealthy = imageRegistryService.exists(testImage);

            if (registryHealthy) {
                healthInfo.put("imageRegistry", "UP");
                healthInfo.put("registryUrl", systemProperties.getImage().getRegistry().getUrl());
            } else {
                healthInfo.put("imageRegistry", "DOWN");
                healthInfo.put("registryError", "无法连接到镜像仓库");
            }

        } catch (Exception e) {
            log.error("镜像仓库健康检查失败", e);
            healthInfo.put("imageRegistry", "DOWN");
            healthInfo.put("registryError", e.getMessage());
        }
    }

    /**
     * 检查本地缓存健康状态
     */
    private void checkLocalCacheHealth(Map<String, Object> healthInfo) {
        try {
            String localCachePath = systemProperties.getImage().getMeta().getLocalCachePath();

            if (localCachePath != null && Files.exists(Paths.get(localCachePath))) {
                healthInfo.put("localCache", "UP");
                healthInfo.put("localCachePath", localCachePath);
            } else {
                healthInfo.put("localCache", "DOWN");
                healthInfo.put("localCacheError", "本地缓存目录不存在: " + localCachePath);
            }

        } catch (Exception e) {
            log.error("本地缓存健康检查失败", e);
            healthInfo.put("localCache", "DOWN");
            healthInfo.put("localCacheError", e.getMessage());
        }
    }

    /**
     * 检查@meta环境健康状态
     */
    private void checkMetaEnvironmentHealth(Map<String, Object> healthInfo) {
        try {
            // 检查S3连接 (简化版本)
            String s3Bucket = systemProperties.getImage().getMeta().getS3Bucket();

            if (s3Bucket != null && !s3Bucket.isEmpty()) {
                healthInfo.put("metaEnvironment", "UP");
                healthInfo.put("s3Bucket", s3Bucket);
            } else {
                healthInfo.put("metaEnvironment", "DOWN");
                healthInfo.put("metaError", "S3存储桶配置缺失");
            }

        } catch (Exception e) {
            log.error("@meta环境健康检查失败", e);
            healthInfo.put("metaEnvironment", "DOWN");
            healthInfo.put("metaError", e.getMessage());
        }
    }
}
