package com.dao42.paas.dto.image;

import lombok.Builder;
import lombok.Data;

/**
 * Docker commit选项
 *
 * <AUTHOR>
 */
@Data
@Builder
public class CommitOptions {
    
    /**
     * 作者信息
     */
    private String author;
    
    /**
     * 提交消息
     */
    private String message;
    
    /**
     * 是否暂停容器
     */
    private boolean pause;
    
    /**
     * 变更集
     */
    private String[] changes;
}
