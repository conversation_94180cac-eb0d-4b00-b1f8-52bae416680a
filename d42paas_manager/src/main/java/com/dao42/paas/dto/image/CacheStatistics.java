package com.dao42.paas.dto.image;

import lombok.Data;

/**
 * 缓存统计信息DTO
 *
 * <AUTHOR>
 */
@Data
public class CacheStatistics {
    
    /**
     * 总缓存数量
     */
    private int totalCacheCount;
    
    /**
     * 可用缓存数量
     */
    private int availableCacheCount;
    
    /**
     * 失败缓存数量
     */
    private int failedCacheCount;
    
    /**
     * 拉取中缓存数量
     */
    private int pullingCacheCount;
    
    /**
     * 总缓存大小 (字节)
     */
    private long totalCacheSize;
    
    /**
     * 热点镜像数量
     */
    private int hotImageCount;
    
    /**
     * 过期缓存数量
     */
    private int expiredCacheCount;
    
    /**
     * 缓存命中率
     */
    private double hitRate;
    
    /**
     * 平均镜像大小 (字节)
     */
    private long averageImageSize;
    
    /**
     * 最大镜像大小 (字节)
     */
    private long maxImageSize;
    
    /**
     * 最小镜像大小 (字节)
     */
    private long minImageSize;
    
    /**
     * 服务器数量
     */
    private int serverCount;
    
    /**
     * 每服务器平均缓存数量
     */
    private double averageCachePerServer;
}
