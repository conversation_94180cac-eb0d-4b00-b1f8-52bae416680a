package com.dao42.paas.dto.container;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 容器启动结果DTO
 *
 * <AUTHOR>
 */
@Data
public class ContainerStartupResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 容器ID
     */
    private String containerId;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 启动时间 (毫秒)
     */
    private long startupTimeMs;
    
    /**
     * 使用的镜像标签
     */
    private String imageTag;
    
    /**
     * 目标服务器ID
     */
    private String serverId;
    
    /**
     * 是否使用了缓存镜像
     */
    private boolean usedCachedImage;
    
    /**
     * 是否注入了@meta环境
     */
    private boolean metaEnvironmentInjected;
    
    /**
     * 启动开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 启动完成时间
     */
    private LocalDateTime endTime;
    
    /**
     * 详细步骤耗时
     */
    private StepTimings stepTimings;
    
    @Data
    public static class StepTimings {
        /**
         * 服务器选择耗时 (毫秒)
         */
        private long serverSelectionMs;
        
        /**
         * 镜像选择耗时 (毫秒)
         */
        private long imageSelectionMs;
        
        /**
         * 镜像拉取耗时 (毫秒)
         */
        private long imagePullMs;
        
        /**
         * @meta环境注入耗时 (毫秒)
         */
        private long metaInjectionMs;
        
        /**
         * 容器创建耗时 (毫秒)
         */
        private long containerCreationMs;
        
        /**
         * 容器启动耗时 (毫秒)
         */
        private long containerStartMs;
    }
}
