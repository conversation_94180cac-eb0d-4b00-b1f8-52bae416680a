package com.dao42.paas.dto.image;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 镜像缓存响应DTO
 *
 * <AUTHOR>
 */
@Data
public class ImageCacheResponse {
    
    /**
     * 缓存ID
     */
    private Long id;
    
    /**
     * Docker服务器ID
     */
    private Long dockerServerId;
    
    /**
     * 镜像标签
     */
    private String imageTag;
    
    /**
     * 镜像摘要
     */
    private String imageDigest;
    
    /**
     * 缓存大小 (字节)
     */
    private Long cacheSize;
    
    /**
     * 缓存状态
     */
    private String status;
    
    /**
     * 命中次数
     */
    private Integer hitCount;
    
    /**
     * 最后命中时间
     */
    private LocalDateTime lastHitAt;
    
    /**
     * 缓存时间
     */
    private LocalDateTime cachedAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 缓存大小 (格式化)
     */
    public String getFormattedCacheSize() {
        if (cacheSize == null) {
            return "未知";
        }
        
        if (cacheSize < 1024) {
            return cacheSize + " B";
        } else if (cacheSize < 1024 * 1024) {
            return String.format("%.1f KB", cacheSize / 1024.0);
        } else if (cacheSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", cacheSize / (1024.0 * 1024));
        } else {
            return String.format("%.1f GB", cacheSize / (1024.0 * 1024 * 1024));
        }
    }
}
