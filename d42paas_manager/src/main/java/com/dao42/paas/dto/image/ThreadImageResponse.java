package com.dao42.paas.dto.image;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Thread镜像响应DTO
 *
 * <AUTHOR>
 */
@Data
public class ThreadImageResponse {
    
    /**
     * 镜像ID
     */
    private Long id;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * Issue ID
     */
    private Long issueId;
    
    /**
     * 容器类型
     */
    private String containerType;
    
    /**
     * 镜像标签
     */
    private String imageTag;
    
    /**
     * 镜像摘要
     */
    private String imageDigest;
    
    /**
     * 镜像大小 (字节)
     */
    private Long imageSize;
    
    /**
     * 父镜像标签
     */
    private String parentImageTag;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 使用次数
     */
    private Integer usageCount;
    
    /**
     * 最后使用时间
     */
    private LocalDateTime lastUsedAt;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 镜像大小 (格式化)
     */
    public String getFormattedImageSize() {
        if (imageSize == null) {
            return "未知";
        }
        
        if (imageSize < 1024) {
            return imageSize + " B";
        } else if (imageSize < 1024 * 1024) {
            return String.format("%.1f KB", imageSize / 1024.0);
        } else if (imageSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", imageSize / (1024.0 * 1024));
        } else {
            return String.format("%.1f GB", imageSize / (1024.0 * 1024 * 1024));
        }
    }
}
