package com.dao42.paas.dto.image;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Thread镜像构建请求DTO
 *
 * <AUTHOR>
 */
@Data
public class ThreadImageBuildRequest {
    
    /**
     * 容器ID
     */
    @NotBlank(message = "容器ID不能为空")
    private String containerId;
    
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;
    
    /**
     * Issue ID (可选)
     */
    private Long issueId;
    
    /**
     * 容器类型
     */
    @NotBlank(message = "容器类型不能为空")
    private String containerType;
    
    /**
     * 镜像标签 (可选，系统自动生成)
     */
    private String imageTag;
    
    /**
     * 构建消息
     */
    private String buildMessage;
    
    /**
     * 是否立即推送到仓库
     */
    private Boolean pushImmediately = true;
}
