package com.dao42.paas.dto.image;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 镜像信息DTO
 *
 * <AUTHOR>
 */
@Data
public class ImageInfo {
    
    /**
     * 镜像ID
     */
    private String id;
    
    /**
     * 镜像标签
     */
    private String tag;
    
    /**
     * 镜像摘要
     */
    private String digest;
    
    /**
     * 镜像大小 (字节)
     */
    private Long size;
    
    /**
     * 创建时间
     */
    private LocalDateTime created;
    
    /**
     * 所有标签
     */
    private List<String> repoTags;
    
    /**
     * 父镜像ID
     */
    private String parentId;
}
