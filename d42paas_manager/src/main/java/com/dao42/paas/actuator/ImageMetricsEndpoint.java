package com.dao42.paas.actuator;

import com.dao42.paas.dto.image.CacheStatistics;
import com.dao42.paas.service.image.HotImageCacheManager;
import com.dao42.paas.service.monitoring.ImageMetricsService;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.actuator.endpoint.annotation.Endpoint;
import org.springframework.boot.actuator.endpoint.annotation.ReadOperation;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 镜像化监控端点
 *
 * <AUTHOR>
 */
@Component
@Endpoint(id = "image-metrics")
@RequiredArgsConstructor
public class ImageMetricsEndpoint {

    private final ImageMetricsService imageMetricsService;
    private final HotImageCacheManager hotImageCacheManager;

    @ReadOperation
    public Map<String, Object> imageMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        try {
            // 基础统计
            metrics.put("totalThreadImages", imageMetricsService.getTotalThreadImages());
            metrics.put("availableThreadImages", imageMetricsService.getAvailableThreadImages());
            metrics.put("totalImageCaches", imageMetricsService.getTotalImageCaches());
            metrics.put("availableImageCaches", imageMetricsService.getAvailableImageCaches());
            metrics.put("totalUsers", imageMetricsService.getTotalUsers());
            metrics.put("cacheHitRate", imageMetricsService.getCacheHitRate());
            
            // 缓存详细统计
            CacheStatistics cacheStats = hotImageCacheManager.getCacheStatistics();
            metrics.put("cacheStatistics", cacheStats);
            
            // 性能指标
            Map<String, Object> performance = new HashMap<>();
            performance.put("averageContainerStartupTime", "N/A"); // 可以从Timer中获取
            performance.put("averageImageBuildTime", "N/A");
            performance.put("averageImagePullTime", "N/A");
            performance.put("averageMetaInjectionTime", "N/A");
            metrics.put("performance", performance);
            
            // 健康状态
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("imageServiceHealthy", true);
            health.put("cacheServiceHealthy", true);
            health.put("metaServiceHealthy", true);
            metrics.put("health", health);
            
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "ERROR");
            error.put("message", e.getMessage());
            metrics.put("error", error);
        }
        
        return metrics;
    }
}
