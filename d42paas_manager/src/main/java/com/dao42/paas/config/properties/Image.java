package com.dao42.paas.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * 镜像配置
 *
 * <AUTHOR>
 */
@Data
public class Image {

    /**
     * 镜像仓库配置
     */
    @NestedConfigurationProperty
    private Registry registry;

    /**
     * 镜像构建配置
     */
    @NestedConfigurationProperty
    private Build build;

    /**
     * 缓存配置
     */
    @NestedConfigurationProperty
    private Cache cache;

    /**
     * @meta 环境配置
     */
    @NestedConfigurationProperty
    private Meta meta;

    /**
     * 用户配额配置
     */
    @NestedConfigurationProperty
    private Quota quota;

    /**
     * 是否自动清理本地镜像
     */
    private boolean autoCleanup = true;

    @Data
    public static class Registry {
        /**
         * 镜像仓库地址
         */
        private String url = "992382636473.dkr.ecr.us-east-1.amazonaws.com";

        /**
         * 用户名
         */
        private String username = "AWS";

        /**
         * 密码
         */
        private String password;

        /**
         * 命名空间
         */
        private String namespace = "clacky";
    }

    @Data
    public static class Build {
        /**
         * 构建超时时间 (秒)
         */
        private int timeoutSeconds = 300;

        /**
         * 并发构建数量
         */
        private int concurrentBuilds = 5;

        /**
         * 是否自动清理
         */
        private boolean autoCleanup = true;
    }

    @Data
    public static class Cache {
        /**
         * 热点镜像阈值
         */
        private int hotImageThreshold = 10;

        /**
         * 是否启用预拉取
         */
        private boolean preloadEnabled = true;

        /**
         * 是否启用缓存清理
         */
        private boolean cleanupEnabled = true;

        /**
         * 不活跃天数
         */
        private int inactiveDays = 30;
    }

    @Data
    public static class Meta {
        /**
         * S3存储桶
         */
        private String s3Bucket = "clacky-meta-environment";

        /**
         * 更新间隔天数
         */
        private int updateIntervalDays = 30;

        /**
         * 本地缓存路径
         */
        private String localCachePath = "/cache/meta";
    }

    @Data
    public static class Quota {
        /**
         * 免费版配额
         */
        @NestedConfigurationProperty
        private Tier freeTier = new Tier(5, 10);

        /**
         * 基础版配额
         */
        @NestedConfigurationProperty
        private Tier basicTier = new Tier(20, 50);

        /**
         * 专业版配额
         */
        @NestedConfigurationProperty
        private Tier proTier = new Tier(100, 200);

        /**
         * 企业版配额
         */
        @NestedConfigurationProperty
        private Tier enterpriseTier = new Tier(-1, -1); // 无限制

        @Data
        public static class Tier {
            /**
             * 最大镜像数量
             */
            private int maxImages;

            /**
             * 最大存储空间 (GB)
             */
            private int maxStorageGb;

            public Tier() {}

            public Tier(int maxImages, int maxStorageGb) {
                this.maxImages = maxImages;
                this.maxStorageGb = maxStorageGb;
            }
        }
    }
}
