package com.dao42.paas.config;

import com.dao42.paas.config.properties.SystemProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

/**
 * 镜像化配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableAsync
@EnableScheduling
@RequiredArgsConstructor
public class ImageConfiguration {

    private final SystemProperties systemProperties;

    @PostConstruct
    public void init() {
        log.info("镜像化服务配置初始化");
        log.info("镜像仓库地址: {}", systemProperties.getImage().getRegistry().getUrl());
        log.info("镜像构建并发数: {}", systemProperties.getImage().getBuild().getConcurrentBuilds());
        log.info("热点镜像阈值: {}", systemProperties.getImage().getCache().getHotImageThreshold());
        log.info("@meta环境S3存储桶: {}", systemProperties.getImage().getMeta().getS3Bucket());
    }

    /**
     * 镜像构建任务执行器
     */
    @Bean("imageTaskExecutor")
    @ConditionalOnProperty(name = "system.image.build.concurrent-builds", havingValue = "true", matchIfMissing = true)
    public Executor imageTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(systemProperties.getImage().getBuild().getConcurrentBuilds());
        executor.setMaxPoolSize(systemProperties.getImage().getBuild().getConcurrentBuilds() * 2);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("image-task-");
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        
        log.info("镜像任务执行器初始化完成: corePoolSize={}, maxPoolSize={}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize());
        
        return executor;
    }

    /**
     * 缓存管理任务执行器
     */
    @Bean("cacheTaskExecutor")
    @ConditionalOnProperty(name = "system.image.cache.preload-enabled", havingValue = "true", matchIfMissing = true)
    public Executor cacheTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("cache-task-");
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        
        log.info("缓存任务执行器初始化完成: corePoolSize={}, maxPoolSize={}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize());
        
        return executor;
    }

    /**
     * @meta环境同步任务执行器
     */
    @Bean("metaTaskExecutor")
    public Executor metaTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(20);
        executor.setThreadNamePrefix("meta-task-");
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        
        log.info("@meta任务执行器初始化完成: corePoolSize={}, maxPoolSize={}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize());
        
        return executor;
    }
}
