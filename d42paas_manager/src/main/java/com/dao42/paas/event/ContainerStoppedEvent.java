package com.dao42.paas.event;

import com.dao42.paas.model.docker.DockerContainer;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 容器停止事件
 *
 * <AUTHOR>
 */
@Getter
public class ContainerStoppedEvent extends ApplicationEvent {
    
    private final DockerContainer container;
    
    public ContainerStoppedEvent(Object source, DockerContainer container) {
        super(source);
        this.container = container;
    }
}
