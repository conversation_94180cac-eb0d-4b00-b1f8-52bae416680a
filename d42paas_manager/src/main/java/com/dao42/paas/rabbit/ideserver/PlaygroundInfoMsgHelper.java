package com.dao42.paas.rabbit.ideserver;

import com.dao42.paas.common.bean.EnvironmentVersion;
import com.dao42.paas.common.message.DockerInfoMQMsg;
import com.dao42.paas.common.message.PlaygroundInfoMQMsg;
import com.dao42.paas.constants.Constant;
import com.dao42.paas.constants.Constant.SpecialChar;
import com.dao42.paas.framework.utils.JsonUtils;
import com.dao42.paas.model.Environment;
import com.dao42.paas.model.EnvironmentVer;
import com.dao42.paas.model.Playground;
import com.dao42.paas.utils.DirUtil;
import java.io.File;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

public class PlaygroundInfoMsgHelper {

    /**
     * 组装PlaygroundInfo
     *
     * @param replyMessageId  replyMessageId
     * @param playground      playground
     * @param initialPath     初始化文件路径（这个文件是从哪里fork的）
     * @param appPath         文件路径
     * @param url             服务url
     * @param lspUrl          lspUrl
     * @param dockerInfoStr   dockerInfo
     * @param defaultOpenFile 默认打开文件
     * @return
     */
    public static PlaygroundInfoMQMsg get(String replyMessageId, Playground playground, String initialPath,
        String appPath, String url, String lspUrl, String dockerInfoStr, String defaultOpenFile,  String agentServerUrl) {
        Environment environment = playground.getEnvironment();
        EnvironmentVer environmentVer = playground.getEnvironmentVer();

        PlaygroundInfoMQMsg message = new PlaygroundInfoMQMsg();
        message.setReplyMessageId(replyMessageId);
        message.setStatus(playground.getStatus());
        message.setDockerId(playground.getDockerContainer().getId().toString());
        message.setFileRootPath(
            DirUtil.join(playground.getDockerContainer().getRootPath(), Constant.CODE_ZONE_SOURCE_PATH));
        message.setFileInitialPath(
            StringUtils.isBlank(initialPath) ? null : DirUtil.join(initialPath, Constant.CODE_ZONE_SOURCE_PATH));
        message.setFileRootId(getFileRootId(message.getFileRootPath()));
        message.setLspRootPath(appPath);
        message.setFileTreeIgnore(Constant.IGNORE_FILE_NAME + environment.getFileTreeIgnore());
        message.setFileTreePackage(environment.getFileTreePackage());
        message.setLspUrl(lspUrl);
        message.setUrl(url);
        message.setLspLanguageId(
            environment.getLanguage().getLspLanguageId());
        message.setLanguage(environment.getLanguage().getName());
        message.setLspSupported(
            playground.getDockerContainer().getEnvironmentVer().getEnvironment().getLanguage().isLspSupported());
        message.setRefresh(true);
        message.setDebugState("stop");
        DockerInfoMQMsg dockerInfo = JsonUtils.jsonToPojo(dockerInfoStr, DockerInfoMQMsg.class);
        if (dockerInfo != null) {
            message.setRunStatus(dockerInfo.getRunStatus());
            message.setLspStatus(dockerInfo.getLspStatus());
            message.setVncSupport(dockerInfo.isVncSupport());
            message.setVncStatus(dockerInfo.getVncStatus());
            message.setGui(dockerInfo.isGui());
            message.setTerminalStatus(dockerInfo.getTerminalStatus());
            message.setRagStatus(dockerInfo.getRagStatus());
            message.setDebugSupport(dockerInfo.isDebugSupport());
            message.setDebugState(dockerInfo.getDebugState());
            message.setRefresh(dockerInfo.isRefresh());
            message.setIntervalTime(dockerInfo.getIntervalTime());
        }
        message.setDefaultOpenFile(defaultOpenFile);
        message.setRealTimeRefresh(playground.getEnvironment().getLanguage().isRealTimeRefresh());

        if ( environmentVer!= null ){
            EnvironmentVersion environmentVersion = getEnvironmentVersion(environmentVer);
            message.setEnvironmentVersion(environmentVersion);
        }

        message.setAgentServerUrl(agentServerUrl);

        return message;
    }

    private static EnvironmentVersion getEnvironmentVersion(EnvironmentVer environmentVer) {
        EnvironmentVersion environmentVersion = new EnvironmentVersion();
        environmentVersion.setId(environmentVer.getId());
        environmentVersion.setLanguage(environmentVer.getEnvironment().getLanguage().getName());
        environmentVersion.setName(environmentVer.getName());
        environmentVersion.setEnvCode(environmentVer.getEnvCode());
        environmentVersion.setRuntime(environmentVer.getRuntime());
        environmentVersion.setTags(environmentVer.getTags() == null ? null:List.of(environmentVer.getTags().split(SpecialChar.SEMICOLON)));
        environmentVersion.setPackageManagers(
            environmentVer.getPackageManagers() == null ? null:List.of(environmentVer.getPackageManagers().split(SpecialChar.SEMICOLON)));
        environmentVersion.setRuntimeInformation(
            environmentVer.getRuntimeInformation() == null ? null:List.of(environmentVer.getRuntimeInformation().split(SpecialChar.SEMICOLON)));
        return environmentVersion;
    }

    public static String getFileRootId(String path) {
        if (StringUtils.isBlank(path)) {
            return "";
        }
        File file = new File(path);
        return file.getParentFile().getName();
    }
}
