package com.dao42.paas.rabbit.ideserver;

import com.dao42.paas.common.enums.PlaygroundStatus;
import com.dao42.paas.common.message.DockerInfoMQMsg;
import com.dao42.paas.constants.MdcConstant;
import com.dao42.paas.exception.KongRequestException;
import com.dao42.paas.framework.alert.ExceptionMsgBot;
import com.dao42.paas.framework.alert.ExceptionMsgBotBean;
import com.dao42.paas.model.Playground;
import com.dao42.paas.repository.PlaygroundRepository;
import com.dao42.paas.service.RedisExternalService;
import com.dao42.paas.service.docker.DockerService;
import com.dao42.paas.service.playground.PlaygroundService;
import com.dao42.paas.service.resource.URLResourceService;
import com.dao42.paas.utils.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class DockerInfoMQHandler extends AbstractIDEServerMQHandler {

    private final PlaygroundRepository playgroundRepository;
    private final DockerService dockerService;
    private final RedisExternalService redisExternalService;
    private final URLResourceService urlResourceService;
    private final ExceptionMsgBot bot;
    private final PlaygroundService playgroundService;

    @Async
    @Override
    public void handle(String routingKey, String body) {
        long startTime = System.currentTimeMillis();
        DockerInfoMQMsg mqMessage = JsonUtil.jsonToPojo(body, DockerInfoMQMsg.class);
        checkExpire(mqMessage);
        String replyContent = redisExternalService.getMQWaitDockerInfo(mqMessage.getDockerId());

        MDC.put(MdcConstant.DOCKER_ID, mqMessage.getDockerId());
        if (!mqMessage.getDockerId().equals(replyContent)) {
            log.warn("DockerScheduling-Active, getMQWaitDockerInfo timeout(30s), dockerId: {}", mqMessage.getDockerId());
            return;
        }

        // 移除等待激活消息的redis key
        redisExternalService.removeMQWaitDockerInfo(mqMessage.getDockerId());
        // 激活成功，更新playground状态
        int effectCount = playgroundRepository.updateStatusByDockerId(PlaygroundStatus.ACTIVE,
            Long.valueOf(mqMessage.getDockerId()));
        if (effectCount == 0) {
            log.warn("DockerScheduling-Active, 根据DockerId({})没有更新Playground的状态，可能绑定的Docker已经变化", mqMessage.getDockerId());
            return;
        }
        Playground playground = playgroundRepository.findByDockerContainerId(Long.valueOf(mqMessage.getDockerId()));
        if (playground == null) {
            log.warn("DockerScheduling-Active, 根据DockerId({})未找到Playground，可能绑定的Docker已经变化", mqMessage.getDockerId());
            return;
        }
        MDC.put(MdcConstant.PLAYGROUND_ID, String.valueOf(playground.getId()));
        // 如果是预创建的Playground，不添加超时监听（后续sdk心跳会重新加上）
        if (!playground.getDockerContainer().isPreCreate()) {
            // 开始监视Playground超时
            redisExternalService.startPlaygroundMonitor(playground);
        }
        redisExternalService.setDockerInfo(mqMessage);
        // 启动前先添加Kong网关解析，避免激活成功的MQ消息发送给前端时，Kong还没有此路由导致的502
        try {
            urlResourceService.request(playground.getDockerContainer());
        } catch (KongRequestException e) {
            log.warn("DockerScheduling-Active, 激活成功，但Kong网关添加路由失败, docker_id: {}, e: {}", mqMessage.getDockerId(), e);
            bot.send(new ExceptionMsgBotBean(e, "激活成功，但Kong网关添加路由失败", null));
            // 发送激活失败MQ消息
            dockerService.sendActiveFailResult(playground.getDockerContainer().getId());
            throw new RuntimeException(e);
        }
        // 发送playground环境变量
        dockerService.sendPlaygroundEnv(playground);
        // 发送激活成功MQ消息
        dockerService.sendActiveSuccessResult(playground);
        // 发送playground url 资源信息
        playgroundService.notifyPlaygroundUrlResource(playground);

        // 发送最新的playgroundInfo
        playgroundService.notifyPlaygroundInfo(playground);

        long endTime = System.currentTimeMillis();
        long diff = endTime - startTime;
        log.info("DockerScheduling-Active-dockerinfo-end, Activating Playground：{}, cost_time: {} ms", playground.getId(), diff);
    }
}
