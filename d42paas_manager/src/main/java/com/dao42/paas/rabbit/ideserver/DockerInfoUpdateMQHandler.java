package com.dao42.paas.rabbit.ideserver;

import com.dao42.paas.common.constants.MsgType;
import com.dao42.paas.common.enums.DockerStatus;
import com.dao42.paas.common.message.DockerInfoMQMsg;
import com.dao42.paas.common.message.PlaygroundHeartBeatResultMQMsg;
import com.dao42.paas.constants.MdcConstant;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.rabbit.MQMessageSender;
import com.dao42.paas.repository.PlaygroundRepository;
import com.dao42.paas.service.RedisExternalService;
import com.dao42.paas.service.docker.DockerContainerService;
import com.dao42.paas.service.playground.PlaygroundService;
import com.dao42.paas.utils.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class DockerInfoUpdateMQHandler extends AbstractIDEServerMQHandler {

    private final RedisExternalService redisExternalService;
    private final DockerContainerService dockerContainerService;
    private final MQMessageSender mqMessageSender;
    private final PlaygroundRepository playgroundRepository;
    private final PlaygroundService playgroundService;

    @Async
    @Override
    public void handle(String routingKey, String body) {
        DockerInfoMQMsg mqMessage = JsonUtil.jsonToPojo(body, DockerInfoMQMsg.class);
        if (mqMessage == null || StringUtils.isBlank(mqMessage.getDockerId())) {
            return;
        }
        // 判断是否绑定 playground 以及 playground 状态
        DockerContainer dockerContainer = this.dockerContainerService.getById(Long.valueOf(mqMessage.getDockerId()));
        Playground playground = this.playgroundRepository.findByDockerContainerId(dockerContainer.getId());

        MDC.put(MdcConstant.PLAYGROUND_ID, String.valueOf(playground.getId()));
        MDC.put(MdcConstant.DOCKER_ID, String.valueOf(dockerContainer.getId()));

        this.redisExternalService.setDockerInfo(mqMessage);
        // 心跳成功
        boolean receivedMessage = redisExternalService.hasMQReplay(mqMessage.getReplyMessageId());
        if (receivedMessage) {
            redisExternalService.clearMQMessageKey(mqMessage.getReplyMessageId());
            PlaygroundHeartBeatResultMQMsg result = new PlaygroundHeartBeatResultMQMsg();
            result.setDockerStatus(DockerStatus.OK);
            redisExternalService.startPlaygroundMonitor(playground);
            // 检查IDE Server
            if (playground != null && playground.getIdeServer() == null) {
                playground = playgroundService.updateIdeServer(playground, mqMessage.getIdeServer());
            }
            mqMessageSender.sendToIdeServer(MsgType.PLAYGROUND_HEART_BEAT, playground, result);

        }

        // 发送最新的playgroundInfo
        playgroundService.notifyPlaygroundInfo(playground);
    }
}
