# 开发环境 Dockerfile - 支持热重载
FROM amazoncorretto:17-alpine

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装必要的包，包括 inotify-tools 用于文件监控
RUN apk add --no-cache tini curl openssh inotify-tools maven bash && \
    addgroup --gid 2000 runner && \
    adduser --disabled-password -u 2000 -G runner runner

# 复制arthas
COPY --from=hengyunabc/arthas:latest /opt/arthas /opt/arthas

# 复制OpenTelemetry agent
ADD https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar /app/opentelemetry-javaagent.jar
RUN chmod +r /app/opentelemetry-javaagent.jar

# 创建工作目录
WORKDIR /app

# 复制项目源码和pom文件
COPY ../pom.xml /app/
COPY ../d42paas_common /app/d42paas_common/
COPY ../d42paas_manager /app/d42paas_manager/
COPY ../d42paas_demo /app/d42paas_demo/
COPY ../d42paas_admin /app/d42paas_admin/

# 下载依赖
RUN mvn dependency:go-offline -f /app/pom.xml

# 复制热重载脚本
COPY d42paas_manager/scripts/dev-hotreload.sh /app/dev-hotreload.sh
RUN chmod +x /app/dev-hotreload.sh

# 设置权限
RUN chown -R runner:runner /app

# 设置启动用户为runner
USER runner

# 设置Redis连接地址
ENV SPRING_REDIS_HOST=host.docker.internal

# 开发环境启动命令 - 使用热重载脚本
ENTRYPOINT ["/sbin/tini", "--", "/bin/sh", "/app/dev-hotreload.sh"]
