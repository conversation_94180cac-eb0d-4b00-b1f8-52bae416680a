#!/bin/bash

echo "🚀 启动 Manager 服务（开发模式）..."

# 等待依赖服务
echo "⏳ 等待依赖服务启动..."
while ! nc -z mysql 3306; do
  sleep 1
done
echo "✅ MySQL 已启动"

while ! nc -z redis 6379; do
  sleep 1
done
echo "✅ Redis 已启动"

while ! nc -z rabbitmq 5672; do
  sleep 1
done
echo "✅ RabbitMQ 已启动"

# 创建必要的目录
echo "📁 确保目录存在..."
mkdir -p /workspace/codeZone/data/@meta
mkdir -p /workspace/environment
mkdir -p /workspace/codeZoneSnapshot
mkdir -p /workspace/dockerContainer
mkdir -p /workspace/d42paas_agent

# 设置权限
chmod -R 755 /workspace/codeZone
chmod -R 755 /workspace/environment
chmod -R 755 /workspace/codeZoneSnapshot
chmod -R 755 /workspace/dockerContainer

echo "🎯 启动 Manager 服务..."
cd /workspace/d42paas_manager || exit 1

# 使用 spring-boot:run 支持热重载
mvn spring-boot:run \
  -Dspring-boot.run.profiles=devcontainer \
  -Dspring-boot.run.fork=false \
  -Dspring-boot.run.jvmArguments="-Xmx1g -XX:+UseG1GC"

echo "🛑 Manager 服务已停止"
