#!/bin/bash

echo "🚀 设置 Clacky AI PaaS 开发环境..."

# 安装必要的工具
echo "📦 安装开发工具..."
apt-get update -qq
apt-get install -y -qq netcat-openbsd git curl wget unzip

# 创建必要的目录
echo "📁 创建项目目录..."
mkdir -p /workspace/codeZone/data/@meta
mkdir -p /workspace/environment
mkdir -p /workspace/codeZoneSnapshot
mkdir -p /workspace/dockerContainer
mkdir -p /workspace/d42paas_agent

# 设置权限
chmod -R 755 /workspace/codeZone
chmod -R 755 /workspace/environment
chmod -R 755 /workspace/codeZoneSnapshot
chmod -R 755 /workspace/dockerContainer

# 预下载 Maven 依赖（仅下载，不编译）
echo "📥 预下载 Maven 依赖..."
cd /workspace
mvn dependency:go-offline -q

echo "✅ 开发环境设置完成！"
echo ""
echo "🎯 快速启动命令："
echo "  启动所有服务: bash .devcontainer/start-services.sh"
echo "  仅启动 Manager: cd d42paas_manager && mvn spring-boot:run -Dspring-boot.run.profiles=devcontainer"
echo "  仅启动 Demo: cd d42paas_demo && mvn spring-boot:run -Dspring-boot.run.profiles=devcontainer"
echo ""
echo "🔧 开发提示："
echo "  - 修改代码后，Maven 会自动重新编译"
echo "  - 使用 mvn spring-boot:run 可以实现热重载"
echo "  - 依赖已缓存，后续启动会更快"
